# UserController 并发控制优化方案

## 问题描述

在 `UserController.findAllBaseGroupMyUsername()` 方法中，当 `userMap` 为空时会调用 `findUserBaseMap()` 方法进行缓存更新。在高并发场景下，可能出现以下问题：

1. **频繁更新**：多个请求同时发现缓存为空，导致多次执行数据库查询和缓存更新
2. **资源浪费**：重复的数据库查询和缓存操作消耗系统资源
3. **性能影响**：频繁的同步操作可能影响接口响应时间

## 解决方案

### 1. 核心思路

参考项目中 `AgentStoryController` 的实现模式，引入以下机制：

- **冷却时间控制**：限制缓存更新的最小间隔时间
- **并发控制**：使用原子操作防止重复执行
- **异步更新**：将缓存更新操作异步化，避免阻塞主流程

### 2. 实现细节

#### 2.1 添加并发控制变量

```java
// 缓存更新控制相关常量
private static final long CACHE_UPDATE_INTERVAL = 30000; // 30秒间隔

// 缓存更新状态控制
private final AtomicBoolean isUpdatingUserCache = new AtomicBoolean(false);
private final AtomicLong lastUserCacheUpdateTime = new AtomicLong(0);
```

#### 2.2 核心方法重构

**原方法问题**：
```java
// 原来的实现 - 存在并发问题
if (userMap == null || userMap.isEmpty()) {
    findUserBaseMap(); // 同步执行，可能重复调用
}
```

**优化后的实现**：
```java
// 优化后的实现 - 异步更新，避免重复
if (userMap == null || userMap.isEmpty()) {
    tryUpdateUserCacheAsync(); // 异步更新，带并发控制
}
```

#### 2.3 新增方法说明

1. **`findUserBaseMapSync()`**：同步方式查询并更新缓存，用于需要立即返回结果的场景
2. **`findUserBaseMapAsync()`**：异步方式更新缓存，带异常处理
3. **`tryUpdateUserCacheAsync()`**：带并发控制的异步更新入口

### 3. 并发控制机制

#### 3.1 冷却时间检查

```java
if (currentTime - lastUpdate < CACHE_UPDATE_INTERVAL) {
    log.debug("用户缓存距离上次更新时间不足指定间隔，跳过本次更新");
    return;
}
```

#### 3.2 原子操作防重复

```java
if (!isUpdatingUserCache.compareAndSet(false, true)) {
    log.debug("用户缓存更新任务已经在执行，跳过本次更新");
    return;
}
```

#### 3.3 异步执行

```java
CompletableFuture.runAsync(() -> {
    try {
        findUserBaseMapAsync();
        lastUserCacheUpdateTime.set(System.currentTimeMillis());
    } finally {
        isUpdatingUserCache.set(false);
    }
}, ForkJoinPool.commonPool());
```

## 优化效果

### 1. 性能提升

- **减少数据库查询**：通过并发控制，避免重复的数据库查询
- **提高响应速度**：异步更新不阻塞主流程
- **降低系统负载**：冷却时间机制防止频繁更新

### 2. 稳定性增强

- **防止缓存雪崩**：避免大量并发请求同时更新缓存
- **异常隔离**：异步更新的异常不影响主业务流程
- **资源保护**：限制并发更新任务数量

### 3. 可维护性

- **代码结构清晰**：职责分离，同步和异步方法分开
- **日志完善**：详细的日志记录便于问题排查
- **向后兼容**：保留原方法作为 `@Deprecated`，确保兼容性

## 配置参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `CACHE_UPDATE_INTERVAL` | 30000ms | 缓存更新最小间隔时间 |

## 使用建议

1. **监控日志**：关注缓存更新相关的日志，了解更新频率
2. **调整间隔**：根据业务需求调整 `CACHE_UPDATE_INTERVAL` 参数
3. **性能测试**：在高并发场景下验证优化效果

## 相关文件

- `UserController.java`：主要修改文件
- `ConcurrencyControlDemo.java`：并发控制演示程序

## 注意事项

1. 异步更新可能导致短暂的数据不一致，但会在下次更新时修正
2. 如果对数据实时性要求极高，可以考虑使用同步方法 `findUserBaseMapSync()`
3. 建议在生产环境部署前进行充分的性能测试
