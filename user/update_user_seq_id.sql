-- 用户相关表的序列更新
SELECT set_sequence_next_value('a_user', 'a_user_id_seq');
SELECT set_sequence_next_value('a_token', 'a_token_id_seq');
SELECT set_sequence_next_value('a_role', 'a_role_id_seq');
SELECT set_sequence_next_value('a_user_role', 'a_user_role_id_seq');
SELECT set_sequence_next_value('a_button_permission', 'a_button_permission_id_seq');
SELECT set_sequence_next_value('a_menu', 'a_menu_id_seq');
SELECT set_sequence_next_value('a_button', 'a_button_id_seq');