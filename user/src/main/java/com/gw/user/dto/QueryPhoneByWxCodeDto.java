package com.gw.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "查询手机号通过微信Code")
public class QueryPhoneByWxCodeDto {
    @NotBlank(message = "code不能为空")
    @Schema(description = "微信登录code")
    private String code;
    @Schema(description = "用户信息加密数据，非必须")
    private String encryptedData;

    @Schema(description = "加密算法的初始向量，非必须")
    private String iv;
}
