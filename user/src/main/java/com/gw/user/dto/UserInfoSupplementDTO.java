package com.gw.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class UserInfoSupplementDTO {
    @Schema(description = "用户id")
    private Long id;
    @Schema(description = "用户昵称")
    private String nickname;
    @Schema(description = "性别：1-男，2-女，3-其他")
    private Integer gender;
    @Schema(description = "用户头像")
    private String avatar;
    @Schema(description = "用户地址")
    private String address;
    @Schema(description = "用户简介")
    private String introduce;
    private String phone;
}
