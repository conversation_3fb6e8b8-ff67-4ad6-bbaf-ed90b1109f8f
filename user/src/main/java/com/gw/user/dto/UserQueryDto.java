package com.gw.user.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Schema(description = "用户查询条件")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserQueryDto {
    @Schema(description = "模糊查询")
    private String queryValue = "";
    @Schema(description = "角色ID 空表示全部")
    private List<Long> roleIds;
    private String phone;
    @JsonIgnore
    private Integer isShow = -1;
    @JsonIgnore
    private String roleCode;
}
