package com.gw.user.controller;

import com.gw.user.service.UserActiveService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户活跃统计控制器
 */
@Tag(name = "用户活跃统计", description = "用户活跃记录和统计相关接口")
@RestController
@RequestMapping("/api/user-active")
@RequiredArgsConstructor
public class UserActiveController {

    private final UserActiveService userActiveService;

    @Operation(summary = "记录用户活跃", description = "记录用户当日活跃，如果已存在则不重复记录")
    @PostMapping("/record/{username}")
    public ResponseEntity<Map<String, Object>> recordUserActive(
            @Parameter(description = "用户名") @PathVariable String username) {
        
        boolean success = userActiveService.recordUserActive(username);
        Map<String, Object> result = new HashMap<>();
        result.put("success", success);
        result.put("message", success ? "记录成功" : "今日已记录过活跃");
        
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "记录指定日期用户活跃", description = "记录用户在指定日期的活跃")
    @PostMapping("/record/{username}/{date}")
    public ResponseEntity<Map<String, Object>> recordUserActiveWithDate(
            @Parameter(description = "用户名") @PathVariable String username,
            @Parameter(description = "活跃日期，格式：yyyy-MM-dd") 
            @PathVariable @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date) {
        
        boolean success = userActiveService.recordUserActive(username, date);
        Map<String, Object> result = new HashMap<>();
        result.put("success", success);
        result.put("message", success ? "记录成功" : "该日期已记录过活跃");
        
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "获取今日活跃用户数", description = "统计今日活跃用户总数")
    @GetMapping("/count/today")
    public ResponseEntity<Map<String, Object>> getTodayActiveUsersCount() {
        int count = userActiveService.countTodayActiveUsers();
        Map<String, Object> result = new HashMap<>();
        result.put("date", LocalDate.now());
        result.put("activeUsersCount", count);
        
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "获取指定日期活跃用户数", description = "统计指定日期的活跃用户总数")
    @GetMapping("/count/date/{date}")
    public ResponseEntity<Map<String, Object>> getActiveUsersCountByDate(
            @Parameter(description = "日期，格式：yyyy-MM-dd") 
            @PathVariable @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date) {
        
        int count = userActiveService.countActiveUsersByDate(date);
        Map<String, Object> result = new HashMap<>();
        result.put("date", date);
        result.put("activeUsersCount", count);
        
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "获取当月活跃用户数", description = "统计当月活跃用户总数")
    @GetMapping("/count/current-month")
    public ResponseEntity<Map<String, Object>> getCurrentMonthActiveUsersCount() {
        int count = userActiveService.countCurrentMonthActiveUsers();
        LocalDate now = LocalDate.now();
        Map<String, Object> result = new HashMap<>();
        result.put("year", now.getYear());
        result.put("month", now.getMonthValue());
        result.put("activeUsersCount", count);
        
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "获取指定月份活跃用户数", description = "统计指定年月的活跃用户总数")
    @GetMapping("/count/month/{year}/{month}")
    public ResponseEntity<Map<String, Object>> getActiveUsersCountByMonth(
            @Parameter(description = "年份") @PathVariable int year,
            @Parameter(description = "月份") @PathVariable int month) {
        
        int count = userActiveService.countActiveUsersByMonth(year, month);
        Map<String, Object> result = new HashMap<>();
        result.put("year", year);
        result.put("month", month);
        result.put("activeUsersCount", count);
        
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "获取当年活跃用户数", description = "统计当年活跃用户总数")
    @GetMapping("/count/current-year")
    public ResponseEntity<Map<String, Object>> getCurrentYearActiveUsersCount() {
        int count = userActiveService.countCurrentYearActiveUsers();
        Map<String, Object> result = new HashMap<>();
        result.put("year", LocalDate.now().getYear());
        result.put("activeUsersCount", count);
        
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "获取指定年份活跃用户数", description = "统计指定年份的活跃用户总数")
    @GetMapping("/count/year/{year}")
    public ResponseEntity<Map<String, Object>> getActiveUsersCountByYear(
            @Parameter(description = "年份") @PathVariable int year) {
        
        int count = userActiveService.countActiveUsersByYear(year);
        Map<String, Object> result = new HashMap<>();
        result.put("year", year);
        result.put("activeUsersCount", count);
        
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "获取过去30天每日活跃用户数", description = "统计过去30天每天的活跃用户数")
    @GetMapping("/count/past-30-days")
    public ResponseEntity<List<Map<String, Object>>> getPast30DaysActiveUsersCount() {
        List<Map<String, Object>> result = userActiveService.countActiveUsersInPast30Days();
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "获取过去指定天数每日活跃用户数", description = "统计过去指定天数每天的活跃用户数")
    @GetMapping("/count/past-days/{days}")
    public ResponseEntity<List<Map<String, Object>>> getPastDaysActiveUsersCount(
            @Parameter(description = "天数") @PathVariable int days) {
        
        List<Map<String, Object>> result = userActiveService.countActiveUsersInPastDays(days);
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "获取日期范围内每日活跃用户数", description = "统计指定日期范围内每天的活跃用户数")
    @GetMapping("/count/date-range")
    public ResponseEntity<List<Map<String, Object>>> getActiveUsersCountByDateRange(
            @Parameter(description = "开始日期，格式：yyyy-MM-dd") 
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @Parameter(description = "结束日期，格式：yyyy-MM-dd") 
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        
        List<Map<String, Object>> result = userActiveService.countActiveUsersByDateRange(startDate, endDate);
        return ResponseEntity.ok(result);
    }
}
