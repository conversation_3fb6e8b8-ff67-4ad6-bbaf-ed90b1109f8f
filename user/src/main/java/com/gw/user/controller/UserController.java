package com.gw.user.controller;

import com.github.pagehelper.PageInfo;
import com.gw.common.agent.service.AgentProxyService;
import com.gw.common.agent.vo.UserInteractionAgentVO;
import com.gw.common.dto.ItemIdDTO;
import com.gw.common.dto.PageBaseRequest;
import com.gw.common.dto.ResponseResult;
import com.gw.common.dto.TimeRangeDTO;
import com.gw.common.exception.BusinessException;
import com.gw.common.exception.BusinessExceptionCode;
import com.gw.common.membership.constant.MemberCacheConstant;
import com.gw.common.membership.service.MembershipProxyService;
import com.gw.common.membership.vo.InvitationCodeVO;
import com.gw.common.membership.vo.UserInviteStatVO;
import com.gw.common.membership.vo.UserMembershipVO;
import com.gw.common.user.constant.UserCommonCacheConstant;
import com.gw.common.user.context.UserContextUtil;
import com.gw.common.user.dto.QueryFromUsernameDTO;
import com.gw.common.user.service.UserProxyService;
import com.gw.common.user.vo.UserBaseContentVo;
import com.gw.common.user.vo.UserStatisticsVO;
import com.gw.common.util.UploadFileUtil;
import com.gw.common.vo.ItemIdVo;
import com.gw.common.vo.PageBaseContentVo;
import com.gw.common.vo.PaginationVo;
import com.gw.common.vo.UploadFileVo;
import com.gw.user.config.CacheProperties;
import com.gw.user.constant.UserConstant;
import com.gw.user.dto.*;
import com.gw.user.entity.RoleEntity;
import com.gw.user.entity.UserEntity;
import com.gw.user.mapper.ModelMapperConvert;
import com.gw.user.service.RoleService;
import com.gw.user.service.UserService;
import com.gw.user.vo.QueryPhoneByWxCodeVo;
import com.gw.user.vo.UserDetailVo1;
import com.gw.user.vo.UserGroupVo;
import com.gw.user.vo.UserVo;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.modelmapper.ModelMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.gw.common.exception.BusinessExceptionCode.FAIL_CODE;
import static com.gw.common.exception.BusinessExceptionCode.UNAUTHORIZED_CODE;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/auth/users")
@Tag(name = "用户模块", description = "用户相关操作")
@Log4j2
public class UserController {
    public static final String USER_UPLOAD_PATH = "upload/file/user/";
    public static final int USER_UPLOAD_MAX_SIZE = 100 * 1024 * 1024;
    public static final List<String> USER_ALLOWED_EXTENSIONS = Arrays.asList("png", "jpg", "jpeg");

    // 缓存更新控制相关常量
    private static final long CACHE_UPDATE_INTERVAL = 30000; // 30秒间隔

    // 缓存更新状态控制
    private final AtomicBoolean isUpdatingUserCache = new AtomicBoolean(false);
    private final AtomicLong lastUserCacheUpdateTime = new AtomicLong(0);

    private final UserService userService;
    private final RoleService roleService;
    private final PasswordEncoder passwordEncoder;
    private final MembershipProxyService membershipProxyService;
    private final RedisTemplate<String, Object> redisTemplate;
    private final CacheProperties cacheProperties;
    private final UserProxyService userProxyService;
    private final AgentProxyService agentProxyService;

    private void fillEntity(UserEntity entity, UserSubmitBaseDto params) {
        List<RoleEntity> roles = params.getRoles().stream().map(roleService::findById).toList();
        entity.setRoles(roles);
        UserEntity userDetails = getCurrentAuthenticatedUser();
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdater(userDetails.getRealName());
        BeanUtils.copyProperties(params, entity);
    }
    @Operation(summary = "通过微信接口获取手机号", responses = {
            @ApiResponse(responseCode = "200", description = "通过微信接口获取手机号")
    })
    @PostMapping("/wx/query_by_wx")
    public ResponseResult<QueryPhoneByWxCodeVo> queryPhoneByWxCode(@RequestBody @Valid QueryPhoneByWxCodeDto params) {
        String phone = userService.QueryPhoneByWx(params.getCode());

        return ResponseResult.success(new QueryPhoneByWxCodeVo(phone));
    }
    @Operation(summary = "创建用户", responses = {
            @ApiResponse(responseCode = "200", description = "成功创建用户")
    })
    @PostMapping("")
    public ResponseResult<ItemIdVo> createUser(@RequestBody @Valid UserSubmitDto params) {
        if (userService.checkUsernameExists(params.getUsername())) {
            return ResponseResult.failure(BusinessExceptionCode.FAIL_CODE.getCode(), "用户名已存在");
        }
        var password = passwordEncoder.encode(params.getPassword());
        UserEntity entity = new UserEntity();
        fillEntity(entity, params);
        entity.setPassword(password);
        UserEntity userDetails = getCurrentAuthenticatedUser();
        entity.setCreator(userDetails.getRealName());

        long id = userService.creatUser(entity);

        // 需要重新获取完整用户信息用于缓存
        UserEntity savedEntity = userService.findUserById(id);
        updateUserInCache(savedEntity);

        return ResponseResult.success(new ItemIdVo(id));
    }

    @PostMapping("update")
    @Operation(summary = "更新用户", responses = {
            @ApiResponse(responseCode = "200", description = "成功更新用户")
    })
    public ResponseResult<ItemIdVo> updateUser(@RequestBody @Valid UserUpdateDto params) {
        UserEntity entity = userService.findUserById(params.getId());

        // 填充更新数据
        fillEntity(entity, params);

        userService.updateUser(entity);

        // 更新缓存
        updateUserInCache(entity);

        return ResponseResult.success(new ItemIdVo(entity.getId()));
    }
    @PostMapping("update_phone")
    @Operation(summary = "更新我的手机号", responses = {
            @ApiResponse(responseCode = "200", description = "成功更新我的手机号")
    })
    public ResponseResult<?> updateUserPhone(@RequestBody @Valid UpdateUserPhoneDTO params) {
        UserEntity userDetails = getCurrentAuthenticatedUser();


        userDetails.setPhone(params.getPhone());

        userService.updateUserPhone(userDetails);

        // 更新缓存
        updateUserInCache(userDetails);

        return ResponseResult.success(null);
    }
    @GetMapping("/my/get")
    @Operation(summary = "获得自己的个人信息", responses = {
            @ApiResponse(responseCode = "200", description = "成功获得自己的个人信息")
    })
    public ResponseResult<UserDetailVo1> getMyInfo() {
        UserEntity userDetails = getCurrentAuthenticatedUser();
        UserEntity entity = userService.findUserById(userDetails.getId());
        var modelMapper = ModelMapperConvert.getUserModelMapper();

        UserDetailVo1 userVo = modelMapper.map(entity, UserDetailVo1.class);

        // 获取会员信息
        fillUserMembershipInfo(userVo, userDetails.getUsername());

        // 获取用户互动信息
        UserInteractionAgentVO vo = agentProxyService.getUserInteraction(userDetails.getUsername());
        userVo.setCommentCnt(vo.getCommentCnt());
        userVo.setLikeCnt(vo.getLikeCnt());
        userVo.setCreatCnt(vo.getCreatCnt());
        userVo.setFavoriteCnt(vo.getFavoriteCnt());

        return ResponseResult.success(userVo);
    }

    /**
     * 填充用户会员信息
     *
     * @param userVo   用户视图对象
     * @param username 用户名
     */
    private void fillUserMembershipInfo(UserDetailVo1 userVo, String username) {
        int vipLevel = 0;
        int vipRemainDays = 0;
        LocalDateTime vipExpireTime = null;
        String inviteCode = "";
        String cacheName = cacheProperties.getCacheName(MemberCacheConstant.MEMBERSHIP_BASE_CACHE_KEY + ":" + username);
        try {
            UserMembershipVO vo = membershipProxyService.getMembershipByUsername(cacheName, username);
            if (vo != null) {
                vipLevel = vo.getVipLevel();
                vipRemainDays = vo.getRemainingDays();
                vipExpireTime = vo.getExpireTime();
                if (vipExpireTime != null) {
                    log.info("vipExpireTime {}", vipExpireTime.toString());
                }
            }

            InvitationCodeVO codeVo = membershipProxyService.getInvitationCodeByUsername(username);
            if (codeVo != null) {
                inviteCode = codeVo.getCode();
            }
        } catch (Exception ex) {
            log.error("获取会员信息异常{}", ex.getMessage());
        }

        userVo.setVipLevel(vipLevel);
        userVo.setVipRemainDays(vipRemainDays);
        userVo.setVipExpireTime(vipExpireTime);
        userVo.setInviteCode(inviteCode);
    }

    @PostMapping("/my/supplement")
    @Operation(summary = "完善个人信息", responses = {
            @ApiResponse(responseCode = "200", description = "完善个人信息")
    })
    public ResponseResult<ItemIdVo> supplementUser(@RequestBody @Valid UserInfoSupplementDTO params) {
        UserEntity userDetails = getCurrentAuthenticatedUser();
        UserEntity entity = userService.findUserById(params.getId());

        if (!userDetails.getId().equals(entity.getId())) {
            throw new BusinessException(FAIL_CODE.getCode(), "只能修改自己的信息");
        }

        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdater(UserContextUtil.getCurrentUsername());
        // 填充更新数据
        entity.setAvatar(params.getAvatar());
        entity.setNickname(params.getNickname());
        entity.setAddress(params.getAddress());
        entity.setIntroduce(params.getIntroduce());
        entity.setGender(params.getGender());
        entity.setPhone(params.getPhone());
        userService.updateUser(entity);

        // 更新缓存
        updateUserInCache(entity);

        return ResponseResult.success(new ItemIdVo(entity.getId()));
    }

    @PostMapping("delete")
    @Operation(summary = "删除用户", responses = {
            @ApiResponse(responseCode = "200", description = "成功删除用户")
    })
    public ResponseResult<ItemIdVo> deleteUser(@RequestBody ItemIdDTO params) {
        UserEntity entity = userService.findUserById(params.getId());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdater(UserContextUtil.getCurrentUsername());

        // 保存用户名以便从缓存中删除
        String username = entity.getUsername();

        userService.deleteUserById(entity);

        // 从缓存中删除
        removeUserFromCache(username);

        return ResponseResult.success(new ItemIdVo(params.getId()));
    }

    @PostMapping("change_password")
    @Operation(summary = "修改密码", responses = {
            @ApiResponse(responseCode = "200", description = "成功修改密码")
    })
    public ResponseResult<?> changePassword(@RequestBody @Valid ChangePasswordDto params) {
        UserEntity entity = userService.findUserById(params.getId());
        var newPassword = passwordEncoder.encode(params.getNewPassword());
        userService.changePasswordByUserId(newPassword, entity.getId());
        return ResponseResult.success(null);
    }

    private UserEntity getCurrentAuthenticatedUser() {
        try {
            return Optional.ofNullable((UserEntity) SecurityContextHolder.getContext()
                            .getAuthentication().getPrincipal())
                    .orElseThrow(() -> new BusinessException(UNAUTHORIZED_CODE.getCode(), "用户未认证"));
        } catch (Exception e) {
            log.error("用户认证信息获取失败", e);
            throw new BusinessException(UNAUTHORIZED_CODE.getCode(), "用户未认证");
        }
    }

    @PostMapping("change_self_password")
    @Operation(summary = "修改自己的密码")
    public ResponseResult<?> changeSelfPassword(@RequestBody @Valid ChangeSelfPasswordDto params) {
        UserEntity currentUser = getCurrentAuthenticatedUser();

        if (!passwordEncoder.matches(params.getOldPassword(), currentUser.getPassword())) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "旧密码不正确");
        }

        String newPassword = passwordEncoder.encode(params.getNewPassword());
        userService.changePasswordByUserId(newPassword, currentUser.getId());

        log.info("用户[{}]修改密码成功", currentUser.getUsername());
        return ResponseResult.success(null);
    }

    /**
     * 更新用户状态的通用方法
     *
     * @param userId 用户ID
     * @param status 目标状态
     * @return 用户ID
     */
    private ItemIdVo updateUserStatus(Long userId, int status) {
        UserEntity entity = userService.findUserById(userId);

        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdater(UserContextUtil.getCurrentUsername());
        entity.setStatus(status);

        userService.updateUser(entity);
        return new ItemIdVo(entity.getId());
    }

    @PostMapping("enable")
    @Operation(summary = "使能用户", responses = {
            @ApiResponse(responseCode = "200", description = "成功使能用户")
    })
    public ResponseResult<ItemIdVo> enableUser(@RequestBody ItemIdDTO params) {
        return ResponseResult.success(updateUserStatus(params.getId(), UserConstant.ENABLE_USER));
    }

    @PostMapping("disable")
    @Operation(summary = "禁用用户", responses = {
            @ApiResponse(responseCode = "200", description = "成功禁用用户")
    })
    public ResponseResult<ItemIdVo> disableUser(@RequestBody ItemIdDTO params) {
        return ResponseResult.success(updateUserStatus(params.getId(), UserConstant.DISABLE_USER));
    }

    @PostMapping("get")
    @Operation(summary = "查询用户", responses = {
            @ApiResponse(responseCode = "200", description = "成功查询用户")
    })
    public ResponseResult<UserVo> getUser(@RequestBody ItemIdDTO params) {
        UserEntity entity = userService.findUserById(params.getId());

        return ResponseResult.success(convertToVo(entity));
    }

    /**
     * 同步方式查询用户基础信息并更新缓存
     * 用于需要立即返回结果的场景
     */
    private Map<String, UserBaseContentVo> findUserBaseMapSync() {
        String cacheKey = cacheProperties.getCacheName(UserCommonCacheConstant.USER_BASE_MAP_CACHE_KEY);

        List<UserEntity> newEntities = userService.findAll();
        ModelMapper modelMapper = ModelMapperConvert.getBaseModelMapper();

        Map<String, UserBaseContentVo> newMaps = new HashMap<>();

        // First build the result map
        for (UserEntity entity : newEntities) {
            UserBaseContentVo vo = modelMapper.map(entity, UserBaseContentVo.class);
            newMaps.put(entity.getUsername(), vo);
        }

        // 更新缓存
        try {
            userProxyService.putUserMapToCache(cacheKey, newMaps, cacheProperties.getCacheExpiration(UserCommonCacheConstant.USER_CATEGORY_KEY));
        } catch (Exception e) {
            log.error("更新用户缓存失败: {}", e.getMessage(), e);
        }

        return newMaps;
    }

    /**
     * 异步方式更新用户基础信息缓存
     * 带有并发控制，避免频繁更新
     */
    private void findUserBaseMapAsync() {
        String cacheKey = cacheProperties.getCacheName(UserCommonCacheConstant.USER_BASE_MAP_CACHE_KEY);

        try {
            List<UserEntity> newEntities = userService.findAll();
            ModelMapper modelMapper = ModelMapperConvert.getBaseModelMapper();

            Map<String, UserBaseContentVo> newMaps = new HashMap<>();

            // First build the result map
            for (UserEntity entity : newEntities) {
                UserBaseContentVo vo = modelMapper.map(entity, UserBaseContentVo.class);
                newMaps.put(entity.getUsername(), vo);
            }

            // 更新缓存
            userProxyService.putUserMapToCache(cacheKey, newMaps, cacheProperties.getCacheExpiration(UserCommonCacheConstant.USER_CATEGORY_KEY));
            log.info("用户基础信息缓存异步更新完成，共更新 {} 条记录", newMaps.size());

        } catch (Exception e) {
            log.error("异步更新用户基础信息缓存失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 尝试异步更新用户缓存，带有冷却时间检查和并发控制
     */
    private void tryUpdateUserCacheAsync() {
        long currentTime = System.currentTimeMillis();
        long lastUpdate = lastUserCacheUpdateTime.get();

        // 如果距离上次更新时间不足指定间隔，则跳过
        if (currentTime - lastUpdate < CACHE_UPDATE_INTERVAL) {
            log.debug("用户缓存距离上次更新时间不足指定间隔，跳过本次更新");
            return;
        }

        // 如果已经有任务在执行，则跳过
        if (!isUpdatingUserCache.compareAndSet(false, true)) {
            log.debug("用户缓存更新任务已经在执行，跳过本次更新");
            return;
        }

        // 使用异步任务执行缓存更新
        CompletableFuture.runAsync(() -> {
            try {
                findUserBaseMapAsync();
                // 更新最后更新时间
                lastUserCacheUpdateTime.set(System.currentTimeMillis());
            } catch (Exception e) {
                log.error("异步更新用户缓存失败", e);
            } finally {
                // 释放执行标志
                isUpdatingUserCache.set(false);
            }
        }, ForkJoinPool.commonPool());
    }

    /**
     * @deprecated 使用 findUserBaseMapSync() 替代
     */
    @Deprecated
    private Map<String, UserBaseContentVo> findUserBaseMap() {
        return findUserBaseMapSync();
    }

    @PostMapping("get_from_username")
    @Operation(summary = "根据用户名查询用户", responses = {
            @ApiResponse(responseCode = "200", description = "成功查询用户")
    })
    @Hidden
    public ResponseResult<UserBaseContentVo> getUserFromUsername(@RequestBody QueryFromUsernameDTO params) {
        if (params == null || params.getUsername() == null || params.getUsername().isEmpty()) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "用户名不能为空");
        }

        String cacheKey = cacheProperties.getCacheName(UserCommonCacheConstant.USER_BASE_MAP_CACHE_KEY);

        String username = params.getUsername();

        // 先从Redis缓存中查询
        UserBaseContentVo vo = userProxyService.findByUsernameFromCache(cacheKey, username);

        if (vo != null) {
            // 缓存命中，直接返回
            return ResponseResult.success(vo);
        }

        // 尝试异步更新缓存
        tryUpdateUserCacheAsync();
        // 缓存未命中，从数据库查询
        try {
            UserEntity entity = userService.findUserByUsername(username);
            if (entity == null) {
                return ResponseResult.failure(FAIL_CODE.getCode(), "用户不存在");
            }

            // 将实体转换为VO
            ModelMapper modelMapper = ModelMapperConvert.getUserModelMapper();
            vo = modelMapper.map(entity, UserBaseContentVo.class);
            userProxyService.updateToCacheIfCacheExist(cacheKey, vo.getUsername(), vo, cacheProperties.getCacheExpiration(UserCommonCacheConstant.USER_CATEGORY_KEY));

            return ResponseResult.success(vo);
        } catch (Exception e) {
            log.error("根据用户名查询用户失败: {}", e.getMessage(), e);
            return ResponseResult.failure(FAIL_CODE.getCode(), "查询用户失败");
        }
    }

    @GetMapping("get_from_token")
    @Operation(summary = "根据token查询用户", responses = {
            @ApiResponse(responseCode = "200", description = "成功查询用户")
    })
    public ResponseResult<UserVo> getFromToken() {
        UserEntity userDetails = getCurrentAuthenticatedUser();
        UserEntity entity = userService.findUserById(userDetails.getId());
        return ResponseResult.success(convertToVo(entity));
    }

    @Operation(summary = "上传图片", responses = {
            @ApiResponse(responseCode = "200", description = "成功上传图片")
    })
    @PostMapping(value = "file/upload", consumes = "multipart/form-data")
    public ResponseResult<UploadFileVo> fileUpload(@RequestParam("file") MultipartFile file) {
        if (file == null) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "文件不能为空");
        }
        String filePath = UploadFileUtil.handleFileUpload(file, USER_ALLOWED_EXTENSIONS, USER_UPLOAD_MAX_SIZE, null,
                USER_UPLOAD_PATH);
        return ResponseResult.success(new UploadFileVo(filePath));
    }

    private void setNonRootUserFilter(UserQueryDto filter, UserEntity currentUser) {
        if (filter.getRoleIds() == null || filter.getRoleIds().isEmpty()) {
            List<RoleEntity> roles = roleService.findAll();
            List<RoleEntity> userRoles = currentUser.getRoles();
            List<Long> roleIds = roles.stream()
                    .map(RoleEntity::getId)
                    .filter(id -> userRoles.stream()
                            .anyMatch(userRole -> userRole.getId().equals(id)))
                    .toList();
            filter.setRoleIds(roleIds);
        }
    }


    @PostMapping("page")
    @Operation(summary = "用户分页查询")
    public ResponseResult<PageBaseContentVo<UserVo>> page(
            @RequestBody @Valid PageBaseRequest<UserQueryDto> params) {
        UserEntity currentUser = getCurrentAuthenticatedUser();

        UserQueryDto filter = Optional.ofNullable(params.getFilter())
                .orElse(new UserQueryDto());
        if (currentUser.getIsRoot() == 0) {
            setNonRootUserFilter(filter, currentUser);
        }
        filter.setIsShow(1);
        PageInfo<UserEntity> pageInfo = userService.page(
                params.getCurrent(),
                params.getPageSize(),
                filter);

        PaginationVo pagination = new PaginationVo(
                pageInfo.getTotal(),
                pageInfo.getPageNum(),
                pageInfo.getPageSize());

        List<UserVo> rows = pageInfo.getTotal() > 0
                ? pageInfo.getList().stream()
                .map(entity -> ModelMapperConvert.getUserModelMapper().map(entity, UserVo.class))
                .toList()
                : new ArrayList<>();


        PageBaseContentVo<UserVo> content = new PageBaseContentVo<>();
        content.setList(rows);
        content.setPagination(pagination);

        return ResponseResult.success(content);
    }

    @PostMapping("app_page")
    @Operation(summary = "app用户分页查询")
    public ResponseResult<PageBaseContentVo<UserDetailVo1>> appPage(
            @RequestBody @Valid PageBaseRequest<UserQueryDto> params) {
        UserEntity currentUser = getCurrentAuthenticatedUser();

        UserQueryDto filter = Optional.ofNullable(params.getFilter())
                .orElse(new UserQueryDto());
        if (currentUser.getIsRoot() == 0) {
            setNonRootUserFilter(filter, currentUser);
        }
        filter.setRoleCode(UserConstant.APP_USER_ROLE_CODE);
        filter.setIsShow(1);
        PageInfo<UserEntity> pageInfo = userService.page(
                params.getCurrent(),
                params.getPageSize(),
                filter);
        List<String> usernames = pageInfo.getList().stream().map(UserEntity::getUsername).toList();
        Map<String, UserInviteStatVO> statMap = membershipProxyService.getInvitationCodeByUsername(usernames);
        ModelMapper modelMapper = ModelMapperConvert.getBaseModelMapper();
        List<UserDetailVo1> vos = pageInfo.getList().stream().map(entity -> {
            UserDetailVo1 userVo = modelMapper.map(entity, UserDetailVo1.class);
            UserMembershipVO vo = statMap.get(username);
            if (vo != null) {
                vipLevel = vo.getVipLevel();
                vipRemainDays = vo.getRemainingDays();
                vipExpireTime = vo.getExpireTime();
                if (vipExpireTime != null) {
                    log.info("vipExpireTime {}", vipExpireTime.toString());
                }
            }
            // 填充会员信息
            fillUserMembershipInfo(userVo, entity.getUsername());

            // 获取用户互动信息
            UserInteractionAgentVO vo = agentProxyService.getUserInteraction(entity.getUsername());
            userVo.setCommentCnt(vo.getCommentCnt());
            userVo.setLikeCnt(vo.getLikeCnt());
            userVo.setCreatCnt(vo.getCreatCnt());
            userVo.setFavoriteCnt(vo.getFavoriteCnt());

            return userVo;
        }).toList();

        return ResponseResult.success(new PageBaseContentVo<>(vos,
                new PaginationVo(pageInfo.getTotal(), params.getCurrent(), params.getPageSize())));
    }

    @PostMapping("all_app")
    @Operation(summary = "查询所有App用户用户", responses = {
            @ApiResponse(responseCode = "200", description = "成功")
    })
    public ResponseResult<List<UserVo>> findAllApp(@RequestBody @Valid UserAppQueryDto query) {
        UserQueryDto params = new UserQueryDto();
        params.setIsShow(1);
        params.setRoleCode(UserConstant.APP_USER_ROLE_CODE);
        if (query != null) {
            params.setQueryValue(query.getQueryValue());
        }

        List<UserEntity> rsp = userService.findAll(params);
        List<UserVo> vos = rsp.stream().map(entity -> ModelMapperConvert.getUserModelMapper().map(entity, UserVo.class)).toList();
        return ResponseResult.success(vos);
    }

    @PostMapping("all")
    @Operation(summary = "查询所有用户", responses = {
            @ApiResponse(responseCode = "200", description = "成功")
    })
    public ResponseResult<List<UserVo>> findAll(@RequestBody @Valid UserQueryDto params) {
        if (params == null) {
            params = new UserQueryDto();
        }
        List<UserEntity> rsp = userService.findAll(params);
        List<UserVo> vos = rsp.stream().map(entity -> ModelMapperConvert.getUserModelMapper().map(entity, UserVo.class)).toList();
        return ResponseResult.success(vos);
    }

    @GetMapping("/find_all_base")
    @Hidden
    public ResponseResult<Map<String, UserBaseContentVo>> findAllBaseGroupMyUsername() {
        String cacheKey = cacheProperties.getCacheName(UserCommonCacheConstant.USER_BASE_MAP_CACHE_KEY);
        Map<String, UserBaseContentVo> userMap = userProxyService.getUserMapFromCache(cacheKey);
        if (userMap != null && !userMap.isEmpty()) {
            return ResponseResult.success(userMap);
        }

        // 如果缓存为空，直接从数据库查询并返回（不等待异步更新完成）
        return ResponseResult.success(findUserBaseMapSync());
    }
    @PostMapping("/find_by_usernames")
    @Hidden
    public ResponseResult<Map<String, UserBaseContentVo>> findAllBaseGroupMyUsername(@RequestBody @Valid List<String> usernames) {
        if (usernames == null || usernames.isEmpty()) {
            return ResponseResult.success(Map.of());
        }

        Map<String, UserEntity> map = userService.findALlByUsernames(usernames);

        // Convert UserEntity map to UserBaseContentVo map
        ModelMapper modelMapper = ModelMapperConvert.getBaseModelMapper();
        Map<String, UserBaseContentVo> result = new HashMap<>();

        for (Map.Entry<String, UserEntity> entry : map.entrySet()) {
            UserBaseContentVo vo = modelMapper.map(entry.getValue(), UserBaseContentVo.class);
            result.put(entry.getKey(), vo);
        }
        String cacheKey = cacheProperties.getCacheName(UserCommonCacheConstant.USER_BASE_MAP_CACHE_KEY);
        Map<String, UserBaseContentVo> userMap = userProxyService.getUserMapFromCache(cacheKey);
        if (userMap == null || userMap.isEmpty()) {
            // 尝试异步更新缓存
            tryUpdateUserCacheAsync();
        }else{
            for (Map.Entry<String, UserBaseContentVo> entry : result.entrySet()) {
                UserBaseContentVo vo = entry.getValue();
                userProxyService.updateToCacheIfCacheExist(cacheKey, entry.getKey(), vo, cacheProperties.getCacheExpiration(UserCommonCacheConstant.USER_CATEGORY_KEY));
            }
        }
        return ResponseResult.success(result);
    }
    /**
     * 更新单个用户缓存
     *
     * @param entity 用户实体
     */
    private void updateUserInCache(UserEntity entity) {
        if (entity == null || entity.getUsername() == null) {
            return;
        }

        try {
            String cacheKey = cacheProperties.getCacheName(UserCommonCacheConstant.USER_BASE_MAP_CACHE_KEY);
            ModelMapper modelMapper = ModelMapperConvert.getBaseModelMapper();
            UserBaseContentVo vo = modelMapper.map(entity, UserBaseContentVo.class);
            userProxyService.updateToCacheIfCacheExist(cacheKey, entity.getUsername(), vo, cacheProperties.getCacheExpiration(UserCommonCacheConstant.USER_CATEGORY_KEY));


        } catch (Exception e) {
            // 捕获异常但不影响主业务流程
            log.error("更新用户缓存失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 删除单个用户缓存
     *
     * @param username 用户名
     */
    private void removeUserFromCache(String username) {
        if (username == null || username.isEmpty()) {
            return;
        }

        try {

            String cacheKey = cacheProperties.getCacheName(UserCommonCacheConstant.USER_BASE_MAP_CACHE_KEY);
            Boolean hasKey = redisTemplate.hasKey(cacheKey);
            if (!hasKey) {
                return;
            }

            // 从Redis hash中删除特定用户条目
            redisTemplate.opsForHash().delete(cacheKey, username);
        } catch (Exception e) {
            // 捕获异常但不影响主业务流程
            log.error("从缓存中删除用户失败: {}", e.getMessage(), e);
        }
    }

    @PostMapping("/grouped-by-pinyin")
    @Operation(summary = "查询用户按照拼音首字母排序", responses = {
            @ApiResponse(responseCode = "200", description = "成功")
    })
    public ResponseResult<List<UserGroupVo>> getAllUsersGroupedByPinyinFirstLetter(
            @RequestBody @Valid UserQueryDto params) {
        Map<String, List<UserEntity>> rsp = userService.getAllUsersSortedByPinyin(params);
        if (rsp == null || rsp.isEmpty()) {
            return ResponseResult.success(new ArrayList<>());
        }
        var modelMapper = ModelMapperConvert.getUserModelMapper();
        Map<String, List<UserVo>> usrMap = rsp.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey,
                entry -> entry.getValue().stream().map(entity -> modelMapper.map(entity, UserVo.class)).toList()));
        List<UserGroupVo> rows = usrMap.entrySet().stream()
                .map(entry -> new UserGroupVo(entry.getKey(), entry.getValue())).toList();
        return ResponseResult.success(rows);
    }

    @GetMapping("/user_statistics")
    @Operation(summary = "获取用户统计", responses = {
            @ApiResponse(responseCode = "200", description = "成功")
    })
    public ResponseResult<UserStatisticsVO> getUserStatistics() {
        // 获取今日时间范围
        LocalDateTime todayStart = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime todayEnd = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);


        // 获取今日新增用户数
        int todayNewUsers = userService.countTodayNewUsers(todayStart, todayEnd);

        // 获取总用户数
        int totalUsers = userService.countTotalUsers();

        // 构建统计数据
        UserStatisticsVO stats = new UserStatisticsVO();
        stats.setTodayNewUsers(todayNewUsers);
        stats.setTotalUsers(totalUsers);

        return ResponseResult.success(stats);
    }

    @PostMapping("/count_new_users")
    @Operation(summary = "统计指定时间范围内的新增用户数", responses = {
            @ApiResponse(responseCode = "200", description = "成功")
    })
    @Hidden
    public ResponseResult<Integer> countNewUsersByTimeRange(@RequestBody TimeRangeDTO req) {
        int count = userService.countTodayNewUsers(req.getStartTime(), req.getEndTime());
        return ResponseResult.success(count);
    }

    public UserVo convertToVo(UserEntity entity) {

        var modelMapper = ModelMapperConvert.getUserModelMapper();

        return modelMapper.map(entity, UserVo.class);
    }
}
