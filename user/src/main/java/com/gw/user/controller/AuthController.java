package com.gw.user.controller;

import com.gw.common.dto.ResponseResult;
import com.gw.common.user.context.UserContextUtil;
import com.gw.user.entity.UserEntity;
import com.gw.user.provider.JwtProvider;
import com.gw.user.service.TokenService;
import com.gw.user.service.UserService;
import com.gw.user.vo.AuthMenusVO;
import com.gw.user.vo.TokenInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/v1/auth")
@RequiredArgsConstructor
@Tag(name = "网关鉴权模块", description = "鉴权相关操作")
@Log4j2
public class AuthController {

    private final JwtProvider jwtProvider;
    private final UserService userService;
    private final TokenService tokenService;

    @Operation(summary = "验证Token", responses = {
            @ApiResponse(responseCode = "200", description = "验证成功")
    })
    @PostMapping("/validate")
    public ResponseResult<TokenInfo> validateToken(@RequestHeader("Authorization") String authorization) {
        try {
            if (authorization == null || !authorization.startsWith("Bearer ")) {
                return ResponseResult.success(TokenInfo.invalid());
            }

            String token = authorization.substring(7);
            String username = jwtProvider.extractUsername(token);

            if (username == null) {
                return ResponseResult.success(TokenInfo.invalid());
            }

            UserEntity user = userService.findUserByUsername(username);

            // 验证token是否在数据库中存在且有效
            boolean isTokenValid = tokenService.findByToken(token)
                    .map(t -> !t.isExpired() && !t.isRevoked())
                    .orElse(false);

            if (!jwtProvider.isTokenValid(token, user) || !isTokenValid) {
                return ResponseResult.success(TokenInfo.invalid());
            }
            userService.updateLastUseTime(user.getId());

            return ResponseResult.success(TokenInfo.builder()
                    .valid(true)
                    .userId(String.valueOf(user.getId()))
                    .username(user.getUsername())
                    .realName(user.getRealName())
                    .roles(user.getRoles().stream()
                            .flatMap(role -> role.getRoleMenus().stream())
                            .distinct()
                            .toList())
                    .permissions(user.getRoles().stream()
                            .flatMap(role -> role.getBtns().stream())
                            .distinct()
                            .toList())
                    .loginTime(System.currentTimeMillis())
                    .build());

        } catch (Exception e) {
            log.error("Token验证失败", e);
            return ResponseResult.success(TokenInfo.invalid());
        }
    }

    @Operation(summary = "检查权限", responses = {
            @ApiResponse(responseCode = "200", description = "检查成功")
    })
    @GetMapping("/check-permission")
    public ResponseResult<Boolean> checkPermission(
            @RequestParam String username,
            @RequestParam String path) {
        try {
            UserEntity user = userService.findUserByUsername(username);
            // 这里可以实现具体的权限检查逻辑
            // 当前实现为简单返回true，您可以根据需求扩展
            return ResponseResult.success(true);
        } catch (Exception e) {
            log.error("权限检查失败", e);
            return ResponseResult.success(false);
        }
    }

    @Operation(summary = "获取当前用户菜单树", responses = {
            @ApiResponse(responseCode = "200", description = "获取成功")
    })
    @GetMapping("current/menutree")
    public ResponseResult<AuthMenusVO> currentMenuTree() {
        String username = UserContextUtil.getCurrentUsername();
        UserEntity user = userService.findUserByUsername(username);
        List<String> roleMenus = new ArrayList<>();
        List<String> btns = new ArrayList<>();
        if (user.getRoles() != null) {

            user.getRoles().forEach(role -> {
                if (role.getRoleMenus() != null && !role.getRoleMenus().isEmpty()) {
                    roleMenus.addAll(role.getRoleMenus());
                }
                if (role.getBtns() != null && !role.getBtns().isEmpty()) {
                    btns.addAll(role.getBtns());
                }
            });
        }
        AuthMenusVO authMenusVO = new AuthMenusVO();

        authMenusVO.setAuthMenus(roleMenus);
        authMenusVO.setAuthButtons(btns);
        return ResponseResult.success(authMenusVO);
    }

}
