package com.gw.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.user.entity.ButtonEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Optional;

@Mapper
public interface ButtonMapper extends BaseMapper<ButtonEntity> {
    @Select("SELECT * FROM a_button WHERE menu_id = #{menuId} AND deleted = 0 ORDER BY sequence")
    List<ButtonEntity> findByMenuId(@Param("menuId") Long menuId);

    @Select("SELECT * FROM a_button WHERE id = #{id} AND deleted = 0")
    Optional<ButtonEntity> findById(@Param("id") Long id);
} 