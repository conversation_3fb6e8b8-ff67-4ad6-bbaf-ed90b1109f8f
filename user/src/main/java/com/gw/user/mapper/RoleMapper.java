package com.gw.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.user.entity.RoleEntity;
import com.gw.user.entity.UserRoleEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Optional;

@Mapper
public interface RoleMapper extends BaseMapper<RoleEntity> {
    List<RoleEntity> selectRolesByUserId(Long userId);

    @Select("UPDATE a_role set status = #{status} where id = ${entity.id} ")
    void updateRoleStatus(@Param("entity") RoleEntity entity, @Param("status") int status);

    @Select("SELECT * FROM a_role WHERE id = #{id} and deleted = 0 LIMIT 1")
    Optional<RoleEntity> findById(Long id);

    @Select("SELECT * FROM a_role WHERE code = #{code} and deleted = 0 LIMIT 1")
    Optional<RoleEntity> findByCode(@Param("code") String code);

    @Insert("INSERT INTO a_user_role (user_id, role_id) VALUES (#{userId}, #{roleId}) ")
    void insertUserReRole(@Param("userId") Long userId, @Param("roleId") Long roleId);

    @Delete("delete from  a_user_role where id = #{userId} ")
    void deleteUserReRoleById(@Param("id") Long id);

    @Delete("delete from  a_user_role where user_id = #{userId} and  role_id = #{roleId}")
    void deleteUserReRole(@Param("userId") Long userId, @Param("roleId") Long roleId);

    @Select("SELECT * FROM a_user_role ")
    List<UserRoleEntity> findAllUserRoles();
}
