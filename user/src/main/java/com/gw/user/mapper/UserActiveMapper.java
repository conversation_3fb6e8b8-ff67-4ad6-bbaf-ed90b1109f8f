package com.gw.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.user.entity.UserActiveEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Mapper
public interface UserActiveMapper extends BaseMapper<UserActiveEntity> {

    /**
     * 检查用户在指定日期是否已有活跃记录
     *
     * @param username   用户名
     * @param activeDate 活跃日期
     * @return 记录数量
     */
    @Select("SELECT COUNT(1) FROM t_user_active WHERE username = #{username} AND active_date = #{activeDate}")
    int countByUsernameAndDate(@Param("username") String username, @Param("activeDate") LocalDate activeDate);

    /**
     * 统计指定日期的活跃用户数
     *
     * @param date 日期
     * @return 活跃用户数
     */
    @Select("SELECT COUNT(DISTINCT username) FROM t_user_active WHERE active_date = #{date}")
    int countActiveUsersByDate(@Param("date") LocalDate date);

    /**
     * 统计指定月份的活跃用户数
     *
     * @param year  年份
     * @param month 月份
     * @return 活跃用户数
     */
    @Select("SELECT COUNT(DISTINCT username) FROM t_user_active WHERE EXTRACT(YEAR FROM active_date) = #{year} AND EXTRACT(MONTH FROM active_date) = #{month}")
    int countActiveUsersByMonth(@Param("year") int year, @Param("month") int month);

    /**
     * 统计指定年份的活跃用户数
     *
     * @param year 年份
     * @return 活跃用户数
     */
    @Select("SELECT COUNT(DISTINCT username) FROM t_user_active WHERE EXTRACT(YEAR FROM active_date) = #{year}")
    int countActiveUsersByYear(@Param("year") int year);

    /**
     * 统计指定日期范围内每天的活跃用户数
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 每天的活跃用户数统计
     */
    List<Map<String, Object>> countActiveUsersByDateRange(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
}
