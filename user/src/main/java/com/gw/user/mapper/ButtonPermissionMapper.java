package com.gw.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.user.entity.ButtonPermissionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Optional;

@Mapper
public interface ButtonPermissionMapper extends BaseMapper<ButtonPermissionEntity> {

    @Select("SELECT * FROM a_button_permission WHERE id = #{id} AND deleted = 0")
    Optional<ButtonPermissionEntity> findById(@Param("id") Long id);


    @Select("SELECT * FROM a_button_permission WHERE code = #{code} AND deleted = 0")
    Optional<ButtonPermissionEntity> findByCode(@Param("code") String code);
} 