package com.gw.user.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;

@Data
@TableName("t_user_active")
public class UserActiveEntity {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String username;
    private LocalDate activeDate;
}
