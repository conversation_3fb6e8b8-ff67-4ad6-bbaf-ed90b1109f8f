package com.gw.user.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gw.common.entity.BaseEntity;
import com.gw.common.util.PinyinUtils;
import lombok.Getter;
import lombok.Setter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

@Getter
@Setter
@TableName("a_user")
public class UserEntity extends BaseEntity implements UserDetails {
    private String username;
    private String password;
    private String nickname;
    private String realName;
    private String phone;
    private Integer gender = 3;
    private String identify;
    private String address;
    private String introduce;
    private int status = 1;
    private String remarks;
    private boolean modify = true;
    @TableField("is_show")
    private Integer isShow = 1;
    @TableField("is_root")
    private Integer isRoot = 0;
    @TableField(exist = false, select = false)
    private List<RoleEntity> roles;
    @TableField(exist = false)
    private String pinyinFirstLetter;
    @TableField("wx_open_id")
    private String wxOpenId;
    @TableField("avatar")
    private String avatar;
    @TableField(exist = false)
    private int isNew = 0;
    private LocalDateTime lastUseTime;
    private Integer isInvite = 0;
    public String getPinyinFirstLetter() {
        if (pinyinFirstLetter == null) {
            pinyinFirstLetter = PinyinUtils.getFirstLetter(this.realName);
        }
        return pinyinFirstLetter;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return List.of();
    }
}
