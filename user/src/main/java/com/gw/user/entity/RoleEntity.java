package com.gw.user.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gw.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@TableName("a_role")
public class RoleEntity extends BaseEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    private String name;
    private int sequence;
    private String remarks;
    private int status = 1;
    private String menus;

    private String buttons;
    @TableField(value = "is_show")
    private int isShow = 1;
    @TableField(value = "is_modify")
    private int isModify = 1;
    private String code;

    public List<String> getRoleMenus() {
        return (menus == null || menus.isEmpty()) ? new ArrayList<>() : List.of(menus.split(","));
    }

    public void setRoleMenus(List<String> roles) {
        this.menus = (roles == null || roles.isEmpty()) ? null : String.join(",", roles);
    }

    public List<String> getBtns() {
        return (buttons == null || buttons.isEmpty()) ? new ArrayList<>() : List.of(buttons.split(","));
    }

    public void setBtns(List<String> buttons) {
        this.buttons = (buttons == null || buttons.isEmpty()) ? null : String.join(",", buttons);
    }
}
