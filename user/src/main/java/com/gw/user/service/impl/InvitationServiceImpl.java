package com.gw.user.service.impl;

import com.gw.user.entity.InvitationEntity;
import com.gw.user.mapper.InvitationMapper;
import com.gw.user.service.InvitationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Log4j2
public class InvitationServiceImpl implements InvitationService {

    private final InvitationMapper invitationMapper;

    @Override
    public String generateInviteCode(Long userId) {
        // 生成唯一邀请码
        String inviteCode = generateUniqueCode();

        // 创建邀请记录
        InvitationEntity invitation = new InvitationEntity();
        invitation.setInviteCode(inviteCode);
        invitation.setInviterId(userId);
        invitation.setStatus(0); // 未使用
        invitation.setRewardStatus(0); // 未发放奖励
        invitation.setCreateTime(LocalDateTime.now());

        // 保存邀请记录
        invitationMapper.insert(invitation);

        return inviteCode;
    }

    @Override
    public InvitationEntity validateInviteCode(String inviteCode) {
        return invitationMapper.findByInviteCode(inviteCode);
    }

    @Override
    @Transactional
    public boolean useInviteCode(String inviteCode, Long inviteeId) {
        // 查询邀请记录
        InvitationEntity invitation = invitationMapper.findByInviteCode(inviteCode);

        if (invitation == null) {
            log.warn("邀请码不存在或已使用: {}", inviteCode);
            return false;
        }

        // 更新邀请记录
        invitation.setInviteeId(inviteeId);
        invitation.setStatus(1); // 已使用
        invitation.setUseTime(LocalDateTime.now());

        int result = invitationMapper.updateById(invitation);

        return result > 0;
    }

    @Override
    public List<InvitationEntity> getUserInvitations(Long userId) {
        return invitationMapper.findByInviterId(userId);
    }

    @Override
    @Transactional
    public boolean grantInvitationReward(Long invitationId) {
        // 查询邀请记录
        InvitationEntity invitation = invitationMapper.selectById(invitationId);

        if (invitation == null || invitation.getStatus() != 1 || invitation.getRewardStatus() == 1) {
            log.warn("邀请记录不存在或状态不正确: {}", invitationId);
            return false;
        }

        // TODO: 实现奖励发放逻辑

        // 更新奖励状态
        invitation.setRewardStatus(1); // 已发放
        int result = invitationMapper.updateById(invitation);

        return result > 0;
    }

    /**
     * 生成唯一邀请码
     *
     * @return 邀请码
     */
    private String generateUniqueCode() {
        // 生成8位随机字符串
        return UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
}