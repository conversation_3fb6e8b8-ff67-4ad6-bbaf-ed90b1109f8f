package com.gw.user.service.impl;

import com.gw.user.service.CaptchaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@Log4j2
@Service
@RequiredArgsConstructor
public class CaptchaServiceImpl implements CaptchaService {

    @Override
    @Cacheable(cacheNames = "captchaCache", key = "#captchaKey")
    public String getVerifyCode(String captchaKey) {
        // 加载特殊数据的逻辑...
        return "";
    }

    @Override
    @CachePut(cacheNames = "captchaCache", key = "#captchaKey")
    public String setVerifyCode(String captchaKey, String verifyCode) {
        // 加载特殊数据的逻辑...
        return verifyCode;
    }
}
