package com.gw.user.service.impl;

import com.gw.user.entity.UserActiveEntity;
import com.gw.user.mapper.UserActiveMapper;
import com.gw.user.service.UserActiveService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 用户活跃服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserActiveServiceImpl implements UserActiveService {

    private final UserActiveMapper userActiveMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recordUserActive(String username) {
        return recordUserActive(username, LocalDate.now());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recordUserActive(String username, LocalDate activeDate) {
        if (username == null || username.trim().isEmpty()) {
            log.warn("用户名为空，无法记录用户活跃");
            return false;
        }

        if (activeDate == null) {
            activeDate = LocalDate.now();
        }

        // 检查是否已存在记录
        int existingCount = userActiveMapper.countByUsernameAndDate(username, activeDate);
        if (existingCount > 0) {
            log.debug("用户 {} 在 {} 的活跃记录已存在", username, activeDate);
            return false;
        }

        // 创建新的活跃记录
        UserActiveEntity entity = new UserActiveEntity();
        entity.setUsername(username);
        entity.setActiveDate(activeDate);

        int result = userActiveMapper.insert(entity);
        if (result > 0) {
            log.info("成功记录用户 {} 在 {} 的活跃", username, activeDate);
            return true;
        } else {
            log.error("记录用户 {} 在 {} 的活跃失败", username, activeDate);
            return false;
        }
    }

    @Override
    public int countTodayActiveUsers() {
        return countActiveUsersByDate(LocalDate.now());
    }

    @Override
    public int countActiveUsersByDate(LocalDate date) {
        if (date == null) {
            date = LocalDate.now();
        }
        return userActiveMapper.countActiveUsersByDate(date);
    }

    @Override
    public int countCurrentMonthActiveUsers() {
        LocalDate now = LocalDate.now();
        return countActiveUsersByMonth(now.getYear(), now.getMonthValue());
    }

    @Override
    public int countActiveUsersByMonth(int year, int month) {
        return userActiveMapper.countActiveUsersByMonth(year, month);
    }

    @Override
    public int countCurrentYearActiveUsers() {
        return countActiveUsersByYear(LocalDate.now().getYear());
    }

    @Override
    public int countActiveUsersByYear(int year) {
        return userActiveMapper.countActiveUsersByYear(year);
    }

    @Override
    public List<Map<String, Object>> countActiveUsersInPastDays(int days) {
        if (days <= 0) {
            days = 30;
        }
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(days - 1);
        return countActiveUsersByDateRange(startDate, endDate);
    }

    @Override
    public List<Map<String, Object>> countActiveUsersInPast30Days() {
        return countActiveUsersInPastDays(30);
    }

    @Override
    public List<Map<String, Object>> countActiveUsersByDateRange(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null) {
            throw new IllegalArgumentException("开始日期和结束日期不能为空");
        }
        if (startDate.isAfter(endDate)) {
            throw new IllegalArgumentException("开始日期不能晚于结束日期");
        }
        return userActiveMapper.countActiveUsersByDateRange(startDate, endDate);
    }
}
