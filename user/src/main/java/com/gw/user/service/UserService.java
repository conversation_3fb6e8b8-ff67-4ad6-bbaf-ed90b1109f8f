package com.gw.user.service;

import com.github.pagehelper.PageInfo;
import com.gw.user.dto.UserQueryDto;
import com.gw.user.dto.WxLoginBaseParam;
import com.gw.user.dto.WxLoginParam;
import com.gw.user.entity.UserEntity;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface UserService {
    List<UserEntity> findAll();

    UserEntity findUserByUsername(String username);

    Map<String, UserEntity> findAllGroupMapUsername();

    boolean checkUsernameExists(String username);

    long creatUser(UserEntity user);


    void updateUser(UserEntity user);
    void updateUserPhone(UserEntity user);
    UserEntity findUserById(long id);

    void deleteUserById(UserEntity user);

    void updateUserStatus(UserEntity user, int status);

    PageInfo<UserEntity> page(int pageNum, int pageSize, UserQueryDto query);

    List<UserEntity> findAll(UserQueryDto query);

    List<UserEntity> findAll(List<Long> userIds);

    List<UserEntity> findAll(long[] ids);


    void batchCreatUser(List<UserEntity> user);

    void changePasswordByUserId(String password, long userId);

    void cleanCache();

    Map<String, List<UserEntity>> getAllUsersSortedByPinyin(UserQueryDto query);
    Map<String,UserEntity> findALlByUsernames(List<String> usernames);
    String QueryPhoneByWx(String code);

    /**
     * 通过微信小程序登录参数查找或创建用户
     *
     * @param loginParam 微信登录参数
     * @return 用户实体
     */
    UserEntity findOrCreateWxUser(WxLoginParam loginParam);

    UserEntity findOrCreateWxUserWithoutPhone(WxLoginBaseParam loginParam);

    int countTodayNewUsers(LocalDateTime startTime, LocalDateTime endTime);

    int countTotalUsers();

    void updateLastUseTime(Long userId);
}
