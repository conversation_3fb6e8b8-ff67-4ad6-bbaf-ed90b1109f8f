package com.gw.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gw.common.entity.BaseEntity;
import com.gw.user.entity.UserBaseEntity;
import com.gw.user.entity.UserEntity;
import com.gw.user.mapper.UserMapper;
import com.gw.user.service.BaseService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class BaseServiceImpl implements BaseService {
    private final UserMapper userMapper;

    @Override
    public List<UserBaseEntity> findAllBaseUserEntity(List<Long> userIds) {
        long[] ids = userIds.stream().mapToLong(Long::longValue).toArray();
        return userMapper.findAllUserBaseByUserIds(ids);
    }

    @Override
    public List<UserBaseEntity> findAllBaseUserEntity() {

        return userMapper.findAllUserBase();
    }

    @Override
    public void fillBaseEntityName(BaseEntity entity) {
        if (entity == null) {
            return;
        }
        if (entity.getCreator() != null) {
            entity.setCreatorName(userMapper.findByUsername(entity.getCreator())
                    .map(UserEntity::getRealName)
                    .orElse("")); // 找不到用户时返回空字符串
        }
        if (entity.getUpdater() != null) {
            entity.setUpdaterName(userMapper.findByUsername(entity.getUpdater())
                    .map(UserEntity::getRealName)
                    .orElse(""));

        }
    }

    @Override
    public void fillBaseEntityName(List list) {
        if (list == null || list.isEmpty()) {
            return;
        }
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deleted", 0);
        List<UserEntity> users = userMapper.selectList(queryWrapper);
        Map<String, UserEntity> userMap = users.stream()
                .collect(Collectors.toMap(
                        UserEntity::getUsername,
                        user -> user,
                        (existing, replacement) -> existing
                ));
        for (Object o : list) {
            if (o instanceof BaseEntity baseEntity) {
                if (baseEntity.getCreator() != null) {
                    baseEntity.setCreatorName(userMap.get(baseEntity.getCreator()).getRealName());
                }
                if (baseEntity.getUpdater() != null) {
                    baseEntity.setUpdaterName(userMap.get(baseEntity.getUpdater()).getRealName());
                }

            }
        }
    }
}
