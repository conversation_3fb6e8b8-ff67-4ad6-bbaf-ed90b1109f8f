package com.gw.user.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gw.common.exception.EntityNotFoundException;
import com.gw.common.user.constant.UserCommonCacheConstant;
import com.gw.common.user.service.UserProxyService;
import com.gw.common.user.vo.UserBaseContentVo;
import com.gw.user.config.CacheProperties;
import com.gw.user.constant.UserConstant;
import com.gw.user.dto.UserQueryDto;
import com.gw.user.dto.WxLoginBaseParam;
import com.gw.user.dto.WxLoginParam;
import com.gw.user.entity.RoleEntity;
import com.gw.user.entity.UserEntity;
import com.gw.user.mapper.RoleMapper;
import com.gw.user.mapper.UserMapper;
import com.gw.user.service.BaseService;
import com.gw.user.service.RoleService;
import com.gw.user.service.UserService;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Log4j2
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, UserEntity> implements UserService {

    private final PasswordEncoder passwordEncoder;
    private final CacheProperties cacheProperties;
    private final RoleMapper roleMapper;
    private final UserProxyService userProxyService;
    @Lazy
    private final RoleService roleService;
    private final BaseService baseService;
    private final WxMaService wxMaService;

    /**
     * 初始化默认用户
     */
    @PostConstruct
    public void init() {
        log.info("UserServiceImpl init");
        if (this.checkUsernameExists(UserConstant.DEFAULT_USER_NAME)) {
            return;
        }
        UserEntity entity = new UserEntity();
        entity.setUsername(UserConstant.DEFAULT_USER_NAME);
        entity.setPassword(passwordEncoder.encode(UserConstant.DEFAULT_USER_PASSWORD));
        entity.setRealName(UserConstant.DEFAULT_REAL_NAME);
        entity.setModify(false);
        this.baseMapper.insert(entity);
    }

    @Override
    @Cacheable(value = "user", key = "#root.methodName")
    public List<UserEntity> findAll() {
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deleted", 0);

        return baseMapper.selectList(queryWrapper);
    }

    private UserEntity fillRolesEntity(UserEntity entity) {
        List<RoleEntity> roleEntity = roleMapper.selectRolesByUserId(entity.getId());
        entity.setRoles(roleEntity);
        return entity;
    }

    @Override
    @Cacheable(value = "user", key = "'username:' + #username")
    public UserEntity findUserByUsername(String username) {
        UserEntity entity = fillRolesEntity(baseMapper.findByUsername(username).orElseThrow());
        baseService.fillBaseEntityName(entity);
        return entity;
    }

    @Override
    @Cacheable(value = "user", key = "#root.methodName")
    public Map<String, UserEntity> findAllGroupMapUsername() {
        List<UserEntity> entities = this.findAll();
        baseService.fillBaseEntityName(entities);
        return entities.stream().collect(Collectors.toMap(UserEntity::getUsername, user -> user));
    }

    @Override
    public boolean checkUsernameExists(String username) {
        return baseMapper.findByUsername(username).isPresent();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(cacheNames = "user", allEntries = true, beforeInvocation = true)
    public long creatUser(UserEntity user) {
        this.baseMapper.insert(user);
        if (user.getRoles() != null) {
            user.getRoles().forEach(role -> roleMapper.insertUserReRole(user.getId(), role.getId()));
        }
        return user.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(cacheNames = "user", allEntries = true, beforeInvocation = true)
    public void batchCreatUser(List<UserEntity> users) {
        users.forEach(this::creatUser);
    }

    @Override
    @CacheEvict(cacheNames = "user", allEntries = true, beforeInvocation = true)
    public void changePasswordByUserId(String password, long userId) {
        this.baseMapper.changePasswordById(userId, password);
    }

    @Override
    @CacheEvict(cacheNames = "user", allEntries = true, beforeInvocation = true)
    public void cleanCache() {

    }

    private void deleteNoLongerOwnedRoles(UserEntity user, List<RoleEntity> roles) {
        // 优化：通过批量删除，减少数据库操作次数
        List<Long> roleIds = user.getRoles() != null ? user.getRoles().stream().map(RoleEntity::getId).toList()
                : new ArrayList<>();
        roles.stream()
                .filter(role -> !roleIds.contains(role.getId()))
                .forEach(role -> roleMapper.deleteUserReRole(user.getId(), role.getId()));
    }

    private void processNewAndUpdatedRoles(UserEntity user, List<RoleEntity> roles) {
        List<Long> roleIds = roles != null ? roles.stream().map(RoleEntity::getId).toList() : new ArrayList<>();
        if (user.getRoles() != null) {
            user.getRoles().stream()
                    .filter(role -> !roleIds.contains(role.getId()))
                    .forEach(role -> roleMapper.insertUserReRole(user.getId(), role.getId()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(cacheNames = "user", allEntries = true, beforeInvocation = true)
    public void updateUser(UserEntity user) {
        this.baseMapper.updateById(user);
        List<RoleEntity> roleEntity = roleMapper.selectRolesByUserId(user.getId());
        deleteNoLongerOwnedRoles(user, roleEntity);
        processNewAndUpdatedRoles(user, roleEntity);
    }

    @Override
    public void updateUserPhone(UserEntity user) {
        this.baseMapper.updatePhoneById(user.getId(), user.getPhone());
    }

    @Override
    @Cacheable(value = "user", key = "'id:' + #id")
    public UserEntity findUserById(long id) {
        return fillRolesEntity(
                this.baseMapper.findById(id).orElseThrow(() -> new EntityNotFoundException("没有找到对应的用户")));
    }

    @Override
    @CacheEvict(cacheNames = "user", allEntries = true, beforeInvocation = true)
    public void deleteUserById(UserEntity user) {
        user.setDeleted(1);
        this.baseMapper.updateById(user);
        this.baseMapper.deleteById(user);
    }

    @Override
    @CacheEvict(cacheNames = "user", allEntries = true, beforeInvocation = true)
    public void updateUserStatus(UserEntity user, int status) {
        this.baseMapper.updateStatusById(user.getId(), status);
    }

    private List<UserEntity> fillUserRoles(List<UserEntity> entities) {
        if (entities == null) {
            return null;
        }
        Map<Long, List<Long>> userRoleIds = roleService.findAllRoleIdsGroupByUserId();
        if (userRoleIds == null) {
            return entities;
        }
        entities.forEach(entity -> {
            List<Long> roleIds = userRoleIds.get(entity.getId());
            entity.setRoles(new ArrayList<>());
            if (roleIds != null) {
                roleIds.forEach(roleId -> {
                    RoleEntity role = roleService.findById(roleId);
                    entity.getRoles().add(role);
                });
            }
        });
        return entities;
    }

    @Override
    public PageInfo<UserEntity> page(int pageNum, int pageSize, UserQueryDto query) {
        // 设置分页参数
        PageHelper.startPage(pageNum, pageSize);
        // 执行查询
        List<UserEntity> entities = this.baseMapper.selectPage(query);
        baseService.fillBaseEntityName(entities);
        return new PageInfo<>(fillUserRoles(entities));
    }

    @Override
    public List<UserEntity> findAll(UserQueryDto query) {
        List<UserEntity> entities = fillUserRoles(this.baseMapper.findAllUser(query));
        baseService.fillBaseEntityName(entities);
        return entities;
    }

    @Override
    public List<UserEntity> findAll(List<Long> userIds) {
        long[] ids = userIds.stream().mapToLong(Long::longValue).toArray();
        return fillUserRoles(this.baseMapper.findAllUserByUserIds(ids));
    }

    @Override
    public List<UserEntity> findAll(long[] ids) {
        return fillUserRoles(this.baseMapper.findAllUserByUserIds(ids));
    }

    @Override
    public Map<String, List<UserEntity>> getAllUsersSortedByPinyin(UserQueryDto query) {
        List<UserEntity> users = this.baseMapper.findAllUser(query);
        baseService.fillBaseEntityName(users);
        fillUserRoles(users);
        return users.stream()
                .sorted(Comparator.comparing(UserEntity::getPinyinFirstLetter)
                        .thenComparing(UserEntity::getRealName))
                .collect(Collectors.groupingBy(
                        UserEntity::getPinyinFirstLetter,
                        Collectors.toList()));
    }

    @Override
    public Map<String, UserEntity> findALlByUsernames(List<String> usernames) {
        if (usernames == null || usernames.isEmpty()) {
            return Map.of();
        }
        Map<String, UserBaseContentVo> newMaps = new HashMap<>();
        List<UserEntity> entities = this.baseMapper.findAllUsersByUsernames(usernames);


        return entities.stream()
                .collect(Collectors.toMap(UserEntity::getUsername, user -> user));
    }

    /**
     * 生成8位随机字符串，包含英文和数字
     *
     * @return 8位随机字符串
     */
    private String generateEightDigitCode() {
        String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder code = new StringBuilder();
        Random random = new Random();

        for (int i = 0; i < 8; i++) {
            code.append(characters.charAt(random.nextInt(characters.length())));
        }

        return code.toString();
    }
    @Override
    public String QueryPhoneByWx(String code) {
        try {
            WxMaPhoneNumberInfo phoneNoInfo = wxMaService.getUserService().getPhoneNoInfo(code);
            return phoneNoInfo.getPhoneNumber();
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }

    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserEntity findOrCreateWxUser(WxLoginParam loginParam) {
        try {
            log.info("wx login {}", JSON.toJSONString(loginParam));

            UserEntity newUser = findOrCreateWxUserWithoutPhone(loginParam);

            // 获取手机号
            WxMaPhoneNumberInfo phoneNoInfo = wxMaService.getUserService().getPhoneNoInfo(loginParam.getCode());
            newUser.setPhone(phoneNoInfo.getPhoneNumber());
            baseMapper.updateById(newUser);

            return newUser;
        } catch (WxErrorException e) {
            throw new RuntimeException("微信登录失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserEntity findOrCreateWxUserWithoutPhone(WxLoginBaseParam loginParam) {
        try {
            log.info("wx login {}", JSON.toJSONString(loginParam));

            // 获取openid
            String openid = wxMaService.getUserService().getSessionInfo(loginParam.getOpenIdCode()).getOpenid();

            // 查找是否已存在该电话的用户
            UserEntity user = baseMapper.findByWxOpenId(openid);
            boolean isExist = user != null;
            if (user != null && user.getDeleted() == 0) {
                // 更新openid
                user.setWxOpenId(openid);

                baseMapper.updateById(user);
                user.setIsNew(0);
                return user;
            }

            // 创建新用户
            UserEntity newUser = user;
            if(newUser == null){
                newUser = new UserEntity();
            }
            newUser.setDeleted(0);
            String time = System.currentTimeMillis() + "";

            newUser.setPassword(passwordEncoder.encode(UUID.randomUUID().toString())); // 随机密码

            newUser.setStatus(UserConstant.ENABLE_USER);
            newUser.setWxOpenId(openid);
            newUser.setNickname("灵兮_临时用户");
            newUser.setRealName("灵兮_临时用户");
            newUser.setIsNew(1);
            if(isExist){
                if (loginParam.getInviteCode() != null && !loginParam.getInviteCode().isEmpty()) {
                    newUser.setIsInvite(1);
                }
                baseMapper.updateById(newUser);
            }else {
                // 设置临时用户名以满足数据库约束
                String tempUsername = "temp_" + time + "_" + UUID.randomUUID().toString().substring(0, 8);
                newUser.setUsername(tempUsername);

                // 保存用户
                baseMapper.insert(newUser);
                Long id = newUser.getId();
                String identify = String.format("%06d", id) + time.substring(time.length() - 2);

                // 检查identify是否已存在，如果存在则使用更长的格式
                if (baseMapper.countByIdentity(identify) != null && baseMapper.countByIdentity(identify) > 0) {
                    identify = String.format("%07d", id) + time.substring(time.length() - 3);
                }

                // 更新为最终的用户名和昵称
                newUser.setUsername(identify);
                String nickname = "灵兮_" + identify;
                newUser.setNickname(nickname);
                newUser.setRealName(nickname);
                newUser.setIdentify(identify);

                if (loginParam.getInviteCode() != null && !loginParam.getInviteCode().isEmpty()) {
                    newUser.setIsInvite(1);
                }

                baseMapper.updateById(newUser);
            }
            // 分配默认角色
            roleService.assignAPPRolesToUser(newUser.getId());
            String cacheKey = cacheProperties.getCacheName(UserCommonCacheConstant.USER_BASE_MAP_CACHE_KEY);

            UserBaseContentVo vo = UserBaseContentVo.builder()
                    .username(newUser.getUsername())
                    .nickname(newUser.getNickname())
                    .phone(newUser.getPhone())
                    .gender(newUser.getGender())
                    .introduce(newUser.getIntroduce())
                    .wxOpenId(newUser.getWxOpenId())
                    .avatar(newUser.getAvatar())
                    .build();
            userProxyService.updateToCacheIfCacheExist(cacheKey, vo.getUsername(), vo, cacheProperties.getCacheExpiration(UserCommonCacheConstant.USER_CATEGORY_KEY));

            return newUser;
        } catch (WxErrorException e) {
            throw new RuntimeException("微信登录失败", e);
        }
    }

    @Override
    public int countTodayNewUsers(LocalDateTime startTime, LocalDateTime endTime) {
        return this.baseMapper.countNewUsersByTimeRange(startTime, endTime);
    }

    @Override
    public int countTotalUsers() {
        return this.baseMapper.countTotalUsers();
    }

    @Override
    public void updateLastUseTime(Long userId) {
        this.baseMapper.updateLastUseTimeById(userId, LocalDateTime.now());
    }
}
