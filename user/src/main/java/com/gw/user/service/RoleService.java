package com.gw.user.service;

import com.github.pagehelper.PageInfo;
import com.gw.user.dto.RoleQueryDto;
import com.gw.user.entity.RoleEntity;

import java.util.List;
import java.util.Map;

public interface RoleService {
    List<RoleEntity> findAll();

    void updateStatus(RoleEntity role, int status);

    void delete(RoleEntity role);

    RoleEntity findById(long roleId);

    RoleEntity findByCode(String code);

    long insert(RoleEntity role);

    long update(RoleEntity role);

    void updateRoleAuth(RoleEntity role);

    PageInfo<RoleEntity> page(int pageNum, int pageSize, RoleQueryDto query);

    Map<Long, List<Long>> findAllRoleIdsGroupByUserId();

    /**
     * 为新用户分配默认角色
     *
     * @param userId 用户ID
     */
    void assignAPPRolesToUser(Long userId);
}
