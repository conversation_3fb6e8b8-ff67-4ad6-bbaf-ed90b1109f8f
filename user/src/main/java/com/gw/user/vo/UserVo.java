package com.gw.user.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "用户模型")
public class UserVo {
    @Schema(description = "用户ID")
    private long id;
    private String identify;
    @Schema(description = "用户名")
    private String username;
    @Schema(description = "真实姓名")
    private String realName;
    @Schema(description = "电话")
    private String phone;
    @Schema(description = "用户信息是否可以修改，false 不可以，true 可以")
    private boolean modify;
    @Schema(description = "状态 1 启用，2禁用")
    private int status;
    @Schema(description = "备注")
    private String remarks;
    private List<RoleBaseVo> roles;
    @Schema(description = "是否在线")
    private boolean online;
    @Schema(description = "用户昵称")
    private String nickname;
    @Schema(description = "性别：1-男，2-女，3-其他")
    private Integer gender;
    @Schema(description = "用户头像")
    private String avatar;
    @Schema(description = "用户地址")
    private String address;
    @Schema(description = "用户简介")
    private String introduce;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    private String creator;
    private String updater;
    private String creatorName;
    private String updaterName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUseTime;
    private String wxOpenId;
    private Integer isInvite = 0;
}
