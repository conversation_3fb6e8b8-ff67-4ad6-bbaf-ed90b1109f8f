package com.gw.user.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gw.user.entity.RoleEntity;
import com.gw.user.mapper.ModelMapperConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.modelmapper.ModelMapper;

import java.time.LocalDateTime;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "权限信息")
public class RoleBaseVo {
    private long id;
    private String name;
    private String remarks;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    private String creator;
    private String updater;
    private String creatorName;
    private String updaterName;
    private int status;

    public RoleBaseVo(RoleEntity role) {
        ModelMapper mapper = ModelMapperConvert.getBaseModelMapper();
        mapper.map(role, this);
    }
}
