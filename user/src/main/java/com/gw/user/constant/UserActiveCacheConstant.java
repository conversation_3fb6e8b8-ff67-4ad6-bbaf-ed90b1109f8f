package com.gw.user.constant;

/**
 * 用户活跃缓存常量
 */
public class UserActiveCacheConstant {
    
    /**
     * 用户活跃记录缓存名称
     */
    public static final String USER_ACTIVE_CACHE = "userActive";
    
    /**
     * 用户活跃统计缓存名称
     */
    public static final String USER_ACTIVE_STATS_CACHE = "userActiveStats";
    
    /**
     * 生成用户活跃记录缓存键
     * 格式：username:yyyy-MM-dd
     */
    public static String generateUserActiveCacheKey(String username, String date) {
        return username + ":" + date;
    }
    
    /**
     * 生成日期活跃用户数缓存键
     * 格式：date:yyyy-MM-dd
     */
    public static String generateDateActiveCountCacheKey(String date) {
        return "date:" + date;
    }
    
    /**
     * 生成月份活跃用户数缓存键
     * 格式：month:yyyy-MM
     */
    public static String generateMonthActiveCountCacheKey(int year, int month) {
        return "month:" + year + "-" + String.format("%02d", month);
    }
    
    /**
     * 生成年份活跃用户数缓存键
     * 格式：year:yyyy
     */
    public static String generateYearActiveCountCacheKey(int year) {
        return "year:" + year;
    }
}
