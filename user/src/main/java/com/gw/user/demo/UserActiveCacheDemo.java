package com.gw.user.demo;

import com.gw.user.service.UserActiveService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * 用户活跃缓存演示类
 * 演示缓存的性能提升效果
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserActiveCacheDemo implements CommandLineRunner {

    private final UserActiveService userActiveService;

    @Override
    public void run(String... args) throws Exception {
        // 只在开发环境运行演示
        if (!"dev".equals(System.getProperty("spring.profiles.active"))) {
            return;
        }

        log.info("=== 用户活跃缓存性能演示 ===");
        
        String testUsername = "demo_user_" + System.currentTimeMillis();
        LocalDate testDate = LocalDate.now();

        // 演示记录用户活跃的缓存效果
        demonstrateRecordActiveCache(testUsername, testDate);
        
        // 演示统计查询的缓存效果
        demonstrateStatsCache(testDate);
        
        log.info("=== 缓存演示完成 ===");
    }

    private void demonstrateRecordActiveCache(String username, LocalDate date) {
        log.info("--- 演示记录用户活跃的缓存效果 ---");
        
        // 第一次记录（会查询数据库）
        long start1 = System.currentTimeMillis();
        boolean result1 = userActiveService.recordUserActive(username, date);
        long time1 = System.currentTimeMillis() - start1;
        log.info("第一次记录用户活跃: 结果={}, 耗时={}ms", result1, time1);

        // 第二次记录（命中缓存）
        long start2 = System.currentTimeMillis();
        boolean result2 = userActiveService.recordUserActive(username, date);
        long time2 = System.currentTimeMillis() - start2;
        log.info("第二次记录用户活跃: 结果={}, 耗时={}ms (缓存命中)", result2, time2);

        // 第三次记录（再次命中缓存）
        long start3 = System.currentTimeMillis();
        boolean result3 = userActiveService.recordUserActive(username, date);
        long time3 = System.currentTimeMillis() - start3;
        log.info("第三次记录用户活跃: 结果={}, 耗时={}ms (缓存命中)", result3, time3);

        if (time2 < time1 && time3 < time1) {
            log.info("✅ 缓存生效！后续查询比首次查询快 {}ms 和 {}ms", 
                    time1 - time2, time1 - time3);
        }
    }

    private void demonstrateStatsCache(LocalDate date) {
        log.info("--- 演示统计查询的缓存效果 ---");
        
        // 第一次统计查询（会查询数据库）
        long start1 = System.currentTimeMillis();
        int count1 = userActiveService.countActiveUsersByDate(date);
        long time1 = System.currentTimeMillis() - start1;
        log.info("第一次统计查询: 结果={}, 耗时={}ms", count1, time1);

        // 第二次统计查询（命中缓存）
        long start2 = System.currentTimeMillis();
        int count2 = userActiveService.countActiveUsersByDate(date);
        long time2 = System.currentTimeMillis() - start2;
        log.info("第二次统计查询: 结果={}, 耗时={}ms (缓存命中)", count2, time2);

        // 测试月份统计缓存
        long start3 = System.currentTimeMillis();
        int monthCount1 = userActiveService.countCurrentMonthActiveUsers();
        long time3 = System.currentTimeMillis() - start3;
        log.info("第一次月份统计: 结果={}, 耗时={}ms", monthCount1, time3);

        long start4 = System.currentTimeMillis();
        int monthCount2 = userActiveService.countCurrentMonthActiveUsers();
        long time4 = System.currentTimeMillis() - start4;
        log.info("第二次月份统计: 结果={}, 耗时={}ms (缓存命中)", monthCount2, time4);

        if (time2 < time1 && time4 < time3) {
            log.info("✅ 统计缓存生效！缓存查询比数据库查询快 {}ms 和 {}ms", 
                    time1 - time2, time3 - time4);
        }
    }
}
