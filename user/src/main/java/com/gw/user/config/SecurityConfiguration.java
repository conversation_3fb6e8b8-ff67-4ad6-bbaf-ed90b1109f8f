package com.gw.user.config;

import com.gw.user.config.properties.SecurityProperties;
import com.gw.user.filter.JwtAuthenticationFilter;
import jakarta.annotation.PostConstruct;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authorization.AuthorizationDecision;
import org.springframework.security.authorization.AuthorizationManager;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.intercept.RequestAuthorizationContext;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;

@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
@EnableMethodSecurity
@EnableConfigurationProperties({SecurityProperties.class})
@Log4j2
public class SecurityConfiguration {

    private final JwtAuthenticationFilter jwtAuthFilter;
    private final CustomPermissionEvaluator customPermissionEvaluator;
    private final SecurityProperties securityProperties;

    @PostConstruct
    public void init() {
        log.info("Loaded public paths: {}", securityProperties.getPublicPaths());
        log.info("Loaded CORS configuration: {}", securityProperties.getCors());
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http.csrf(AbstractHttpConfigurer::disable)
                .sessionManagement(c -> c.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .cors(c -> c.configurationSource(request -> {
                    CorsConfiguration config = new CorsConfiguration();
                    config.setAllowedOrigins(securityProperties.getCors().getAllowedOrigins());
                    config.setAllowedMethods(securityProperties.getCors().getAllowedMethods());
                    config.setAllowedHeaders(securityProperties.getCors().getAllowedHeaders());
                    config.setExposedHeaders(securityProperties.getCors().getExposedHeaders());
                    config.setAllowCredentials(securityProperties.getCors().isAllowCredentials());
                    config.setMaxAge(securityProperties.getCors().getMaxAge());
                    return config;
                }))
                .authorizeHttpRequests(authz -> {
                    // First, explicitly allow all configured public paths unconditionally
                    for (String path : securityProperties.getPublicPaths()) {
                        authz.requestMatchers(path).permitAll();
                    }

                    // Then handle other authorization rules
                    authz.requestMatchers(request -> {
                                String internalService = request.getHeader("X-Internal-Service");
                                System.out.println("internalService = " + internalService);
                                return internalService != null &&
                                        securityProperties.getInternalServices().contains(internalService);
                            }).permitAll()
                            .anyRequest().authenticated();
                })
                .addFilterBefore(jwtAuthFilter, UsernamePasswordAuthenticationFilter.class)
                .exceptionHandling(c -> c.authenticationEntryPoint((request, response, authException) -> response
                        .sendError(HttpServletResponse.SC_UNAUTHORIZED, "未授权访问")));

        return http.build();
    }

    private AuthorizationManager<RequestAuthorizationContext> customAuthorizationManager() {
        return (authentication, context) -> {
            boolean hasPermission = customPermissionEvaluator.hasPermission(
                    authentication.get(),
                    context.getRequest().getRequestURI(),
                    "ACCESS");
            return new AuthorizationDecision(hasPermission);
        };
    }
}