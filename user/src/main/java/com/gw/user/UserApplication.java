package com.gw.user;

import com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner;
import com.baomidou.mybatisplus.extension.ddl.IDdl;
import com.gw.common.user.context.WebMvcUserContextConfig;
import com.gw.user.config.properties.SecurityProperties;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.util.List;

@OpenAPIDefinition(
        info = @Info(
                title = "用户管理通用API",
                version = "1.0",
                description = "用户管理通用API"
        ),
        security = @SecurityRequirement(name = "bearerAuth")
)
@EnableTransactionManagement
@SpringBootApplication
@EnableDiscoveryClient
@EnableConfigurationProperties({SecurityProperties.class})
@Import(WebMvcUserContextConfig.class)
@EnableFeignClients(basePackages = {"com.gw.common.user.client", "com.gw.common.membership.client", "com.gw.common.agent.client"})
@ComponentScan(basePackages = {
        "com.gw.user",
        "com.gw.common.user.context",
        "com.gw.common.user",
        "com.gw.common.membership",
        "com.gw.common.agent",
        "com.gw.common.exception"
})
public class UserApplication {

    public static void main(String[] args) {
        SpringApplication.run(UserApplication.class, args);
    }

    @Bean
    public DdlApplicationRunner ddlApplicationRunner(@Autowired(required = false) List<IDdl> ddlList) {
        return new DdlApplicationRunner(ddlList);
    }
}
