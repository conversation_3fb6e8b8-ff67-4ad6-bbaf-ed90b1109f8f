<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gw.user.mapper.UserMapper">
    <resultMap id="userMap" type="com.gw.user.entity.UserEntity">
        <id column="id" property="id"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="real_name" property="realName"/>
        <result column="phone" property="phone"/>
        <result column="status" property="status"/>
        <result column="remarks" property="remarks"/>
        <result column="avatar" property="avatar"/>
        <result column="wx_open_id" property="wxOpenId"/>
        <result column="modify" property="modify"/>
        <result column="gender" property="gender"/>
        <result column="nickname" property="nickname"/>
        <result column="address" property="address"/>
        <result column="introduce" property="introduce"/>
        <result column="deleted" property="deleted"/>
        <result column="is_root" property="isRoot"/>
        <result column="identify" property="identify"/>
        <result column="creator" property="creator"/>
        <result column="update_time" property="updateTime"/>
        <result column="updater" property="updater"/>
        <result column="create_time" property="createTime"/>
        <result column="last_use_time" property="lastUseTime"/>
        <result column="is_invite" property="isInvite"/>
        <collection property="roles" ofType="com.gw.user.entity.RoleEntity">
            <id column="role_id" property="id"/>
            <result column="role_name" property="name"/>
            <result column="role_sequence" property="sequence"/>
            <result column="role_remarks" property="remarks"/>
            <result column="menus" property="menus"/>
            <result column="buttons" property="buttons"/>
            <result column="role_status" property="status"/>
        </collection>
    </resultMap>
    <sql id="userColumns">
        u.id
        , u.username, u.password, u.real_name, u.phone, u.status, u.remarks, u.avatar, u.modify,u.is_root,u.wx_open_id, u.last_use_time,
        u.nickname, u.gender, u.address, u.introduce,u.identify,u.is_invite,
        r.id as role_id, r.name as role_name, r.sequence as role_sequence, r.remarks as role_remarks, r.status as role_status,
        r.menus as menus,r.buttons as buttons,
        u.creator, u.updater, u.create_time, u.update_time, u.deleted
    </sql>
    <sql id="userPageColumns">
        u.id
        , u.username, u.password, u.real_name, u.phone, u.status, u.remarks, u.avatar, u.modify,u.is_root,u.wx_open_id, u.last_use_time,
        u.nickname, u.gender, u.address, u.introduce,u.identify,u.is_invite,
        u.creator, u.updater, u.create_time, u.update_time, u.deleted
    </sql>
    <!-- 分页查询 -->
    <select id="selectPage" resultMap="userMap">
        SELECT
        <include refid="userPageColumns"/>
        FROM a_user u
        LEFT JOIN a_user_role ur ON u.id = ur.user_id
        LEFT JOIN a_role r ON ur.role_id = r.id
        <where>
            u.deleted = 0
            <if test="queryValue != null and queryValue != ''">
                AND (
                u.username LIKE CONCAT('%', #{queryValue}, '%')
                OR u.real_name LIKE CONCAT('%', #{queryValue}, '%')
                )
            </if>
            <if test="phone != null and phone != ''">
                And u.phone LIKE CONCAT('%', #{phone}, '%')
            </if>
            <if test="roleCode != null  and roleCode != ''">
                And r.code = #{roleCode}
            </if>
            <if test="isShow > 0">
                AND u.is_show = 1
            </if>
        </where>
        ORDER BY u.update_time DESC, u.id DESC
    </select>
    <select id="findAllUser" resultMap="userMap">
        SELECT
        <include refid="userColumns"/>
        FROM a_user u
        LEFT JOIN a_user_role ur ON u.id = ur.user_id
        LEFT JOIN a_role r ON ur.role_id = r.id
        <where>
            u.deleted = 0
            AND u.is_show = 1
            <if test="queryValue != null and queryValue != ''">
                AND (
                u.username LIKE CONCAT('%', #{queryValue}, '%')
                OR u.real_name LIKE CONCAT('%', #{queryValue}, '%')
                )
            </if>
            <if test="roleCode != null  and roleCode != ''">
                And r.code = #{roleCode}
            </if>
            <if test="roleIds != null and roleIds.size() > 0">
                AND r.id IN
                <foreach collection="roleIds" item="roleId" open="(" close=")" separator=",">
                    #{roleId}
                </foreach>
            </if>
        </where>
        ORDER BY u.update_time DESC, u.id DESC
    </select>
    <!-- 根据 ID 查询 -->
    <select id="findById" resultMap="userMap">
        SELECT
        <include refid="userColumns"/>
        FROM a_user u
        LEFT JOIN a_user_role ur ON u.id = ur.user_id
        LEFT JOIN a_role r ON ur.role_id = r.id
        WHERE u.id = #{id} AND u.deleted = 0
    </select>
    <select id="findByUsername" resultMap="userMap">
        SELECT
        <include refid="userColumns"/>
        FROM a_user u
        LEFT JOIN a_user_role ur ON u.id = ur.user_id
        LEFT JOIN a_role r ON ur.role_id = r.id
        WHERE username = #{username}
        LIMIT 1
    </select>

    <select id="findAllUserByUserIds" resultMap="userMap">
        SELECT
        <include refid="userColumns"/>
        FROM a_user u
        LEFT JOIN a_user_role ur ON u.id = ur.user_id
        LEFT JOIN a_role r ON ur.role_id = r.id
        WHERE u.deleted = 0 AND u.id = ANY (ARRAY [#{userIds}])
        ORDER BY u.update_time DESC, u.id DESC
    </select>

    <select id="findAllUsersByUsernames" resultMap="userMap">
        SELECT
        <include refid="userColumns"/>
        FROM a_user u
        LEFT JOIN a_user_role ur ON u.id = ur.user_id
        LEFT JOIN a_role r ON ur.role_id = r.id
        WHERE u.deleted = 0
        <if test="usernames != null and usernames.size() > 0">
            AND u.username IN
            <foreach collection="usernames" item="username" open="(" close=")" separator=",">
                #{username}
            </foreach>
        </if>
        ORDER BY u.update_time DESC, u.id DESC
    </select>
    <resultMap id="userResultMap" type="com.gw.user.entity.UserEntity">
        <id column="id" property="id"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="real_name" property="realName"/>
        <result column="phone" property="phone"/>
        <result column="status" property="status"/>
        <result column="remarks" property="remarks"/>
        <result column="avatar" property="avatar"/>
        <result column="modify" property="modify"/>
        <result column="gender" property="gender"/>
        <result column="nickname" property="nickname"/>
        <result column="address" property="address"/>
        <result column="introduce" property="introduce"/>
        <result column="is_invite," property="isInvite"/>
        <result column="last_use_time" property="lastUseTime"/>
        <result column="identify" property="identify"/>
    </resultMap>
</mapper>