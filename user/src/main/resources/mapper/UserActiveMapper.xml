<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gw.user.mapper.UserActiveMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.gw.user.entity.UserActiveEntity">
        <id column="id" property="id"/>
        <result column="username" property="username"/>
        <result column="active_date" property="activeDate"/>
    </resultMap>

    <!-- 统计指定日期范围内每天的活跃用户数 -->
    <select id="countActiveUsersByDateRange" resultType="map">
        SELECT 
            active_date as date,
            COUNT(DISTINCT username) as activeCount
        FROM t_user_active 
        WHERE active_date BETWEEN #{startDate} AND #{endDate}
        GROUP BY active_date
        ORDER BY active_date
    </select>

</mapper>
