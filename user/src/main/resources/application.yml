server:
  port: 8009
  tomcat:
    connection-timeout: 20000
spring:
  application:
    name: user-service
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  data:
    redis:
      database: 0
      port: 6379
      host: localhost
      timeout: 10000
      client-type: lettuce
      lettuce:
        pool:
          # 连接池最大连接数（使用负值表示没有限制） 默认 8
          max-active: 20
          # 连接池中的最大空闲连接 默认 8
          max-idle: 10
          # 连接池中的最小空闲连接 默认 0
          min-idle: 3
          # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
          max-wait: 1s
  datasource:
    driver-class-name: org.postgresql.Driver
    url: *******************************************
    username: postgres
    password: root
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848

mybatis-plus:
  ddl:
    application-runner-enabled: false
  mapper-locations: classpath*:/mapper/*Mapper.xml
  #实体扫描，多个package用逗号或者分号分隔
  global-config:
    db-config:
      id-type: auto # 或者 assign_id, input, uuid, id_worker, snowflake 等其他策略
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl # ??SQL??
pagehelper:
  helper-dialect: postgresql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql
logging:
  config: classpath:log4j2.xml
application:
  security:
    jwt:
      header: Authorization
      secret-key: UmVhbGx5U2VjcmV0U2VjdXJpdHlLZXlWb2x1bWVVbmljb2RlZEF0QmVzdA==
      #token过期时间 单位是秒
      expiration: 604800
      #app token过期时间 单位是秒
      appExpiration: 604800
      wxExpiration: 604800
      tokenHead: Bearer
      tokenType: Bearer
      refresh-token:
        expiration: 86400
    internal-services:
      - gateway-service
      - agent-service
      - chat-service
    public-paths:
      - "/wsapi/**"
      - "/api/v1/auth/login/**"
      - "/api/v1/auth/organize/tree/username/**"
      - "/api/v1/auth/users/find_all"
      - "/api/v1/auth/users/get_from_username"
      - "/api/v1/auth/users/find_all_base"
      - "/api/v1/auth/users/user_statistics"
      - "/api/v1/auth/users/count_new_users"
      - "/api/v1/auth/users/find_by_usernames"
      - "/v2/api-docs"
      - "/v3/api-docs"
      - "/v3/api-docs/**"
      - "/swagger-resources"
      - "/swagger-resources/**"
      - "/api/v1/user/swagger-ui/**"
      - "/api/v1/user/swagger-ui.html"
    cors:
      exposed-headers:
        - "Authorization"
      allow-credentials: false
      max-age: 3600
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
    path: /api/v1/user/swagger-ui.html
  paths-to-match:
cache:
  prefix: lingxi
  configs:
    - name: user
      expireAfterWrite: 10m
    - name: department
      expireAfterWrite: 720h
    - name: role
      expireAfterWrite: 720h
    - name: captchaCache
      expireAfterWrite: 1m
    - name: token
      expireAfterWrite: 720h
    - name: organize
      expireAfterWrite: 720h
    - name: userActive
      expireAfterWrite: 24h
    - name: userActiveStats
      expireAfterWrite: 1h
wx:
  miniapp:
    appid: ${WX_MINIAPP_APPID:wxe96c29e240e54ff0} # 从环境变量获取，如果没有则使用默认值
    secret: ${WX_MINIAPP_SECRET:7b9a54bc3bf8070c34be5cb184602561} # 从环境变量获取，如果没有则使用默认值
