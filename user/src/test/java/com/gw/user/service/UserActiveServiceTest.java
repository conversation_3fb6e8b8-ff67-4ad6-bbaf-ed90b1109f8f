package com.gw.user.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
class UserActiveServiceTest {

    @Autowired
    private UserActiveService userActiveService;

    @Test
    void testRecordUserActive() {
        String username = "testuser";
        
        // 第一次记录应该成功
        boolean result1 = userActiveService.recordUserActive(username);
        assertTrue(result1, "第一次记录用户活跃应该成功");
        
        // 同一天再次记录应该返回false（已存在）
        boolean result2 = userActiveService.recordUserActive(username);
        assertFalse(result2, "同一天重复记录用户活跃应该返回false");
    }

    @Test
    void testRecordUserActiveWithDate() {
        String username = "testuser2";
        LocalDate testDate = LocalDate.of(2024, 1, 1);
        
        // 指定日期记录应该成功
        boolean result = userActiveService.recordUserActive(username, testDate);
        assertTrue(result, "指定日期记录用户活跃应该成功");
    }

    @Test
    void testCountTodayActiveUsers() {
        // 记录一些用户活跃
        userActiveService.recordUserActive("user1");
        userActiveService.recordUserActive("user2");
        userActiveService.recordUserActive("user3");
        
        int count = userActiveService.countTodayActiveUsers();
        assertTrue(count >= 3, "今日活跃用户数应该至少为3");
    }

    @Test
    void testCountActiveUsersByDate() {
        LocalDate testDate = LocalDate.of(2024, 2, 1);
        userActiveService.recordUserActive("dateuser1", testDate);
        userActiveService.recordUserActive("dateuser2", testDate);
        
        int count = userActiveService.countActiveUsersByDate(testDate);
        assertTrue(count >= 2, "指定日期活跃用户数应该至少为2");
    }

    @Test
    void testCountCurrentMonthActiveUsers() {
        int count = userActiveService.countCurrentMonthActiveUsers();
        assertTrue(count >= 0, "当月活跃用户数应该大于等于0");
    }

    @Test
    void testCountCurrentYearActiveUsers() {
        int count = userActiveService.countCurrentYearActiveUsers();
        assertTrue(count >= 0, "当年活跃用户数应该大于等于0");
    }

    @Test
    void testCountActiveUsersInPast30Days() {
        List<Map<String, Object>> result = userActiveService.countActiveUsersInPast30Days();
        assertNotNull(result, "过去30天活跃用户统计结果不应为null");
    }

    @Test
    void testCountActiveUsersByDateRange() {
        LocalDate startDate = LocalDate.now().minusDays(7);
        LocalDate endDate = LocalDate.now();
        
        List<Map<String, Object>> result = userActiveService.countActiveUsersByDateRange(startDate, endDate);
        assertNotNull(result, "日期范围活跃用户统计结果不应为null");
    }

    @Test
    void testInvalidParameters() {
        // 测试空用户名
        boolean result = userActiveService.recordUserActive("");
        assertFalse(result, "空用户名应该返回false");
        
        // 测试null用户名
        boolean result2 = userActiveService.recordUserActive(null);
        assertFalse(result2, "null用户名应该返回false");
        
        // 测试无效日期范围
        assertThrows(IllegalArgumentException.class, () -> {
            userActiveService.countActiveUsersByDateRange(LocalDate.now(), LocalDate.now().minusDays(1));
        }, "开始日期晚于结束日期应该抛出异常");
    }
}
