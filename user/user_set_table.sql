-- 用户表的创建
DROP TABLE IF EXISTS "public"."a_user";
DROP SEQUENCE IF EXISTS a_user_id_seq;
CREATE SEQUENCE a_user_id_seq START 1 CACHE 1;
CREATE TABLE a_user
(
    -- BaseEntity字段
    id            BIGINT PRIMARY KEY DEFAULT NEXTVAL('a_user_id_seq'::regclass),
    create_time   TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    update_time   TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted       INTEGER            DEFAULT 0,
    creator       VA<PERSON>HA<PERSON>(255),
    updater       VARCHAR(255),
    -- UserEntity字段
    username      VARCHAR(255) NOT NULL,
    password      VARCHAR(255) NOT NULL,
    real_name     VARCHAR(255),
    phone         VARCHAR(255),
    status        INTEGER            DEFAULT 1,
    remarks       VARCHAR(255),
    is_show       INTEGER            DEFAULT 1,
    is_root       INTEGER            DEFAULT 1,
    "modify"      BOOLEAN            DEFAULT FALSE,
    gender        INTEGER            DEFAULT 3,
    address       VA<PERSON>HA<PERSON>(255),
    nickname      VARCHAR(512),
    introduction  VARCHAR(2048),
    wx_open_id    VARCHAR(255),
    last_use_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    avatar        VARCHAR(255)
);
-- 添加表注释
COMMENT ON TABLE a_user IS '用户表';
COMMENT ON COLUMN a_user.id IS '主键ID';
COMMENT ON COLUMN a_user.create_time IS '创建时间';
COMMENT ON COLUMN a_user.update_time IS '更新时间';
COMMENT ON COLUMN a_user.deleted IS '删除标记(0:未删除,1:已删除)';
COMMENT ON COLUMN a_user.creator IS '创建者';
COMMENT ON COLUMN a_user.updater IS '更新者';
COMMENT ON COLUMN a_user.username IS '用户名';
COMMENT ON COLUMN a_user.password IS '密码';
COMMENT ON COLUMN a_user.real_name IS '真实姓名';
COMMENT ON COLUMN a_user.phone IS '手机号';
COMMENT ON COLUMN a_user.status IS '状态(1:正常,0:禁用)';
COMMENT ON COLUMN a_user.remarks IS '备注';
COMMENT ON COLUMN a_user.is_show IS '是否显示(1:显示,0:隐藏)';
COMMENT ON COLUMN a_user.wx_open_id IS '微信OpenID';
COMMENT ON COLUMN a_user.last_use_time IS '用户最后使用时间';
COMMENT ON COLUMN a_user.avatar IS '头像URL';
-- 创建索引
CREATE INDEX idx_user_username ON a_user (username) WHERE deleted = 0;
CREATE INDEX idx_user_phone ON a_user (phone) WHERE deleted = 0;
CREATE INDEX idx_user_status ON a_user (status) WHERE deleted = 0;
CREATE INDEX idx_user_wx_open_id ON a_user (wx_open_id) WHERE deleted = 0;

-- 用户活跃表
DROP TABLE IF EXISTS "public"."t_user_active";
DROP SEQUENCE IF EXISTS t_user_active_id_seq;

CREATE SEQUENCE t_user_active_id_seq START 1 CACHE 1;

CREATE TABLE t_user_active
(
    id          BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_user_active_id_seq'::regclass),
    username    VARCHAR(255) NOT NULL,
    active_date DATE         NOT NULL DEFAULT CURRENT_DATE
);

-- 添加表注释
COMMENT ON TABLE t_user_active IS '用户活跃表';
COMMENT ON COLUMN t_user_active.id IS '主键ID';
COMMENT ON COLUMN t_user_active.username IS '用户名';
COMMENT ON COLUMN t_user_active.active_date IS '活跃日期';

-- 创建索引
CREATE INDEX idx_user_active_username ON t_user_active (username);
CREATE INDEX idx_user_active_date ON t_user_active (active_date);
CREATE UNIQUE INDEX uk_user_active_username_date ON t_user_active (username, active_date);

--令牌表
DROP TABLE IF EXISTS "public"."a_token";
DROP SEQUENCE IF EXISTS a_token_id_seq;

CREATE SEQUENCE a_token_id_seq START 1 CACHE 1;

CREATE TABLE a_token
(
    id            BIGINT PRIMARY KEY DEFAULT NEXTVAL('a_token_id_seq'::regclass),
    access_token  VARCHAR(255),
    refresh_token VARCHAR(255),
    user_id       BIGINT,
    expired       BOOLEAN            DEFAULT false,
    revoked       BOOLEAN            DEFAULT false,
    device        VARCHAR(255)
);

-- 添加表注释
COMMENT ON TABLE a_token IS 'Token表';
COMMENT ON COLUMN a_token.id IS '主键ID';
COMMENT ON COLUMN a_token.access_token IS '访问令牌';
COMMENT ON COLUMN a_token.refresh_token IS '刷新令牌';
COMMENT ON COLUMN a_token.user_id IS '用户ID';
COMMENT ON COLUMN a_token.expired IS '是否过期';
COMMENT ON COLUMN a_token.revoked IS '是否撤销';
COMMENT ON COLUMN a_token.device IS '设备类型';
-- 角色表
DROP TABLE IF EXISTS "public"."a_role";
DROP SEQUENCE IF EXISTS a_role_id_seq;

CREATE SEQUENCE a_role_id_seq START 1 CACHE 1;
CREATE TABLE a_role
(
    -- BaseEntity字段
    id          BIGINT PRIMARY KEY DEFAULT NEXTVAL('a_role_id_seq'::regclass),
    create_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted     INTEGER            DEFAULT 0,
    creator     VARCHAR(255),
    updater     VARCHAR(255),

    -- RoleEntity特有字段
    name        VARCHAR(255) COLLATE "pg_catalog"."default" NOT NULL,
    sequence    INTEGER,
    remarks     VARCHAR(255),
    code        VARCHAR(255),
    status      INTEGER            DEFAULT 1,
    menus       TEXT,
    buttons     TEXT,
    is_show     INTEGER            DEFAULT 1,
    is_modify   INTEGER            DEFAULT 1
);

-- 添加字段注释
COMMENT ON TABLE a_role IS '角色表';
COMMENT ON COLUMN a_role.id IS '主键ID';
COMMENT ON COLUMN a_role.create_time IS '创建时间';
COMMENT ON COLUMN a_role.update_time IS '更新时间';
COMMENT ON COLUMN a_role.deleted IS '删除标记(0:未删除,1:已删除)';
COMMENT ON COLUMN a_role.creator IS '创建者';
COMMENT ON COLUMN a_role.updater IS '更新者';
COMMENT ON COLUMN a_role.name IS '角色名称';
COMMENT ON COLUMN a_role.sequence IS '排序号';
COMMENT ON COLUMN a_role.remarks IS '备注';
COMMENT ON COLUMN a_role.status IS '状态(1:正常)';
COMMENT ON COLUMN a_role.menus IS '菜单权限(逗号分隔)';
COMMENT ON COLUMN a_role.buttons IS '按钮权限(逗号分隔)';
COMMENT ON COLUMN a_role.is_show IS '是否显示(1:显示)';
COMMENT ON COLUMN a_role.is_modify IS '是否可修改(1:可修改)';

-- 用户角色关联表
DROP TABLE IF EXISTS "public"."a_user_role";
DROP SEQUENCE IF EXISTS a_user_role_id_seq;

CREATE SEQUENCE a_user_role_id_seq START 1 CACHE 1;
CREATE TABLE a_user_role
(
    id      BIGINT PRIMARY KEY DEFAULT NEXTVAL('a_user_role_id_seq'::regclass),
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL
);

-- 添加字段注释
COMMENT ON TABLE a_user_role IS '用户角色关联表';
COMMENT ON COLUMN a_user_role.id IS '主键ID';
COMMENT ON COLUMN a_user_role.user_id IS '用户ID';
COMMENT ON COLUMN a_user_role.role_id IS '角色ID';

-- 按钮权限表
DROP TABLE IF EXISTS "public"."a_button_permission";
DROP SEQUENCE IF EXISTS a_button_permission_id_seq;

CREATE SEQUENCE a_button_permission_id_seq START 1 CACHE 1;
CREATE TABLE a_button_permission
(
    -- BaseEntity字段
    id          BIGINT PRIMARY KEY DEFAULT NEXTVAL('a_button_permission_id_seq'::regclass),
    create_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted     INTEGER            DEFAULT 0,
    creator     VARCHAR(255),
    updater     VARCHAR(255),

    -- ButtonPermission特有字段
    code        VARCHAR(255) NOT NULL,       -- 按钮权限编码
    name        VARCHAR(255) NOT NULL,       -- 按钮权限名称
    url_pattern VARCHAR(255) NOT NULL,       -- 请求URL模式
    method      VARCHAR(20),                 -- HTTP方法(GET,POST等)
    remarks     VARCHAR(255),                -- 备注说明
    status      INTEGER            DEFAULT 1 -- 状态(1:正常)

);


-- 添加表注释
COMMENT ON TABLE a_button_permission IS '按钮权限表';
COMMENT ON COLUMN a_button_permission.code IS '按钮权限编码';
COMMENT ON COLUMN a_button_permission.name IS '按钮权限名称';
COMMENT ON COLUMN a_button_permission.url_pattern IS '请求URL模式';
COMMENT ON COLUMN a_button_permission.method IS 'HTTP方法';
COMMENT ON COLUMN a_button_permission.remarks IS '备注说明';
COMMENT ON COLUMN a_button_permission.status IS '状态(1:正常)';

--menu
DROP TABLE IF EXISTS "public"."a_menu";
DROP SEQUENCE IF EXISTS a_menu_id_seq;

CREATE SEQUENCE a_menu_id_seq START 1 CACHE 1;

CREATE TABLE a_menu
(
    -- BaseEntity字段
    id          BIGINT PRIMARY KEY DEFAULT NEXTVAL('a_menu_id_seq'::regclass),
    create_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted     INTEGER            DEFAULT 0,
    creator     VARCHAR(255),
    updater     VARCHAR(255),

    -- MenuEntity特有字段
    parent_id   BIGINT,
    name        VARCHAR(255) NOT NULL,
    code        VARCHAR(255),
    path        VARCHAR(255),
    sequence    INTEGER,
    icon        VARCHAR(255),
    type        INTEGER,
    status      INTEGER            DEFAULT 1
);

-- 添加表注释
COMMENT ON TABLE a_menu IS '菜单表';
COMMENT ON COLUMN a_menu.id IS '主键ID';
COMMENT ON COLUMN a_menu.create_time IS '创建时间';
COMMENT ON COLUMN a_menu.update_time IS '更新时间';
COMMENT ON COLUMN a_menu.deleted IS '删除标记(0:未删除,1:已删除)';
COMMENT ON COLUMN a_menu.creator IS '创建者';
COMMENT ON COLUMN a_menu.updater IS '更新者';
COMMENT ON COLUMN a_menu.parent_id IS '父级菜单ID';
COMMENT ON COLUMN a_menu.name IS '菜单名称';
COMMENT ON COLUMN a_menu.code IS '菜单编码';
COMMENT ON COLUMN a_menu.path IS '菜单路径';
COMMENT ON COLUMN a_menu.sequence IS '排序号';
COMMENT ON COLUMN a_menu.icon IS '图标';
COMMENT ON COLUMN a_menu.type IS '菜单类型';
COMMENT ON COLUMN a_menu.status IS '状态(1:正常)';

--button表
DROP TABLE IF EXISTS "public"."a_button";
DROP SEQUENCE IF EXISTS a_button_id_seq;

CREATE SEQUENCE a_button_id_seq START 1 CACHE 1;

CREATE TABLE a_button
(
    -- BaseEntity字段
    id          BIGINT PRIMARY KEY DEFAULT NEXTVAL('a_button_id_seq'::regclass),
    create_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted     INTEGER            DEFAULT 0,
    creator     VARCHAR(255),
    updater     VARCHAR(255),

    -- ButtonEntity特有字段
    menu_id     BIGINT,
    name        VARCHAR(255) NOT NULL,
    code        VARCHAR(255) NOT NULL,
    sequence    INTEGER,
    status      INTEGER            DEFAULT 1
);

-- 添加表注释
COMMENT ON TABLE a_button IS '按钮表';
COMMENT ON COLUMN a_button.id IS '主键ID';
COMMENT ON COLUMN a_button.create_time IS '创建时间';
COMMENT ON COLUMN a_button.update_time IS '更新时间';
COMMENT ON COLUMN a_button.deleted IS '删除标记(0:未删除,1:已删除)';
COMMENT ON COLUMN a_button.creator IS '创建者';
COMMENT ON COLUMN a_button.updater IS '更新者';
COMMENT ON COLUMN a_button.menu_id IS '菜单ID';
COMMENT ON COLUMN a_button.name IS '按钮名称';
COMMENT ON COLUMN a_button.code IS '按钮编码';
COMMENT ON COLUMN a_button.sequence IS '排序号';
COMMENT ON COLUMN a_button.status IS '状态(1:正常)';

