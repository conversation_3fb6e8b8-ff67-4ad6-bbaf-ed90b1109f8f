-- 创建邀请表
CREATE TABLE IF NOT EXISTS t_invitation
(
    id            BIGSERIAL PRIMARY KEY,
    invite_code   VARCHAR(20) NOT NULL COMMENT '邀请码',
    inviter_id    BIGINT      NOT NULL COMMENT '邀请人ID',
    invitee_id    BIGINT COMMENT '被邀请人ID',
    status        INT                  DEFAULT 0 COMMENT '邀请状态：0-未使用，1-已使用',
    reward_status INT                  DEFAULT 0 COMMENT '奖励状态：0-未发放，1-已发放',
    create_time   TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    use_time      TIMESTAMP COMMENT '使用时间',
    UNIQUE KEY uk_invite_code (invite_code)
) COMMENT='邀请记录表';

-- 创建索引
CREATE INDEX idx_inviter_id ON t_invitation (inviter_id);
CREATE INDEX idx_invitee_id ON t_invitation (invitee_id); 