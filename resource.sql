-- User module resource
INSERT INTO a_resource (name, path, permission_code)
VALUES ('创建用户', '/api/v1/auth/users', 'user:create');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('更新用户', '/api/v1/auth/users/update', 'user:update');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取个人信息', '/api/v1/auth/users/my/get', 'user:my:get');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('完善个人信息', '/api/v1/auth/users/my/supplement', 'user:my:supplement');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('删除用户', '/api/v1/auth/users/delete', 'user:delete');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('修改密码', '/api/v1/auth/users/change_password', 'user:change_password');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('修改自己的密码', '/api/v1/auth/users/change_self_password', 'user:change_self_password');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('启用用户', '/api/v1/auth/users/enable', 'user:enable');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('禁用用户', '/api/v1/auth/users/disable', 'user:disable');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('查询用户', '/api/v1/auth/users/get', 'user:get');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('按用户名查询用户', '/api/v1/auth/users/get_from_username', 'user:get_from_username');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('根据token查询用户', '/api/v1/auth/users/get_from_token', 'user:get_from_token');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('用户上传图片', '/api/v1/auth/users/file/upload', 'user:file:upload');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('用户分页查询', '/api/v1/auth/users/page', 'user:page');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('app用户分页查询', '/api/v1/auth/users/app_page', 'user:app_page');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('查询所有App用户', '/api/v1/auth/users/all_app', 'user:all_app');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('查询所有用户', '/api/v1/auth/users/all', 'user:all');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('查询基础用户信息', '/api/v1/auth/users/find_all_base', 'user:find_all_base');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('用户拼音排序查询', '/api/v1/auth/users/grouped-by-pinyin', 'user:grouped_by_pinyin');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取用户统计', '/api/v1/auth/users/user_statistics', 'user:user_statistics');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('统计新增用户数', '/api/v1/auth/users/count_new_users', 'user:count_new_users');

-- 认证模块资源
INSERT INTO a_resource (name, path, permission_code)
VALUES ('验证Token', '/api/v1/auth/validate', 'auth:validate');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('检查权限', '/api/v1/auth/check-permission', 'auth:check_permission');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取当前用户菜单树', '/api/v1/auth/current/menutree', 'auth:current:menutree');

-- 登录模块资源
INSERT INTO a_resource (name, path, permission_code)
VALUES ('网页登录', '/api/v1/auth/login', 'login:web');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('App登录', '/api/v1/auth/login/app', 'login:app');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('微信登录', '/api/v1/auth/login/wx', 'login:wx');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取验证码ID', '/api/v1/auth/login/captchaid', 'login:captchaid');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取验证码', '/api/v1/auth/login/captcha', 'login:captcha');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('微信小程序登录', '/api/v1/auth/login/wx/miniapp', 'login:wx:miniapp');

-- 注册模块资源
INSERT INTO a_resource (name, path, permission_code)
VALUES ('用户注册', '/api/v1/register', 'register:user');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('微信注册', '/api/v1/register/wx', 'register:wx');

-- 邀请模块资源
INSERT INTO a_resource (name, path, permission_code)
VALUES ('生成邀请码', '/api/v1/invitation/code', 'invitation:code:generate');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取邀请记录', '/api/v1/invitation/records', 'invitation:records');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('验证邀请码', '/api/v1/invitation/validate', 'invitation:validate');

-- 角色模块资源
INSERT INTO a_resource (name, path, permission_code)
VALUES ('创建角色', '/api/v1/auth/roles', 'role:create');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('更新角色', '/api/v1/auth/roles/update', 'role:update');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('角色授权', '/api/v1/auth/roles/auth', 'role:auth');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('启用角色', '/api/v1/auth/roles/enable', 'role:enable');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('禁用角色', '/api/v1/auth/roles/disable', 'role:disable');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('删除角色', '/api/v1/auth/roles/delete', 'role:delete');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取角色详情', '/api/v1/auth/roles/get', 'role:get');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取所有角色', '/api/v1/auth/roles/list', 'role:list');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('角色分页查询', '/api/v1/auth/roles/page', 'role:page');

-- 菜单模块资源
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取菜单树', '/api/v1/auth/menu/tree-display', 'menu:tree');

-- 按钮权限模块资源
INSERT INTO a_resource (name, path, permission_code)
VALUES ('创建按钮权限', '/api/v1/auth/button-permission', 'button:create');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('更新按钮权限', '/api/v1/auth/button-permission/update', 'button:update');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('删除按钮权限', '/api/v1/auth/button-permission/delete', 'button:delete');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取按钮权限列表', '/api/v1/auth/button-permission/list', 'button:list');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取按钮权限详情', '/api/v1/auth/button-permission/get', 'button:get');

-- 智能体管理相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('后台创建智能体', '/api/v1/agent/admin', 'agent:admin:create');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('创建智能体', '/api/v1/agent', 'agent:create');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('后台更新智能体', '/api/v1/agent/admin/update', 'agent:admin:update');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('更新智能体', '/api/v1/agent/update', 'agent:update');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('更新智能体头像', '/api/v1/agent/update_avatar', 'agent:update_avatar');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('更新智能体背景', '/api/v1/agent/update_bg', 'agent:update_bg');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('更新我的智能体背景', '/api/v1/agent/my/update_bg', 'agent:my:update_bg');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('内部获取智能体详情', '/api/v1/agent/internal/get', 'agent:internal:get');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('内部获取智能体详情根据IDS', '/api/v1/agent/internal/get_by_ids', 'agent:internal:get_by_ids');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('后台获取智能体详情', '/api/v1/agent/admin/get', 'agent:admin:get');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取智能体详情', '/api/v1/agent/get', 'agent:get');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取智能体表详情', '/api/v1/agent/get_entity', 'agent:get_entity');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('修改上下架状态', '/api/v1/agent/admin/modify/shelf_status', 'agent:admin:modify:shelf_status');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('设置智能体为专属', '/api/v1/agent/set_exclusive', 'agent:set_exclusive');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获得专属智能体', '/api/v1/agent/exclusive', 'agent:exclusive');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获得专属智能体ID', '/api/v1/agent/internal/exclusive_id', 'agent:internal:exclusive_id');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('删除智能体', '/api/v1/agent/delete', 'agent:delete');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('删除我的智能体', '/api/v1/agent/my/delete', 'agent:my:delete');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取当前用户的智能体', '/api/v1/agent/my', 'agent:my');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取当前用户的智能体推荐', '/api/v1/agent/my_recommend', 'agent:my_recommend');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('分页获取当前用户的智能体', '/api/v1/agent/my/page', 'agent:my:page');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('后台分页获取智能体', '/api/v1/agent/admin/page', 'agent:admin:page');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('App分页获取智能体', '/api/v1/agent/app/page', 'agent:app:page');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('分页获取智能体', '/api/v1/agent/page', 'agent:page');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('分页获取公开智能体', '/api/v1/agent/public/page', 'agent:public:page');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('列举支持的音色', '/api/v1/agent/list_voice', 'agent:list_voice');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('上传文件', '/api/v1/agent/file/upload', 'agent:file:upload');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('分页获取用户推荐智能体', '/api/v1/agent/recommend/my/page', 'agent:recommend:my:page');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('分页获取系统推荐智能体', '/api/v1/agent/recommend/system/page', 'agent:recommend:system:page');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获得用户和智能体的互动统计', '/api/v1/agent/internal/user_interaction', 'agent:internal:user_interaction');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('分页获取用户创建的智能体', '/api/v1/agent/create/my/creat', 'agent:create:my:creat');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('分页获取用户点赞的智能体', '/api/v1/agent/create/my/like', 'agent:create:my:like');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('分页获取用户收藏的智能体', '/api/v1/agent/create/my/favorite', 'agent:create:my:favorite');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('分页获取用户评论的智能体', '/api/v1/agent/create/my/comment', 'agent:create:my:comment');

-- 智能体工具相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取文生图模型', '/api/v1/agent/tools/image_model', 'agent:tools:image_model');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('AI优化提示词', '/api/v1/agent/tools/prompt', 'agent:tools:prompt');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('AI文生图提示词优化', '/api/v1/agent/tools/ai_text_to_image_prompt', 'agent:tools:ai_text_to_image_prompt');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('AI文生图', '/api/v1/agent/tools/image', 'agent:tools:image');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('后台AI文生图', '/api/v1/agent/tools/admin/image', 'agent:tools:admin:image');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('检查内容是否违规', '/api/v1/agent/tools/check_content', 'agent:tools:check_content');

-- 智能体热搜相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('智能体搜索', '/api/v1/agent/search-rank/search', 'agent:search-rank:search');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('删除搜索项', '/api/v1/agent/search-rank/delete', 'agent:search-rank:delete');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('删除我的所有搜索项', '/api/v1/agent/search-rank/delete_all_my', 'agent:search-rank:delete_all_my');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取用户搜索排行榜', '/api/v1/agent/search-rank/personal/rank', 'agent:search-rank:personal:rank');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取全局搜索排行榜', '/api/v1/agent/search-rank/global/rank', 'agent:search-rank:global:rank');

-- 智能体设置相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('保存智能体设置', '/api/v1/agent/setting/save', 'agent:setting:save');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取我的智能体设置', '/api/v1/agent/setting/get_setting', 'agent:setting:get_setting');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取智能体设置根据用户名', '/api/v1/agent/setting/get_username', 'agent:setting:get_username');

-- 智能体扣子相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取扣子配置', '/api/v1/agent/coze/setting', 'agent:coze:setting');

-- 缓存管理相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取缓存信息', '/api/v1/agent/cache/info', 'agent:cache:info');

-- 智能体使用记录相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('记录用户使用智能体', '/api/v1/agent/usage/record', 'agent:usage:record');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取智能体使用人数', '/api/v1/agent/usage/user-count', 'agent:usage:user-count');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取智能体使用信息', '/api/v1/agent/usage/info', 'agent:usage:info');

-- 用户喜好设置相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('保存用户喜好设置', '/api/v1/agent/preference/save', 'agent:preference:save');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取用户喜好设置', '/api/v1/agent/preference/get', 'agent:preference:get');

-- 智能体兴趣相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取智能体兴趣', '/api/v1/agent/interest/get', 'agent:interest:get');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('保存智能体兴趣', '/api/v1/agent/interest/save', 'agent:interest:save');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('删除智能体兴趣', '/api/v1/agent/interest/delete', 'agent:interest:delete');

-- 统计管理相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取平台统计数据', '/api/v1/agent/statistics/platform', 'agent:statistics:platform');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取智能体类型统计', '/api/v1/agent/statistics/agent-types', 'agent:statistics:agent-types');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取月度统计数据', '/api/v1/agent/statistics/monthly', 'agent:statistics:monthly');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取日度统计数据', '/api/v1/agent/statistics/daily', 'agent:statistics:daily');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取月度日度统计数据', '/api/v1/agent/statistics/monthly-daily', 'agent:statistics:monthly-daily');

-- 智能体标签管理相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('创建标签', '/api/v1/agent/tags', 'agent:tags:create');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('更新标签', '/api/v1/agent/tags/update', 'agent:tags:update');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('删除标签', '/api/v1/agent/tags/delete', 'agent:tags:delete');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取所有标签', '/api/v1/agent/tags/list_all', 'agent:tags:list_all');

-- 智能体类型管理相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('创建类型', '/api/v1/agent/types', 'agent:types:create');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('更新类型', '/api/v1/agent/types/update', 'agent:types:update');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('删除类型', '/api/v1/agent/types/delete', 'agent:types:delete');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取所有类型', '/api/v1/agent/types/list_all', 'agent:types:list_all');

-- 智能体点赞相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('添加点赞', '/api/v1/agent/likes/add', 'agent:likes:add');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('取消点赞', '/api/v1/agent/likes/remove', 'agent:likes:remove');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取用户点赞的智能体', '/api/v1/agent/likes/my', 'agent:likes:my');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('检查智能体是否被点赞', '/api/v1/agent/likes/check', 'agent:likes:check');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取智能体点赞数', '/api/v1/agent/likes/count', 'agent:likes:count');

-- 智能体评论相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('创建评论', '/api/v1/agent/comments/add', 'agent:comments:add');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('删除评论', '/api/v1/agent/comments/delete', 'agent:comments:delete');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取智能体评论列表', '/api/v1/agent/comments/list', 'agent:comments:list');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取评论回复列表', '/api/v1/agent/comments/replies', 'agent:comments:replies');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取我的评论列表', '/api/v1/agent/comments/my', 'agent:comments:my');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取智能体评论数', '/api/v1/agent/comments/count', 'agent:comments:count');

-- 智能体收藏相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('添加收藏', '/api/v1/agent/favorites/add', 'agent:favorites:add');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('取消收藏', '/api/v1/agent/favorites/remove', 'agent:favorites:remove');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取用户收藏的智能体', '/api/v1/agent/favorites/my', 'agent:favorites:my');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('检查智能体是否被收藏', '/api/v1/agent/favorites/check', 'agent:favorites:check');

-- 聊天管理相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取会话信息', '/api/v1/chat/session/get', 'chat:session:get');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('分页获取当前用户会话列表', '/api/v1/chat/sessions', 'chat:sessions');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取我的智能体的对话历史', '/api/v1/chat/messages/history', 'chat:messages:history');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取会话最近的消息', '/api/v1/chat/messages/recent', 'chat:messages:recent');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取会话的问题建议', '/api/v1/chat/chat/followup', 'chat:chat:followup');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('置顶会话', '/api/v1/chat/chat/session_topping', 'chat:chat:session_topping');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取用户与指定智能体的最近会话', '/api/v1/chat/session/latest', 'chat:session:latest');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取智能体会话的统计数据', '/api/v1/chat/agent/stats', 'chat:agent:stats');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('文本转语音', '/api/v1/chat/text_convert_speech', 'chat:text_convert_speech');

-- 会话评论相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('创建评论', '/api/v1/chat/comments/create', 'chat:comments:create');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('回复评论', '/api/v1/chat/comments/reply', 'chat:comments:reply');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('删除评论', '/api/v1/chat/comments/delete', 'chat:comments:delete');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取评论详情', '/api/v1/chat/comments/get', 'chat:comments:get');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取评论及回复', '/api/v1/chat/comments/get-with-replies', 'chat:comments:get-with-replies');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取会话评论列表', '/api/v1/chat/comments/by-conversation', 'chat:comments:by-conversation');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取用户评论列表', '/api/v1/chat/comments/by-user', 'chat:comments:by-user');

-- 聊天评论回复相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取评论的回复', '/api/v1/chat/comment_replies/by-comment', 'chat:comment_replies:by-comment');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取用户的评论回复', '/api/v1/chat/comment_replies/by-user', 'chat:comment_replies:by-user');

-- 会话点赞相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('点赞/取消点赞会话', '/api/v1/chat/likes/action', 'chat:likes:action');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取会话点赞统计', '/api/v1/chat/likes/stats', 'chat:likes:stats');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取会话点赞列表', '/api/v1/chat/likes/by-conversation', 'chat:likes:by-conversation');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取用户点赞列表', '/api/v1/chat/likes/by-user', 'chat:likes:by-user');

-- 会员服务接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取我的会员信息', '/api/v1/membership/my', 'membership:my');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('根据用户名查询会员信息', '/api/v1/membership/internal/query_by_username',
        'membership:internal:query_by_username');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('根据用户名查询会员基础信息', '/api/v1/membership/internal/query_base_by_username',
        'membership:internal:query_base_by_username');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('检查会员权益', '/api/v1/membership/my_benefits/check', 'membership:my_benefits:check');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('新用户注册赠会员', '/api/v1/membership/new_user_reg', 'membership:new_user_reg');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('后台赠送会员', '/api/v1/membership/admin/gift', 'membership:admin:gift');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('增加会员积分', '/api/v1/membership/points/add', 'membership:points:add');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('消费会员积分', '/api/v1/membership/points/consume', 'membership:points:consume');

-- 会员统计相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('计算指定时间范围内的收款金额', '/api/v1/membership/statistics/income', 'membership:statistics:income');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('统计VIP用户总数', '/api/v1/membership/statistics/vip-count', 'membership:statistics:vip-count');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('计算历史收款总金额', '/api/v1/membership/statistics/total-income', 'membership:statistics:total-income');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取会员统计数据', '/api/v1/membership/statistics/summary', 'membership:statistics:summary');

-- 会员积分历史相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取积分历史', '/api/v1/membership/points_history/my', 'membership:points_history:my');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('分页获取积分历史', '/api/v1/membership/points_history/my/page', 'membership:points_history:my:page');

-- 会员历史记录相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取我的会员历史记录', '/api/v1/membership/history/my', 'membership:history:my');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('分页获取我的会员历史记录', '/api/v1/membership/history/my/page', 'membership:history:my:page');

-- 后台设置会员折扣相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('创建会员折扣', '/api/v1/membership/admin/discount', 'membership:admin:discount:create');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('更新会员折扣', '/api/v1/membership/admin/discount/update', 'membership:admin:discount:update');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('删除会员折扣', '/api/v1/membership/admin/discount/delete', 'membership:admin:discount:delete');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('查询所有有效会员折扣', '/api/v1/membership/admin/discount/all_valid', 'membership:admin:discount:all_valid');

-- 后台会员权益服务相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('创建会员权益', '/api/v1/membership/admin/benefit', 'membership:admin:benefit:create');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('更新会员权益', '/api/v1/membership/admin/benefit/update', 'membership:admin:benefit:update');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('删除会员权益', '/api/v1/membership/admin/benefit/delete', 'membership:admin:benefit:delete');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('查询所有会员权益', '/api/v1/membership/admin/benefit/all', 'membership:admin:benefit:all');

-- 奖励配置相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('根据编码获取奖励配置', '/api/v1/membership/reward/config/get_by_code', 'membership:reward:config:get_by_code');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('更新奖励配置', '/api/v1/membership/reward/config/update', 'membership:reward:config:update');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取所有奖励配置', '/api/v1/membership/reward/config/list', 'membership:reward:config:list');

-- 会员套餐相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('创建会员套餐', '/api/v1/membership/packages', 'membership:packages:create');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('更新会员套餐', '/api/v1/membership/packages/update', 'membership:packages:update');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('删除会员套餐', '/api/v1/membership/packages/delete', 'membership:packages:delete');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取会员套餐详情', '/api/v1/membership/packages/get', 'membership:packages:get');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取所有会员套餐', '/api/v1/membership/packages/get_all', 'membership:packages:get_all');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取所有上架的会员套餐', '/api/v1/membership/packages/active', 'membership:packages:active');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取推荐的会员套餐', '/api/v1/membership/packages/recommended', 'membership:packages:recommended');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('根据类型获取会员套餐', '/api/v1/membership/packages/get-by-type', 'membership:packages:get-by-type');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('更新套餐状态', '/api/v1/membership/packages/update-status', 'membership:packages:update-status');

-- 会员订单相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('创建会员订单', '/api/v1/membership/orders', 'membership:orders:create');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取订单详情', '/api/v1/membership/orders/get', 'membership:orders:get');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('后台使能订单', '/api/v1/membership/orders/active', 'membership:orders:active');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取我的用户订单列表', '/api/v1/membership/orders/page/my', 'membership:orders:page:my');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('后台获取用户订单列表', '/api/v1/membership/orders/admin/page', 'membership:orders:admin:page');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取我的用户订单支出明细列表', '/api/v1/membership/orders/expenses/page/my',
        'membership:orders:expenses:page:my');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('为订单生成微信小程序支付参数', '/api/v1/membership/orders/wxpay', 'membership:orders:wxpay');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('微信支付回调通知', '/api/v1/membership/orders/wxpay/notify', 'membership:orders:wxpay:notify');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取微信H5支付二维码URL', '/api/v1/membership/orders/wxpay/qrcode', 'membership:orders:wxpay:qrcode');

-- 邀请相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取邀请码', '/api/v1/membership/invitation/code', 'membership:invitation:code');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('根据用户名获取邀请码', '/api/v1/membership/invitation/code/code_by_username',
        'membership:invitation:code_by_username');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取用户邀请码', '/api/v1/membership/invitation/get_code', 'membership:invitation:get_code');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('验证邀请码', '/api/v1/membership/invitation/code/validate', 'membership:invitation:code:validate');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('使用邀请码', '/api/v1/membership/invitation/code/use', 'membership:invitation:code:use');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('分页获取我的邀请记录', '/api/v1/membership/invitation/my/page', 'membership:invitation:my:page');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('分页获取邀请记录', '/api/v1/membership/invitation/admin/page', 'membership:invitation:admin:page');

-- 系统消息管理相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('创建系统消息', '/api/v1/notify/system_message', 'notify:system_message:create');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('服务自动创建系统消息', '/api/v1/notify/system_message/auto_create', 'notify:system_message:auto_create');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('更新系统消息', '/api/v1/notify/system_message/update', 'notify:system_message:update');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('标记消息为已读', '/api/v1/notify/system_message/my_mark_read', 'notify:system_message:my_mark_read');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('标记我的所有消息为已读', '/api/v1/notify/system_message/my_mark_all_read',
        'notify:system_message:my_mark_all_read');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('删除系统消息', '/api/v1/notify/system_message/delete', 'notify:system_message:delete');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取我的系统消息详情', '/api/v1/notify/system_message/get', 'notify:system_message:get');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取当前用户的有效系统消息', '/api/v1/notify/system_message/my_valid', 'notify:system_message:my_valid');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('分页获取系统消息', '/api/v1/notify/system_message/public/page', 'notify:system_message:public:page');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('后台分页获取系统消息', '/api/v1/notify/system_message/admin/page', 'notify:system_message:admin:page');

-- 互动消息管理相关接口
INSERT INTO a_resource (name, path, permission_code)
VALUES ('创建互动消息', '/api/v1/notify/interactive_message', 'notify:interactive_message:create');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('服务自动创建互动消息', '/api/v1/notify/interactive_message/auto_create',
        'notify:interactive_message:auto_create');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('更新互动消息', '/api/v1/notify/interactive_message/update', 'notify:interactive_message:update');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('标记互动消息为已读', '/api/v1/notify/interactive_message/my_mark_read',
        'notify:interactive_message:my_mark_read');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('标记我的所有互动消息为已读', '/api/v1/notify/interactive_message/my_mark_all_read',
        'notify:interactive_message:my_mark_all_read');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('删除互动消息', '/api/v1/notify/interactive_message/delete', 'notify:interactive_message:delete');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取我的互动消息详情', '/api/v1/notify/interactive_message/get', 'notify:interactive_message:get');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('获取当前用户的有效互动消息', '/api/v1/notify/interactive_message/my_valid',
        'notify:interactive_message:my_valid');
INSERT INTO a_resource (name, path, permission_code)
VALUES ('分页获取互动消息', '/api/v1/notify/interactive_message/page', 'notify:interactive_message:page');