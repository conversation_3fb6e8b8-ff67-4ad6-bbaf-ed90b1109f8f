package com.gw.common.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.time.LocalDateTime;

import static com.baomidou.mybatisplus.annotation.IdType.AUTO;

@Data
public class BaseEntity {
    @TableId(type = AUTO)
    private Long id;
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime = LocalDateTime.now();

    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime = LocalDateTime.now();
    @TableLogic
    private Integer deleted = 0;
    private String creator;
    private String updater;
    @TableField(exist = false)
    private String creatorName;
    @TableField(exist = false)
    private String updaterName;
}
