package com.gw.common.agent.client;

import com.gw.common.agent.dto.*;
import com.gw.common.agent.vo.*;
import com.gw.common.dto.ItemIdDTO;
import com.gw.common.dto.ResponseResult;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * AI代理服务客户端
 */
@FeignClient(name = "agent-service")
public interface AgentClient {

    @PostMapping("/api/v1/agent/internal/get")
    ResponseResult<AgentBaseVO> getAgentInfo(@RequestBody ItemIdDTO req);

    @GetMapping("/api/v1/agent/coze/setting")
    ResponseResult<CozeSettingVO> getCozeSetting();

    @PostMapping("/api/v1/agent/setting/get_username")
    ResponseResult<MyAgentSettingVO> getMyAgentSetting(AgentSettingQueryDTO query);

    @PostMapping("/api/v1/agent/internal/get_by_ids")
    ResponseResult<List<AgentBaseVO>> getAgentsByIds(@RequestBody AgentQueryByIdsDTO req);

    @PostMapping("/api/v1/agent/internal/user_interaction")
    ResponseResult<UserInteractionAgentVO> getUserInteraction(@RequestBody UserInteractionAgentQuery req);

    @GetMapping("/api/v1/agent/internal/exclusive_id")
    ResponseResult<Long> findFirstIdByExclusive();

    @PostMapping("/api/v1/agent/usage/record")
    boolean recordAgentUsage(@RequestParam("agentId") Long agentId, @RequestParam("username") String username);

    /**
     * 获取智能体的使用人数
     *
     * @param agentId 智能体ID
     * @return 使用人数
     */
    @GetMapping("/api/v1/agent/usage/user-count")
    int getAgentUserCount(@RequestParam("agentId") Long agentId);

    @PostMapping("/api/v1/agent/story/internal/get")
    ResponseResult<AgentStoryBaseVO> getStoryInfo(@RequestBody ItemIdDTO req);

    @PostMapping("/api/v1/agent/story/internal/get_by_ids")
    ResponseResult<List<AgentStoryBaseVO>> getStoriesByIds(@RequestBody StoryQueryByIdsDTO req);

    @PostMapping("/api/v1/agent/story/internal/story_interaction")
    ResponseResult<UserInteractionStoryVO> getStoryInteraction(@RequestBody UserInteractionStoryQuery req);


    @PostMapping("/api/v1/agent/story/usage/record")
    boolean recordStoryUsage(@RequestParam("storyId") Long storyId, @RequestParam("username") String username);

    @PostMapping("/api/v1/agent/story/internal/scene/get")
    ResponseResult<AgentStorySceneBaseVO> getStorySceneInfo(@RequestBody ItemIdDTO req);

    @PostMapping("/api/v1/agent/story/internal/scene/rec-current-use")
    ResponseResult<?> recCurrentSceneUse(@RequestBody StorySceneUseRecDTO req);

    ResponseResult<SensitiveWordCheckResultVO> checkText(@RequestBody @Valid SensitiveWordCheckDTO req)
}
