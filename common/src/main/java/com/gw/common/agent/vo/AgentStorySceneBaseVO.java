package com.gw.common.agent.vo;

import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class AgentStorySceneBaseVO {
    @JsonProperty("i")
    private Long id;
    @JsonProperty("sid")
    private Long storyId;
    @JsonProperty("sn")
    private String sceneName;
    @JsonProperty("sd")
    private String sceneDescription;
    @JsonProperty("btu")
    private String bgThumbnailUrl;
    @JsonProperty("ot")
    private String openingText;
    @JsonProperty("oa")
    private Long openingAgentId;
    private List<SceneAgentRelationBaseVO> agentRelations;

    public static String toJsonString(AgentStorySceneBaseVO vo) {
        return JSON.toJSONString(vo);
    }

    public static AgentStorySceneBaseVO fromJsonString(String json) {
        return JSON.parseObject(json, AgentStorySceneBaseVO.class);
    }
}
