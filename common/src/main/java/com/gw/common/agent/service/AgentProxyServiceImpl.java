package com.gw.common.agent.service;

import com.alibaba.fastjson2.JSON;
import com.gw.common.agent.client.AgentClient;
import com.gw.common.agent.dto.AgentQueryByIdsDTO;
import com.gw.common.agent.dto.AgentSettingQueryDTO;
import com.gw.common.agent.dto.UserInteractionAgentQuery;
import com.gw.common.agent.vo.AgentBaseVO;
import com.gw.common.agent.vo.CozeSettingVO;
import com.gw.common.agent.vo.MyAgentSettingVO;
import com.gw.common.agent.vo.UserInteractionAgentVO;
import com.gw.common.dto.ItemIdDTO;
import com.gw.common.dto.ResponseResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Log4j2
public class AgentProxyServiceImpl implements AgentProxyService {
    private final AgentClient agentClient;
    private final RedisTemplate<String, Object> redisTemplate;

    @Override
    public void putAgentMapToCache(String cacheKey, Map<Long, AgentBaseVO> agentBaseMap, Duration expire) {
        Map<String, String> cacheMap = new LinkedHashMap<>();
        agentBaseMap.forEach((k, v) -> cacheMap.put(k.toString(), JSON.toJSONString(v)));
        redisTemplate.opsForHash().putAll(cacheKey, cacheMap);
        if (expire != null) {
            redisTemplate.expire(cacheKey, expire);
        }
    }

    @Override
    public Map<Long, AgentBaseVO> getAgentBaseMapFromCache(String cacheKey) {
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(cacheKey);
        if (entries.isEmpty()) {
            return null;
        }
        Map<Long, AgentBaseVO> result = new HashMap<>();
        entries.forEach((k, v) -> {
            String jsonStr = (String) v;
            AgentBaseVO vo = JSON.parseObject(jsonStr, AgentBaseVO.class);
            result.put(Long.parseLong(k.toString()), vo);
        });
        return result;
    }

    @Override
    public AgentBaseVO findAgentBaseByIdFromCache(String cacheKey, Long id) {
        Object entry = redisTemplate.opsForHash().get(cacheKey, id.toString());
        if (entry != null) {
            String jsonStr = (String) entry;
            return JSON.parseObject(jsonStr, AgentBaseVO.class);
        }
        return null;
    }

    @Override
    public void updateAgentBaseToCacheIfCacheExist(String cacheKey, Long id, AgentBaseVO vo, Duration expire) {
        Boolean hasKey = redisTemplate.hasKey(cacheKey);
        if (hasKey) {
            redisTemplate.opsForHash().put(cacheKey, id.toString(), JSON.toJSONString(vo));
            if (expire != null) {
                redisTemplate.expire(cacheKey, expire);
            }
        }
    }

    @Override
    public void deleteAgentBaseKeyFromCache(String cacheKey, Long id) {
        if (cacheKey != null && id != null) {
            redisTemplate.opsForHash().delete(cacheKey, id.toString());
        }
    }

    @Override
    public void deleteAgentBaseFromCache(String cacheKey) {
        if (cacheKey != null) {
            redisTemplate.delete(cacheKey);
        }
    }

    @Override
    public AgentBaseVO getAgentInfo(String cacheKey, Long id) {
        if (id == null) {
            log.warn("智能体ID为空");
            return null;
        }

        log.debug("尝试从缓存获取智能体信息，cacheKey: {}, id: {}", cacheKey, id);
        AgentBaseVO vo = findAgentBaseByIdFromCache(cacheKey, id);
        if (vo != null) {
            log.debug("命中缓存，智能体信息: {}", vo);
            return vo;
        }

        log.debug("缓存未命中或反序列化失败，从远程服务获取智能体数据");
        // 缓存未命中或反序列化失败，从远程服务获取数据
        ItemIdDTO req = new ItemIdDTO();
        req.setId(id);
        ResponseResult<AgentBaseVO> rsp = agentClient.getAgentInfo(req);

        if (rsp.getCode() == 200 && rsp.getData() != null) {
            log.debug("远程获取智能体信息成功: {}", rsp.getData());
            return rsp.getData();
        } else {
            log.error("获取智能体信息失败，id: {}, error: {}", id, rsp.getMsg());
            return null;
        }
    }

    @Override
    public CozeSettingVO getCozeSetting() {
        ResponseResult<CozeSettingVO> rsp = agentClient.getCozeSetting();
        if (rsp.getCode() == 200) {
            return rsp.getData();
        } else {
            log.error("获取扣子配置信息失败：{}", rsp.getMsg());
            return null;
        }
    }

    @Override
    public MyAgentSettingVO getMyAgentSetting(String username, Long agentId) {
        if (agentId == null) {
            agentId = 0l;
        }
        ResponseResult<MyAgentSettingVO> rsp = agentClient.getMyAgentSetting(new AgentSettingQueryDTO(username, agentId));
        if (rsp.getCode() == 200) {
            log.debug("获取我的智能体配置信息成功：{}", rsp.getData());
            return rsp.getData();
        } else {
            log.error("获取我的智能体配置信息失败：{}", rsp.getMsg());
            return new MyAgentSettingVO();
        }
    }

    private Map<Long, AgentBaseVO> fetchFromCache(String cacheKey, List<Long> agentIds) {
        Map<Long, AgentBaseVO> cacheMap = getAgentBaseMapFromCache(cacheKey);
        if (cacheMap == null) {
            return new HashMap<>();
        }
        Map<Long, AgentBaseVO> resultMap = new HashMap<>();
        for (Long id : agentIds) {
            AgentBaseVO vo = cacheMap.get(id);
            if (vo != null) {
                resultMap.put(id, vo);
            }
        }
        return resultMap;
    }

    private Map<Long, AgentBaseVO> fetchFromRemote(List<Long> agentIds) {
        AgentQueryByIdsDTO req = new AgentQueryByIdsDTO(agentIds);
        ResponseResult<List<AgentBaseVO>> rsp = agentClient.getAgentsByIds(req);
        if (rsp.getCode() == 200 && rsp.getData() != null) {
            return rsp.getData().stream()
                    .collect(Collectors.toMap(AgentBaseVO::getId, vo -> vo, (e, r) -> e));
        } else {
            log.error("根据ID数组获取智能体信息失败，请求ID列表：{}，错误信息：{}", agentIds, rsp.getMsg());
            return new HashMap<>();
        }
    }

    @Override
    public Map<Long, AgentBaseVO> getAgentsByIds(String cacheKey, List<Long> agentIds) {
        if(agentIds == null || agentIds.isEmpty()){
            return new HashMap<>();
        }
        Map<Long, AgentBaseVO> result = fetchFromCache(cacheKey, agentIds);
        List<Long> missingIds = agentIds.stream()
                .filter(id -> !result.containsKey(id))
                .collect(Collectors.toList());
        if (!missingIds.isEmpty()) {
            result.putAll(fetchFromRemote(missingIds));
        }
        return result;
    }


    @Override
    public UserInteractionAgentVO getUserInteraction(String username) {
        UserInteractionAgentQuery req = new UserInteractionAgentQuery(username);
        ResponseResult<UserInteractionAgentVO> rsp = agentClient.getUserInteraction(req);
        if (rsp.getCode() == 200) {
            return rsp.getData();
        } else {
            log.error("根据ID数组获取智能体信息失败：{}", rsp.getMsg());
        }
        return new UserInteractionAgentVO();
    }

    @Override
    public Long findFirstIdByExclusive() {
        ResponseResult<Long> rsp = agentClient.findFirstIdByExclusive();
        if (rsp.getCode() == 200) {
            return rsp.getData();
        } else {
            log.error("获取第一个专属智能体ID失败：{}", rsp.getMsg());
            return null;
        }
    }

    @Override
    public boolean recordAgentUsage(Long agentId, String username) {
        try {
            CompletableFuture.runAsync(() -> {
                try {
                    agentClient.recordAgentUsage(agentId, username);
                    log.info("记录用户[{}]使用智能体[{}]", username, agentId);
                } catch (Exception e) {
                    log.error("记录智能体使用失败", e);
                }
            });
            return true;
        } catch (Exception e) {
            log.error("异步任务执行失败", e);
            return false;
        }
    }

    @Override
    public int getAgentUserCount(Long agentId) {
        // 临时实现：返回默认值
        log.info("获取智能体[{}]的使用人数", agentId);
        return 0;
    }
}
