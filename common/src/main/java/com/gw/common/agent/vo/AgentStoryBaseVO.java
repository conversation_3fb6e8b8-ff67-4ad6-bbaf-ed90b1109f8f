package com.gw.common.agent.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class AgentStoryBaseVO {
    private Long id;
    @JsonProperty("n")
    private String name;
    @JsonProperty("d")
    private String description;
    @JsonProperty("my")
    private String myName;
    @JsonProperty("mg")
    private Integer myGender;
    @JsonProperty("mi")
    private String myIdentity;
    @JsonProperty("st")
    private Integer status;
    @JsonProperty("ip")
    private Integer isPublic;
    @JsonProperty("ss")
    private Integer shelfStatus = 1;
    @JsonProperty("bgt")
    private String bgThumbnailUrl;
    @JsonProperty("stt")
    private Long storyTypeId;
    @JsonProperty("cr")
    private String creator;
}
