package com.gw.common.agent.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Schema(description = "智能体类型VO")
@AllArgsConstructor
@NoArgsConstructor
public class AgentTypeVO {
    @Schema(description = "类型ID")
    private Long id;
    @Schema(description = "类型名称")
    @NotBlank(message = "标签名称不能为空")
    private String name;
    @Schema(description = "标签描述")
    private String description;

}
