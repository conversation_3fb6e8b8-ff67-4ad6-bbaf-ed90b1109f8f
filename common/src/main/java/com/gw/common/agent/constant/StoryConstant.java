package com.gw.common.agent.constant;

/**
 * 剧情系统常量
 */
public class StoryConstant {

    public static final int STORY_PUBLIC = 2;
    public static final int STORY_PRIVATE = 1;
    // 剧情状态
    public static final int SHELF_ON_STATUS = 1;   // 上架
    public static final int SHELF_OFF_STATUS = 2;   // 下架

    public static final int STORY_CHECK_STATUS_FAIL = -1;
    public static final int STORY_CHECK_STATUS = 1;
    public static final int STORY_CREATE_STATUS_FAIL = -2;
    public static final int STORY_PUBLIC_STATUS = 4;

    public static final int STORY_PUBLISH_STATUS_FAIL = -4;
    public static final int STORY_STATUS_DRAFT = 255;     // 草稿

    public static final int STORY_STATUS_PUBLISHED = 256;


    // 场景状态
    public static final int SCENE_STATUS_DISABLED = 0; // 禁用
    public static final int SCENE_STATUS_NORMAL = 1;   // 正常

    // 关联状态
    public static final int RELATION_STATUS_DISABLED = 0; // 禁用
    public static final int RELATION_STATUS_NORMAL = 1;   // 正常

    private StoryConstant() {
        // 私有构造函数，防止实例化
    }
} 