package com.gw.common.dto;

import com.gw.common.exception.BusinessExceptionCode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "公共响应模型")
public class ResponseResult<T> {
    @Schema(description = "响应码")
    private int code;
    @Schema(description = "响应消息")
    private String msg;
    @Schema(description = "响应数据")
    private T data;

    public static <T> ResponseResult<T> success(T data) {
        return new ResponseResult<>(200, "success", data);
    }

    public static <T> ResponseResult<T> failure(int code, String msg) {
        return new ResponseResult<>(code, msg, null);
    }

    public static <T> ResponseResult<T> failure(String msg) {
        return new ResponseResult<>(BusinessExceptionCode.FAIL_CODE.getCode(), msg, null);
    }

    public static <T> ResponseResult<T> failure(BusinessExceptionCode code, String msg) {
        return new ResponseResult<>(code.getCode(), msg, null);
    }
}
