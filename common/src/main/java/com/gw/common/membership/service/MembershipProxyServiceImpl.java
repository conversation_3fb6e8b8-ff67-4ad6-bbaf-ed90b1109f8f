package com.gw.common.membership.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gw.common.dto.ResponseResult;
import com.gw.common.dto.TimeRangeDTO;
import com.gw.common.membership.client.MembershipClient;
import com.gw.common.membership.dto.*;
import com.gw.common.membership.vo.InvitationCodeVO;
import com.gw.common.membership.vo.MembershipStatisticsVO;
import com.gw.common.membership.vo.UserMembershipBaseVO;
import com.gw.common.membership.vo.UserMembershipVO;
import com.gw.common.vo.BenefitCanUseVO;
import com.gw.common.vo.BenefitRemainingNumVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.modelmapper.ModelMapper;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Log4j2
@Service
@RequiredArgsConstructor
public class MembershipProxyServiceImpl implements MembershipProxyService {
    private final MembershipClient membershipClient;
    private final RedisTemplate<String, Object> redisTemplate;
    private final ObjectMapper objectMapper;
    private final ModelMapper modelMapper = new ModelMapper();

    @Override
    public UserMembershipVO getMembershipByUsername(String cacheName, String username) {

        // 首先尝试从缓存中获取数据
        Object cachedObject = redisTemplate.opsForValue().get(cacheName);
        UserMembershipBaseVO cachedValue = null;

        if (cachedObject != null) {
            try {
                // 如果是直接的UserMembershipBaseVO对象
                if (cachedObject instanceof UserMembershipBaseVO) {
                    cachedValue = (UserMembershipBaseVO) cachedObject;
                }
                // 如果是Map类型（通常是从Redis反序列化的结果）
                else if (cachedObject instanceof Map) {
                    cachedValue = objectMapper.convertValue(cachedObject, UserMembershipBaseVO.class);
                }

                // 如果成功获取到缓存数据，直接返回
                if (cachedValue != null) {

                    return modelMapper.map(cachedValue, UserMembershipVO.class);
                }
            } catch (Exception e) {
                // 反序列化失败，忽略并继续从数据库加载
                // 可以添加日志记录
                System.err.println("Failed to deserialize cached value: " + e.getMessage());
            }
        }
        ResponseResult<UserMembershipVO> resp = membershipClient.getMembershipByUsername(new MembershipQueryDTO(username));
        if (resp.getCode() == 200) {
            return resp.getData();
        }
        return null;
    }

    @Override
    public InvitationCodeVO getInvitationCodeByUsername(String username) {
        ResponseResult<InvitationCodeVO> resp = membershipClient.getInvitationCodeByUsername(new InvitationCodeQuery(username));
        if (resp.getCode() == 200) {
            return resp.getData();
        }
        return null;
    }

    @Override
    public void useInvitationCode(String username, String code) {
        ResponseResult<?> resp = membershipClient.useInvitationCode(new InvitationCodeUseDTO(code, username));
        if (resp.getCode() != 200) {
            log.error("邀请增会员失败: {}", resp.getMsg());
        }
    }

    @Override
    public void newUserReg(String username) {
        ResponseResult<?> resp = membershipClient.newUserReg(new NewUserRegisterDTO(username));
        if (resp.getCode() != 200) {
            log.error("新用户增会员失败: {}", resp.getMsg());
        }
    }

    @Override
    public boolean checkBenefitCanUse(String username, String benefitCode) {
        try {
            ResponseResult<BenefitCanUseVO> result = membershipClient.queryCanUse(new BenefitCanUseQueryDTO(username, benefitCode));
            if (result.getCode() != 200) {
                log.error("检查权益是否可用失败,参数username:{},benefitCode:{},错误信息:{}", username, benefitCode, result.getMsg());
                return false;
            }
            return result.getData().isCanUse();
        } catch (Exception e) {
            log.error("调用会员服务检查权益异常,参数username:{},benefitCode:{}", username, benefitCode, e);
            return false;
        }
    }

    @Override
    public void asyncRecordBenefitUsage(String username, String benefitCode, Integer count) {
        CompletableFuture.runAsync(() -> {
            try {
                BenefitUsageRecordDTO recordDTO = new BenefitUsageRecordDTO(username, benefitCode, count);
                ResponseResult<BenefitRemainingNumVO> result = membershipClient.usageRecord(recordDTO);
                if (result.getCode() != 200) {
                    log.error("记录会员权益使用错误,参数:{},错误信息:{}", recordDTO, result.getMsg());
                }
            } catch (Exception e) {
                log.error("记录会员权益使用异常,username:{},benefitCode:{},count:{}", username, benefitCode, count, e);
            }
        });

    }

    @Override
    public double calculateIncomeByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            ResponseResult<Double> result = membershipClient.calculateIncomeByTimeRange(new TimeRangeDTO(startTime, endTime));
            if (result.getCode() != 200) {
                log.error("根据时间范围计算收入失败: {}", result.getMsg());
            }
            return result.getData();
        } catch (Exception e) {
            log.error("调用会员服务计算收入异常", e);
        }
        return 0;
    }

    @Override
    public int countTotalVipUsers() {
        try {
            ResponseResult<Integer> result = membershipClient.countTotalVipUsers();
            if (result.getCode() != 200) {
                log.error("统计VIP用户数失败: {}", result.getMsg());
            }
            return result.getData();
        } catch (Exception e) {
            log.error("调用会员服务统计VIP用户数异常", e);
        }
        return 0;
    }

    @Override
    public double calculateTotalIncome() {
        try {
            ResponseResult<Double> result = membershipClient.calculateTotalIncome();
            if (result.getCode() != 200) {
                log.error("统计总收入失败: {}", result.getMsg());
            }
            return result.getData();
        } catch (Exception e) {
            log.error("调用会员服务计算总收入异常", e);
        }
        return 0;
    }

    @Override
    public MembershipStatisticsVO getMembershipStatistics() {
        try {
            ResponseResult<MembershipStatisticsVO> result = membershipClient.getMembershipStatistics();
            if (result.getCode() == 200 && result.getData() != null) {
                return result.getData();
            }
            log.error("获取会员统计数据失败: {}", result.getMsg());
        } catch (Exception e) {
            log.error("调用会员服务获取统计数据异常", e);
        }
        // 失败时返回默认值
        MembershipStatisticsVO defaultStatistics = new MembershipStatisticsVO();
        defaultStatistics.setTodayIncome(0.0);
        defaultStatistics.setTotalVipUsers(0);
        defaultStatistics.setTotalIncome(0.0);
        return defaultStatistics;
    }
}
