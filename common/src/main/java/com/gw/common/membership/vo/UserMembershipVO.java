package com.gw.common.membership.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户会员
 */
@Data
@Schema(description = "用户会员信息")
public class UserMembershipVO {
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private String username;

    /**
     * 当前套餐ID
     */
    @Schema(description = "当前套餐ID")
    private Long packageId;

    /**
     * 当前套餐名称
     */
    @Schema(description = "当前套餐名称")
    private String packageName;

    /**
     * 会员状态: 0-非会员, 1-会员
     */
    @Schema(description = "会员状态: 0-非会员, 1-会员")
    private Integer status;

    /**
     * 会员开始时间
     */
    @Schema(description = "会员开始时间")
    private LocalDateTime startTime;

    /**
     * 会员到期时间
     */
    @Schema(description = "会员到期时间")
    private LocalDateTime expireTime;

    /**
     * 是否过期
     */
    @Schema(description = "是否过期")
    private Boolean isExpired;

    /**
     * 剩余天数
     */
    @Schema(description = "剩余天数")
    private Integer remainingDays;

    /**
     * 是否自动续费: 0-否, 1-是
     */
    @Schema(description = "是否自动续费: 0-否, 1-是")
    private Integer autoRenew;

    /**
     * 会员等级
     */
    @Schema(description = "会员等级")
    private Integer vipLevel;

    /**
     * 会员积分
     */
    @Schema(description = "会员积分")
    private Integer memberPoints;

    /**
     * 会员权益列表
     */
    @Schema(description = "会员权益列表")
    private List<MembershipBenefitVO> benefits;
}