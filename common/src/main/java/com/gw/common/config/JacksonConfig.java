package com.gw.common.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.gw.common.util.FlexibleLocalDateDeserializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.time.LocalDate;

/**
 * Jackson 全局配置
 * 配置日期时间的序列化和反序列化
 */
@Configuration
public class JacksonConfig {

    /**
     * 配置全局 ObjectMapper
     * 支持灵活的 LocalDate 反序列化
     */
    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        
        // 注册 Java 8 时间模块
        mapper.registerModule(new JavaTimeModule());
        
        // 创建自定义模块，添加灵活的 LocalDate 反序列化器
        SimpleModule customModule = new SimpleModule("CustomDateModule");
        customModule.addDeserializer(LocalDate.class, new FlexibleLocalDateDeserializer());
        mapper.registerModule(customModule);
        
        return mapper;
    }
}
