package com.gw.common.exception;

import lombok.Getter;

@Getter
public class BusinessExceptionCode {
    public static final BusinessExceptionCode VIOLATION_CODE = new BusinessExceptionCode(330, "违规");
    public static final BusinessExceptionCode FAIL_CODE = new BusinessExceptionCode(400, "失败");
    public static final BusinessExceptionCode NOT_FOUND_CODE = new BusinessExceptionCode(404, "请求的资源找不不到");
    public static final BusinessExceptionCode UNAUTHORIZED_CODE = new BusinessExceptionCode(401, "请求需要用户认证");
    public static final BusinessExceptionCode FORBIDDEN_CODE = new BusinessExceptionCode(404, "请求的资源未找到");
    public static final BusinessExceptionCode METHOD_NOT_ALLOWED_CODE = new BusinessExceptionCode(405, "请求方法不被允许");
    public static final BusinessExceptionCode NOT_ACCEPTABLE_CODE = new BusinessExceptionCode(406, "请求的资源不可接受");
    public static final BusinessExceptionCode REQUEST_TIMEOUT_CODE = new BusinessExceptionCode(408, "请求超时");
    public static final BusinessExceptionCode GONE_CODE = new BusinessExceptionCode(410, "请求的资源已不存在");
    public static final BusinessExceptionCode UNSUPPORTED_MEDIA_TYPE_CODE = new BusinessExceptionCode(415, "请求的媒体类型不支持");
    public static final BusinessExceptionCode TOO_MANY_REQUESTS_CODE = new BusinessExceptionCode(429, "请求次数过多");
    public static final BusinessExceptionCode INTERNAL_SERVER_ERROR_CODE = new BusinessExceptionCode(500, "服务器内部错误");
    public static final BusinessExceptionCode SERVICE_UNAVAILABLE_CODE = new BusinessExceptionCode(503, "服务不可用");
    public static final BusinessExceptionCode GATEWAY_TIMEOUT_CODE = new BusinessExceptionCode(504, "网关超时");
    public static final BusinessExceptionCode BAD_GATEWAY_CODE = new BusinessExceptionCode(502, "网关错误");
    public static final BusinessExceptionCode BENEFIT_NOT_ALLOWED_CODE = new BusinessExceptionCode(310, "权益不允许使用");
    private int code;
    private String msg;

    protected BusinessExceptionCode() {
    }

    public BusinessExceptionCode(int code, String msg) {
        this.msg = msg;
        this.code = code;
    }
}
