package com.gw.common.notify.client;

import com.gw.common.dto.ResponseResult;
import com.gw.common.notify.dto.InteractiveNotifySubmitDTO;
import com.gw.common.notify.dto.SystemNotifySubmitDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * AI代理服务客户端
 */
@FeignClient(name = "notify-service")
public interface NotifyClient {

    @PostMapping("/api/v1/notify/system_message/auto_create")
    ResponseResult<?> autoCreateSystemMessage(@RequestBody SystemNotifySubmitDTO req);

    @PostMapping("/api/v1/notify/interactive_message/auto_create")
    ResponseResult<?> autoCreateInteractiveMessage(@RequestBody InteractiveNotifySubmitDTO req);
}
