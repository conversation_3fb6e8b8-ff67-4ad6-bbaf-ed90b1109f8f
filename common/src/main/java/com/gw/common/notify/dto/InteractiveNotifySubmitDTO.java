package com.gw.common.notify.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 互动消息通知提交DTO
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "互动消息通知提交请求")
public class InteractiveNotifySubmitDTO extends InteractiveMessageCreateDTO {
    /**
     * 创建者用户名
     */
    @Schema(description = "创建者用户名")
    private String username;

    public InteractiveNotifySubmitDTO(String content, Integer type, String username) {
        super(content, type);
        this.username = username;
    }

    public InteractiveNotifySubmitDTO(String content, Integer type, String targetUsername, String username) {
        super(content, type, targetUsername);
        this.username = username;
    }

    public InteractiveNotifySubmitDTO(String content, Integer type, Long agentId, String targetUsername, String username) {
        super(content, type, agentId, targetUsername);
        this.username = username;
    }
} 