package com.gw.common.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PaginationVo {
    @Schema(description = "总数")
    private long total;
    @Schema(description = "分页索引,表示第几页，从1开始")
    private int current;
    @Schema(description = "每页的大小")
    private int pageSize;
}
