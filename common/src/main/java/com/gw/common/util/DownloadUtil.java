package com.gw.common.util;

import lombok.extern.log4j.Log4j2;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.ClassicHttpResponse;
import org.apache.hc.core5.http.Header;

import java.nio.charset.StandardCharsets;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Log4j2
public class DownloadUtil {


    public static String downloadFile(String url, String savePath) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            log.info("开始下载文件，URL: {}", url);
            HttpGet request = new HttpGet(url);

            try (ClassicHttpResponse response = httpClient.execute(request)) {
                // 检查重定向
                int statusCode = response.getCode();
                if (statusCode >= 300 && statusCode < 400) {
                    Header locationHeader = response.getFirstHeader("Location");
                    if (locationHeader != null) {
                        String location = locationHeader.getValue();
                        log.info("检测到重定向到: {}", location);
                        return downloadFile(location, savePath);
                    }
                }

                var entity = response.getEntity();
                if (entity == null) {
                    log.error("响应实体为空");
                    return null;
                }

                // 解析文件名
                String fileName = parseFileNameFromResponse(response);
                if (fileName == null) {
                    log.warn("无法从Content-Disposition解析文件名，尝试从URL路径解析");
                    // 从URL路径提取文件名
                    java.net.URL parsedUrl = new java.net.URL(url);
                    String path = parsedUrl.getPath();

                    // 移除路径末尾的斜杠
                    if (path.endsWith("/")) {
                        path = path.substring(0, path.length() - 1);
                    }

                    // 提取最后一个路径段作为文件名
                    int lastSlashIndex = path.lastIndexOf('/');
                    if (lastSlashIndex != -1) {
                        fileName = path.substring(lastSlashIndex + 1);
                    } else {
                        fileName = "downloaded_file";
                    }

                    if (fileName.isEmpty()) {
                        log.warn("URL路径为空，使用默认文件名");
                        fileName = "downloaded_file";
                    }

                    // 添加默认扩展名（可根据需求扩展）
                    if (!fileName.contains(".")) {
                        fileName += ".png";
                    }

                    log.info("使用解析到的文件名: {}", fileName);
                }

                java.io.File saveDir = new java.io.File(savePath);
                if (!saveDir.exists()) {
                    log.info("创建保存目录: {}", savePath);
                    saveDir.mkdirs();
                }
                java.io.File file = new java.io.File(saveDir + java.io.File.separator + fileName);
                log.info("保存文件到: {}", file.getAbsolutePath());

                try (java.io.FileOutputStream fos = new java.io.FileOutputStream(file)) {
                    entity.writeTo(fos);
                    log.info("文件下载完成: {}", file.getAbsolutePath());
                    // 修改返回值为savePath+filename格式并统一使用正斜杠
                    String relativePath = saveDir.getPath() + java.io.File.separator + fileName;
                    return relativePath.replace(java.io.File.separator, "/");
                }
            }
        } catch (Exception e) {
            log.error("文件下载失败 - URL: {}, 错误信息: {}", url, e.getMessage(), e);
            return null;
        }
    }

    private static String parseFileNameFromResponse(ClassicHttpResponse response) {
        Header cdHeader = response.getFirstHeader("Content-Disposition");
        if (cdHeader == null) return null;

        String contentDisposition = cdHeader.getValue();
        // 修复正则表达式：处理带引号的文件名
        Pattern pattern = Pattern.compile("filename\\*?=(\"([^\"]+)\"|([^;]+))");
        Matcher matcher = pattern.matcher(contentDisposition);
        if (matcher.find()) {
            String filename = matcher.group(2) != null ? matcher.group(2) : matcher.group(3);
            if (filename == null) return null;
            filename = filename.trim();

            // 处理RFC 5987编码格式
            if (filename.startsWith("UTF-8''")) {
                return java.net.URLDecoder.decode(filename.substring(6), StandardCharsets.UTF_8);
            }
            return filename;
        }
        return null;
    }
}
