package com.gw.common.user.client;

import com.gw.common.dto.ResponseResult;
import com.gw.common.dto.TimeRangeDTO;
import com.gw.common.user.dto.QueryFromUsernameDTO;
import com.gw.common.user.vo.UserBaseContentVo;
import com.gw.common.user.vo.UserStatisticsVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

@FeignClient(name = "user-service")
public interface UserServiceClient {
    @PostMapping("/api/v1/auth/users/get_from_username")
    ResponseResult<UserBaseContentVo> getUserInfoFromUsername(@RequestBody QueryFromUsernameDTO req);

    @GetMapping("/api/v1/auth/users/find_all_base")
    ResponseResult<Map<String, UserBaseContentVo>> findAllBase();

    @GetMapping("/api/v1/auth/users/user_statistics")
    ResponseResult<UserStatisticsVO> getUserStatistics();

    @PostMapping("/api/v1/auth/users/count_new_users")
    ResponseResult<Integer> countNewUsersByTimeRange(@RequestBody TimeRangeDTO req);
    @PostMapping("/api/v1/auth/users/find_by_usernames")
    ResponseResult<Map<String, UserBaseContentVo>> findAllBase(@RequestBody List<String> usernames);
}