package com.gw.common.user.context;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class UserContext {
    private static final ThreadLocal<UserContext> userHolder = new ThreadLocal<>();
    private String userId;
    private String username;
    private String realName;
    private List<String> roles;

    public static void set(UserContext user) {
        userHolder.set(user);
    }

    public static UserContext get() {
        return userHolder.get();
    }

    public static void remove() {
        userHolder.remove();
    }
} 