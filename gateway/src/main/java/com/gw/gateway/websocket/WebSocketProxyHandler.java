package com.gw.gateway.websocket;

import com.gw.gateway.filter.AuthProperties;
import com.gw.gateway.service.TokenValidationService;
import com.gw.gateway.vo.TokenInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.reactive.socket.WebSocketHandler;
import org.springframework.web.reactive.socket.WebSocketSession;
import org.springframework.web.reactive.socket.client.WebSocketClient;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.concurrent.ConcurrentHashMap;

@Log4j2
@Component
@RequiredArgsConstructor
public class WebSocketProxyHandler implements WebSocketHandler {

    private final WebSocketClient webSocketClient;
    private final TokenValidationService tokenValidationService;
    private final AuthProperties authProperties;
    private final ConcurrentHashMap<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    @Value("${websocket.chat-service-uri:http://localhost:9085}")
    private String chatServiceUri;

    @Value("${websocket.multi-chat-service-uri:http://localhost:8084}")
    private String multiChatServiceUri;

    @Override
    public Mono<Void> handle(WebSocketSession session) {
        log.debug("接收到WebSocket连接: {}", session.getId());
        sessions.put(session.getId(), session);

        // 从原始会话路径中提取目标路径和目标服务URI
        String path = session.getHandshakeInfo().getUri().getPath();
        String targetPath;
        String targetServiceUri;

        if (path.startsWith("/api/v1/ws/multi/chat")) {
            // 多人聊天服务路由
            targetPath = path.replace("/api/v1/ws/multi/chat", "/ws/multi/chat");
            targetServiceUri = multiChatServiceUri;
        } else if (path.startsWith("/api/v1/ws/chat")) {
            // 单人聊天服务路由
            targetPath = path.replace("/api/v1/ws/chat", "/ws/chat");
            targetServiceUri = chatServiceUri;
        } else {
            log.warn("未知的WebSocket路径: {}", path);
            return closeSession(session, "未知的WebSocket路径");
        }

        // 检查是否需要跳过认证
        if (isSkipUrl(path)) {
            log.debug("WebSocket路径跳过认证: {}", path);
            return handleWebSocketConnection(session, targetPath, targetServiceUri, null);
        }

        // 获取并验证token
        String token = getToken(session);
        if (token == null) {
            log.warn("WebSocket连接没有提供有效的token，路径: {}", path);
            return closeSession(session, "未提供有效的token");
        }

        // 验证token并获取用户信息
        return tokenValidationService.validateTokenAndPermissions(token, path)
                .flatMap(tokenInfo -> {
                    if (!tokenInfo.isValid()) {
                        log.warn("WebSocket连接使用了无效的token，路径: {}", path);
                        return closeSession(session, "无效的token");
                    }

                    log.debug("WebSocket连接认证成功，用户: {}, 路径: {}", tokenInfo.getUsername(), path);
                    return handleWebSocketConnection(session, targetPath, targetServiceUri, tokenInfo);
                })
                .onErrorResume(e -> {
                    log.error("WebSocket认证处理错误: {}", e.getMessage(), e);
                    return closeSession(session, "认证处理错误");
                });
    }

    private Mono<Void> handleWebSocketConnection(WebSocketSession session, String targetPath, String targetServiceUri, TokenInfo tokenInfo) {
        // 构建目标URI
        URI targetUri = URI.create(targetServiceUri + targetPath);
        log.debug("目标WebSocket URI: {}", targetUri);

        // 准备要转发的头信息
        HttpHeaders headers = new HttpHeaders();

        // 首先复制原始WebSocket连接的所有头部信息
        HttpHeaders originalHeaders = session.getHandshakeInfo().getHeaders();
        originalHeaders.forEach((key, values) -> {
            // 跳过一些可能导致冲突的头部信息
            if (!shouldSkipHeader(key)) {
                headers.addAll(key, values);
                log.debug("转发原始头部: {} = {}", key, values);
            }
        });

        // 添加或覆盖用户认证相关的头信息
        if (tokenInfo != null) {
            headers.set("X-Internal-Service", "api-gateway");
            headers.set("X-User-Id", tokenInfo.getUserId());
            headers.set("X-User-Name", tokenInfo.getUsername());
            headers.set("X-User-RealName",
                    Base64.getEncoder().encodeToString(
                            tokenInfo.getRealName().getBytes(StandardCharsets.UTF_8)));
            headers.set("X-User-Roles", String.join(",", tokenInfo.getRoles()));
        }

        // 转发头信息
        return webSocketClient.execute(
                        targetUri,
                        headers,
                        clientSession -> {
                            log.debug("已连接到目标WebSocket: {}", targetUri);

                            // 从客户端会话接收消息并发送到服务端
                            Mono<Void> clientToServer = session.receive()
                                    .doOnNext(message -> log.debug("接收到消息: {}", message.getPayloadAsText()))
                                    .map(message -> {
                                        log.debug("转发消息到服务端: {}", message.getPayloadAsText());
                                        return clientSession.textMessage(message.getPayloadAsText());
                                    })
                                    .doOnError(error -> log.error("转发消息到服务端出错: {}", error.getMessage(), error))
                                    .as(clientSession::send);

                            // 从服务端接收消息并发送到客户端
                            Mono<Void> serverToClient = clientSession.receive()
                                    .doOnNext(message -> log.debug("从服务端接收到消息: {}", message.getPayloadAsText()))
                                    .map(message -> {
                                        log.debug("转发服务端消息到客户端: {}", message.getPayloadAsText());
                                        return session.textMessage(message.getPayloadAsText());
                                    })
                                    .doOnError(error -> {
                                        log.error("转发服务端消息到客户端出错: {}", error.getMessage(), error);
                                        // 当服务端连接出错时，关闭客户端连接
                                        closeSession(session, "服务端连接出错")
                                                .subscribe(null, e -> log.error("关闭客户端会话失败", e));
                                    })
                                    .as(session::send);

                            // 监听客户端会话关闭
                            session.closeStatus()
                                    .doOnNext(status -> {
                                        log.debug("客户端会话关闭: {}, 原因: {}", session.getId(), status.getReason());
                                        sessions.remove(session.getId());
                                    })
                                    .subscribe(null, e -> log.error("监听客户端会话关闭失败", e));

                            // 监听服务端会话关闭
                            clientSession.closeStatus()
                                    .doOnNext(status -> {
                                        log.debug("服务端会话关闭: {}, 原因: {}", clientSession.getId(), status.getReason());
                                        // 当服务端会话关闭时，关闭客户端连接
                                        closeSession(session, "服务端连接断开")
                                                .subscribe(null, e -> log.error("服务端会话关闭后关闭客户端会话失败", e));
                                    })
                                    .subscribe(null, e -> log.error("监听服务端会话关闭失败", e));

                            return Mono.zip(clientToServer, serverToClient)
                                    .then()
                                    .doOnError(error -> {
                                        log.error("WebSocket代理连接出错: {}", error.getMessage(), error);
                                        // 当代理连接出错时，关闭客户端连接
                                        closeSession(session, "WebSocket代理连接出错")
                                                .subscribe(null, e -> log.error("代理连接出错后关闭客户端会话失败", e));
                                    });
                        })
                .doFinally(signal -> {
                    log.debug("WebSocket代理连接结束: {}, 信号: {}", session.getId(), signal);
                    // 当代理连接结束时，关闭客户端连接并移除会话
                    if (sessions.containsKey(session.getId())) {
                        closeSession(session, "WebSocket代理连接结束")
                                .subscribe(null, e -> log.error("代理连接结束后关闭客户端会话失败", e));
                    }
                })
                .onErrorResume(error -> {
                    log.error("无法连接到服务端: {}", error.getMessage(), error);
                    // 当无法连接到服务端时，关闭客户端连接
                    return closeSession(session, "无法连接到服务端");
                });
    }

    private Mono<Void> closeSession(WebSocketSession session, String reason) {
        return session.close().doFinally(signal -> {
            log.debug("WebSocket会话关闭: {}, 原因: {}", session.getId(), reason);
            sessions.remove(session.getId());
        });
    }

    private String getToken(WebSocketSession session) {
        HttpHeaders headers = session.getHandshakeInfo().getHeaders();
        String authorization = headers.getFirst(HttpHeaders.AUTHORIZATION);
        if (authorization == null || !authorization.startsWith("Bearer ")) {
            return null;
        }
        return authorization.substring(7);
    }

    private boolean isSkipUrl(String path) {
        return authProperties.getSkip().getUrls().stream()
                .anyMatch(pattern -> pathMatcher.match(pattern, path));
    }

    /**
     * 判断是否应该跳过某个头部信息的转发
     * 跳过一些可能导致冲突或安全问题的头部
     */
    private boolean shouldSkipHeader(String headerName) {
        if (headerName == null) {
            return true;
        }

        String lowerCaseHeader = headerName.toLowerCase();

        // 跳过WebSocket握手相关的头部，这些会由WebSocketClient自动处理
        if (lowerCaseHeader.equals("upgrade") ||
            lowerCaseHeader.equals("connection") ||
            lowerCaseHeader.equals("sec-websocket-key") ||
            lowerCaseHeader.equals("sec-websocket-version") ||
            lowerCaseHeader.equals("sec-websocket-protocol") ||
            lowerCaseHeader.equals("sec-websocket-extensions")) {
            return true;
        }

        // 跳过Host头部，因为目标服务的Host会不同
        if (lowerCaseHeader.equals("host")) {
            return true;
        }

        // 跳过Content-Length和Transfer-Encoding，WebSocket不需要这些
        if (lowerCaseHeader.equals("content-length") ||
            lowerCaseHeader.equals("transfer-encoding")) {
            return true;
        }

        return false;
    }
}