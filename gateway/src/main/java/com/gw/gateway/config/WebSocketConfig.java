package com.gw.gateway.config;

import lombok.extern.log4j.Log4j2;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;

/**
 * WebSocket配置，用于Spring Cloud Gateway路由
 * 注意：主要的WebSocket令牌验证和用户信息处理已移至WebSocketProxyHandler
 */
@Component
@Log4j2
public class WebSocketConfig extends AbstractGatewayFilterFactory<WebSocketConfig.Config> {

    public WebSocketConfig() {
        super(Config.class);
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            String path = exchange.getRequest().getURI().getPath();
            log.debug("WebSocketConfig过滤器处理路径: {}", path);

            // 检查是否是WebSocket握手请求
            if (isWebSocketHandshake(exchange)) {
                log.debug("检测到WebSocket握手请求: {}", path);
                // 简单地转发所有WebSocket请求，令牌验证和用户头信息处理在WebSocketProxyHandler中完成
            }

            // 继续处理链
            return chain.filter(exchange);
        };
    }

    private boolean isWebSocketHandshake(ServerWebExchange exchange) {
        HttpHeaders headers = exchange.getRequest().getHeaders();
        return headers.containsKey("Upgrade") &&
                "websocket".equalsIgnoreCase(headers.getFirst("Upgrade"));
    }

    public static class Config {
        // 配置属性（如果需要）
    }
}