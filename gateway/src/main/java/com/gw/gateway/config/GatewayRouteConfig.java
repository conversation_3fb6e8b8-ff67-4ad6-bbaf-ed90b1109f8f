package com.gw.gateway.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class GatewayRouteConfig {

    @Autowired
    private WebSocketConfig webSocketConfig;

    @Bean
    public RouteLocator customRouteLocator(RouteLocatorBuilder builder) {
        return builder.routes()
                // 注意: WebSocket路由已经在application.yml中定义，这里不再重复定义

                // REST API请求路由
                .route("chat-service-api", r -> r
                        .path("/api/v1/chat/**")
                        .filters(f -> f
                                .preserveHostHeader())
                        .uri("lb://chat-service"))
                // 其他路由...
                .build();
    }
}