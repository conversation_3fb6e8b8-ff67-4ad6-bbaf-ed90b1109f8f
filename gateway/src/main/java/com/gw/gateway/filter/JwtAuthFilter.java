package com.gw.gateway.filter;

import com.alibaba.fastjson2.JSON;
import com.gw.gateway.service.TokenValidationService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class JwtAuthFilter implements GlobalFilter, Ordered {
    private static final Logger log = LoggerFactory.getLogger(JwtAuthFilter.class);
    private final AntPathMatcher pathMatcher = new AntPathMatcher();
    private final AuthProperties authProperties;
    private final TokenValidationService tokenValidationService;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getPath().toString();

        // 检查是否是跳过认证的URL
        if (isSkipUrl(path)) {
            return chain.filter(exchange);
        }

        // 正常HTTP请求处理
        String token = getToken(request);
        if (token == null) {
            return onError(exchange, "No token provided", HttpStatus.UNAUTHORIZED);
        }

        return tokenValidationService.validateTokenAndPermissions(token, path)
                .flatMap(tokenInfo -> {
                    if (!tokenInfo.isValid()) {
                        return onError(exchange, "Invalid token", HttpStatus.UNAUTHORIZED);
                    }

                    // 将用户信息添加到请求头
                    ServerHttpRequest mutatedRequest = exchange.getRequest().mutate()
                            .header("X-Internal-Service", "api-gateway")
                            .header("X-User-Id", tokenInfo.getUserId())
                            .header("X-User-Name", tokenInfo.getUsername())
                            .header("X-User-RealName",
                                    Base64.getEncoder()
                                            .encodeToString(tokenInfo.getRealName().getBytes(StandardCharsets.UTF_8)))
                            .header("X-User-Roles", String.join(",", tokenInfo.getRoles()))
                            .build();

                    return chain.filter(exchange.mutate().request(mutatedRequest).build());
                })
                .onErrorResume(e -> {
                    log.error("Authentication error", e);
                    return onError(exchange, "Authentication failed", HttpStatus.UNAUTHORIZED);
                });
    }

    private String getToken(ServerHttpRequest request) {
        String bearerToken = request.getHeaders().getFirst(HttpHeaders.AUTHORIZATION);
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }

    private Mono<Void> onError(ServerWebExchange exchange, String message, HttpStatus unauthorized) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        response.getHeaders().add("Content-Type", "application/json");

        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("message", message);
        errorDetails.put("timestamp", LocalDateTime.now());

        byte[] bytes = JSON.toJSONString(errorDetails).getBytes(StandardCharsets.UTF_8);
        DataBuffer buffer = response.bufferFactory().wrap(bytes);
        return response.writeWith(Mono.just(buffer));
    }

    private boolean isSkipUrl(String path) {
        return authProperties.getSkip().getUrls().stream()
                .anyMatch(pattern -> pathMatcher.match(pattern, path));
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + 2;
    }
}