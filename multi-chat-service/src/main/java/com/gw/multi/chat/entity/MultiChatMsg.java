package com.gw.multi.chat.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 多智能体群聊消息实体
 * <p>
 * 存储结构：
 * - 按故事ID分表存储（collection名为: multi_chat_messages_{storyId}）
 * - 支持多种消息类型
 * - 记录智能体交互信息
 * - 维护场景上下文
 */
@Data
@Document(collection = "multi_chat_messages")
public class MultiChatMsg {

    /**
     * 消息唯一标识
     */
    @Id
    private String id;

    /**
     * 故事ID
     */
    @Indexed
    private Long storyId;

    /**
     * 场景ID
     */
    @Indexed
    private Long sceneId;

    /**
     * 会话ID
     */
    @Indexed
    private String storySessionId;
    @Indexed
    private String sceneSessionId;
    /**
     * 用户名
     */
    @Indexed
    private String username;


    private Long agentId;
    private String agentName;


    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息状态：SENDING(发送中), SENT(已发送), DELIVERED(已送达),
     * READ(已读), FAILED(发送失败)
     */
    private String status;
    private String role;
    /**
     * 回复的消息ID
     */
    private String replyToMessageId;

    /**
     * 提及的用户列表
     */
    private List<String> mentionedUsers;

    /**
     * 相关的智能体ID列表
     */
    private List<Long> relatedAgents;


    /**
     * 创建时间
     */
    @Indexed
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 消息序号（在会话中的顺序）
     */
    @Indexed
    private Long sequenceNumber;

} 