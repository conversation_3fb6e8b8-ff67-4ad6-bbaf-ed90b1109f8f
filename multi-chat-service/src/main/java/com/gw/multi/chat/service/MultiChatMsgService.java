package com.gw.multi.chat.service;

import com.gw.multi.chat.entity.MultiChatMsg;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 多智能体群聊消息服务
 * <p>
 * 负责消息的持久化、查询和管理
 */
public interface MultiChatMsgService {

    /**
     * 保存消息
     *
     * @param message 消息对象
     * @return 保存后的消息
     */
    MultiChatMsg saveMessage(MultiChatMsg message);

    /**
     * 批量保存消息
     *
     * @param messages 消息列表
     * @return 保存后的消息列表
     */
    List<MultiChatMsg> saveMessages(List<MultiChatMsg> messages);

    /**
     * 根据会话ID查询消息历史
     *
     * @param sessionId 会话ID
     * @param pageable  分页参数
     * @return 消息分页结果
     */
    Page<MultiChatMsg> findBySessionId(Long storyId, String sessionId, Pageable pageable);

    /**
     * 根据会话ID查询最近的消息
     *
     * @param sessionId 会话ID
     * @param limit     消息数量限制
     * @return 消息列表
     */
    List<MultiChatMsg> findRecentMessages(Long storyId, String sessionId, int limit);

    /**
     * 根据故事ID查询消息
     *
     * @param storyId  故事ID
     * @param pageable 分页参数
     * @return 消息分页结果
     */
    Page<MultiChatMsg> findByStoryId(Long storyId, Pageable pageable);

    /**
     * 根据用户名查询消息
     *
     * @param username 用户名
     * @param pageable 分页参数
     * @return 消息分页结果
     */
    Page<MultiChatMsg> findByUsername(Long storyId, String username, Pageable pageable);

    /**
     * 根据故事ID和用户名查询消息
     *
     * @param storyId  故事ID
     * @param username 用户名
     * @param pageable 分页参数
     * @return 消息分页结果
     */
    Page<MultiChatMsg> findByStoryIdAndUsername(Long storyId, String username, Pageable pageable);

    /**
     * 根据故事ID和场景ID查询消息
     *
     * @param storyId  故事ID
     * @param sceneId  场景ID
     * @param pageable 分页参数
     * @return 消息分页结果
     */
    Page<MultiChatMsg> findByStoryIdAndSceneId(Long storyId, Long sceneId, Pageable pageable);
    Page<MultiChatMsg> pageBySceneSessionIdAndStoryIdAndSceneId(String sceneSessionId,Long storyId, Long sceneId, Pageable pageable);

    List<MultiChatMsg> findByStoryIdAndSceneSessionId(Long storyId, String sceneSessionId, Integer limit);

    /**
     * 根据多个条件查询消息
     *
     * @param storyId  故事ID（可选）
     * @param username 用户名（可选）
     * @param sceneId  场景ID（可选）
     * @param pageable 分页参数
     * @return 消息分页结果
     */
    Page<MultiChatMsg> findByConditions(Long storyId, String username, Long sceneId, Pageable pageable);

    /**
     * 统计会话消息数量
     *
     * @param sessionId 会话ID
     * @return 消息总数
     */
    long countBySessionId(Long storyId, String sessionId);

    /**
     * 根据多个条件统计消息数量
     *
     * @param storyId  故事ID（可选）
     * @param username 用户名（可选）
     * @param sceneId  场景ID（可选）
     * @return 消息总数
     */
    long countByConditions(Long storyId, String username, Long sceneId);

    /**
     * 删除指定时间之前的消息
     *
     * @param sessionId  会话ID
     * @param beforeTime 时间阈值
     * @return 删除的消息数量
     */
    long deleteMessagesBefore(Long storyId, String sessionId, LocalDateTime beforeTime);

    /**
     * 根据消息ID查询消息
     *
     * @param messageId 消息ID
     * @return 消息对象
     */
    MultiChatMsg findById(Long storyId, String messageId);

    /**
     * 更新消息状态
     *
     * @param messageId 消息ID
     * @param status    新状态
     */
    void updateMessageStatus(Long storyId, String messageId, String status);

    /**
     * 获取集合名称（根据故事ID分表）
     *
     * @param storyId 故事ID
     * @return 集合名称
     */
    String getCollectionName(Long storyId);

    List<MultiChatMsg> findAllMessagesBySequenceNumber(Long storyId, Long sceneId,Long sequenceNumber);
    List<MultiChatMsg> findAllMessages(Long storyId, Long sceneId, Long startSequenceNumber, Long endSequenceNum);
} 