package com.gw.multi.chat.config;

import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.boot.autoconfigure.web.WebProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.reactive.config.ResourceHandlerRegistry;
import org.springframework.web.reactive.config.WebFluxConfigurer;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.ServerResponse;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.web.reactive.function.server.RequestPredicates.GET;
import static org.springframework.web.reactive.function.server.RouterFunctions.route;

/**
 * WebFlux 配置类
 * 处理静态资源和特殊路径配置
 */
@Configuration
public class WebFluxConfig implements WebFluxConfigurer {

    /**
     * 提供 WebProperties bean，用于错误处理
     */
    @Bean
    public WebProperties webProperties() {
        return new WebProperties();
    }

    /**
     * 提供 HttpMessageConverters bean，用于Feign客户端
     * 这是在WebFlux环境中使用OpenFeign所必需的配置
     */
    @Bean
    public HttpMessageConverters httpMessageConverters() {
        List<HttpMessageConverter<?>> converters = new ArrayList<>();
        converters.add(new MappingJackson2HttpMessageConverter());
        return new HttpMessageConverters(converters);
    }

    /**
     * 配置静态资源处理
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置 Swagger UI 静态资源
        registry.addResourceHandler("/swagger-ui/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/swagger-ui/")
                .resourceChain(false);

        // 配置其他静态资源
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/")
                .resourceChain(false);

        // 配置静态文件目录
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/")
                .resourceChain(false);
    }

    /**
     * 处理 favicon.ico 请求
     * 返回 204 No Content 避免 404 错误
     */
    @Bean
    public RouterFunction<ServerResponse> faviconRoute() {
        return route(GET("/favicon.ico"),
                request -> ServerResponse.noContent().build())
                .andRoute(GET("/swagger-ui/favicon.ico"),
                        request -> ServerResponse.noContent().build())
                .andRoute(GET("/api/v1/multi-chat/swagger-ui/favicon.ico"),
                        request -> ServerResponse.noContent().build())
                .andRoute(request -> request.path().endsWith("/favicon.ico"),
                        request -> ServerResponse.noContent().build());
    }

    /**
     * 如果有实际的 favicon.ico 文件，可以使用这个配置
     * 需要将 favicon.ico 文件放在 src/main/resources/static/ 目录下
     */
    /*
    @Bean
    public RouterFunction<ServerResponse> faviconRouteWithFile() {
        return route(GET("/favicon.ico"),
                request -> {
                    Resource favicon = new ClassPathResource("static/favicon.ico");
                    if (favicon.exists()) {
                        return ServerResponse.ok()
                                .contentType(MediaType.valueOf("image/x-icon"))
                                .bodyValue(favicon);
                    } else {
                        return ServerResponse.noContent().build();
                    }
                });
    }
    */
} 