package com.gw.multi.chat.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 火山方舟大模型服务配置
 */
@Configuration
@ConfigurationProperties(prefix = "volcanoark")
@Data
public class VolcanoArkConfig {

    /**
     * API Key
     */
    private String apiKey;

    /**
     * 服务基础URL
     */
    private String baseUrl = "https://ark.cn-beijing.volces.com";

    /**
     * 默认模型ID
     */
    private String defaultModelId = "doubao-1.5-pro-32k";

    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 30000;

    /**
     * 上下文缓存生存时间（秒）
     */
    private long contextCacheTtl = 3600; // 默认值与之前硬编码的值相同

    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 120000;

    /**
     * 重试次数
     */
    private int retryTimes = 2;

    /**
     * 最大并发连接数
     */
    private int maxConnections = 10;

    /**
     * 连接池保持时间（秒）
     */
    private long keepAliveDuration = 60;

}