package com.gw.multi.chat.config;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 历史记录压缩任务专用线程池配置
 * 支持高并发处理和自定义拒绝策略
 */
@Configuration
public class HistoryCompressThreadPoolConfig {

    private static final Logger log = LogManager.getLogger(HistoryCompressThreadPoolConfig.class);

    @Autowired
    private HistoryCompressConfig compressConfig;

    /**
     * 压缩任务线程池
     */
    @Bean("historyCompressExecutor")
    public Executor historyCompressExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 核心线程数
        executor.setCorePoolSize(compressConfig.getConcurrentThreads());

        // 最大线程数
        executor.setMaxPoolSize(compressConfig.getConcurrentThreads() * 2);

        // 队列容量
        executor.setQueueCapacity(200);

        // 线程名前缀
        executor.setThreadNamePrefix("history-compress-");

        // 空闲线程的存活时间
        executor.setKeepAliveSeconds(60);

        // 拒绝策略 - 自定义处理
        executor.setRejectedExecutionHandler(new CustomRejectedExecutionHandler());

        // 等待任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);

        // 等待时间
        executor.setAwaitTerminationSeconds(30);

        executor.initialize();

        log.info("历史压缩任务线程池初始化完成 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}",
                compressConfig.getConcurrentThreads(),
                compressConfig.getConcurrentThreads() * 2,
                200);

        return executor;
    }

    /**
     * AI请求专用线程池 - 用于处理AI压缩请求
     */
    @Bean("aiRequestExecutor")
    public Executor aiRequestExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // AI请求通常较慢，使用较少的线程
        executor.setCorePoolSize(Math.max(1, compressConfig.getConcurrentThreads() / 2));
        executor.setMaxPoolSize(compressConfig.getConcurrentThreads());
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("ai-compress-");
        executor.setKeepAliveSeconds(120);

        // AI请求失败时记录日志
        executor.setRejectedExecutionHandler(new AIRequestRejectedExecutionHandler());

        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);

        executor.initialize();

        log.info("AI压缩请求线程池初始化完成 - 核心线程数: {}, 最大线程数: {}",
                Math.max(1, compressConfig.getConcurrentThreads() / 2),
                compressConfig.getConcurrentThreads());

        return executor;
    }

    /**
     * 自定义拒绝执行处理器
     */
    private static class CustomRejectedExecutionHandler implements RejectedExecutionHandler {
        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
            log.warn("历史压缩任务被拒绝执行 - 活跃线程数: {}, 队列大小: {}, 任务总数: {}",
                    executor.getActiveCount(),
                    executor.getQueue().size(),
                    executor.getTaskCount());

            // 尝试在调用线程中执行
            if (!executor.isShutdown()) {
                try {
                    r.run();
                } catch (Exception e) {
                    log.error("在调用线程中执行压缩任务失败", e);
                }
            }
        }
    }

    /**
     * AI请求拒绝执行处理器
     */
    private static class AIRequestRejectedExecutionHandler implements RejectedExecutionHandler {
        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
            log.error("AI压缩请求被拒绝执行 - 线程池已满，活跃线程数: {}, 队列大小: {}",
                    executor.getActiveCount(),
                    executor.getQueue().size());

            // AI请求被拒绝时不在调用线程执行，避免阻塞主流程
            // 可以考虑添加到延迟队列或告警系统
        }
    }
} 