# Vue.js WebSocket 聊天客户端

基于 Vue.js 和 STOMP over WebSocket 实现的聊天客户端，用于与后端 Spring WebSocket 服务进行通信。

## 功能特性

- 与 Spring WebSocket 后端集成
- 支持 STOMP 协议
- JWT 认证
- 实时消息发送和接收
- 会话管理
- 自动重连机制
- 断线检测和恢复

## 目录结构

```
├── websocket-client.js   # WebSocket 服务类
├── ChatComponent.vue     # 聊天组件
└── App.vue               # 示例应用
```

## 安装依赖

```bash
npm install sockjs-client @stomp/stompjs uuid dayjs axios
```

## 使用方法

### 1. 初始化 WebSocket 服务

```javascript
import WebSocketService from './websocket-client.js';

// 创建 WebSocket 服务实例
const wsService = new WebSocketService('http://your-api-base-url', 'your-jwt-token');

// 连接到服务器
await wsService.connect();

// 订阅用户消息
const subscriptionId = wsService.subscribeToUserMessages('username', (message) => {
  console.log('收到消息:', message);
});

// 发送消息
await wsService.sendMessage({
  sessionId: 'session-id',
  sender: 'username',
  agentId: 123,
  content: '你好，这是一条测试消息',
});

// 断开连接
wsService.disconnect();
```

### 2. 在 Vue 组件中使用

参考 `ChatComponent.vue` 和 `App.vue`，它们提供了完整的聊天界面实现示例。

## WebSocket 服务配置

### WebSocketService 类

`websocket-client.js` 中的 `WebSocketService` 类提供了以下主要方法：

- `constructor(baseApiUrl, authToken)` - 初始化 WebSocket 服务
- `connect()` - 连接到 WebSocket 服务器
- `disconnect()` - 断开 WebSocket 连接
- `subscribeToUserMessages(username, callback)` - 订阅用户消息
- `unsubscribe(subscriptionId)` - 取消订阅
- `sendMessage(messageData)` - 发送聊天消息

### 消息格式

发送消息时需要提供符合 `ChatMessageDto` 的对象：

```javascript
{
    id: 'uuid', // 可选，不提供时会自动生成
        sessionId
:
    'session-id', // 必填，会话ID
        sender
:
    'username', // 必填，发送者用户名
        agentId
:
    123, // 必填，AI智能体ID
        role
:
    'user', // 可选，默认为 'user'
        type
:
    'USER', // 可选，默认为 'USER'，可选值: 'USER', 'AI', 'SYSTEM'
        content
:
    '消息内容', // 必填，消息文本内容
        contentType
:
    'text', // 可选，默认为 'text'
        status
:
    'SENDING' // 可选，默认为 'SENDING'
}
```

## 与后端集成

本客户端专为 Spring WebSocket 后端设计，使用 STOMP 协议通信。确保后端满足以下要求：

1. WebSocket 端点: `/api/v1/ws`
2. 消息处理端点: `/app/api/v1/ws/chat/send_message`
3. 用户消息目的地: `/user/{username}/queue/messages`
4. 支持 JWT 认证头: `Authorization: Bearer <token>`

## 注意事项

1. 确保 API 基础 URL 和 JWT 令牌正确设置
2. 处理连接错误和重连逻辑
3. 消息发送前确保连接状态正常
4. 组件销毁时注意清理资源（取消订阅和断开连接）

## 自定义

您可以根据需要修改样式和行为：

1. 修改 `ChatComponent.vue` 中的样式来自定义聊天界面
2. 扩展 `WebSocketService` 类以添加更多功能
3. 调整重连参数和超时设置
