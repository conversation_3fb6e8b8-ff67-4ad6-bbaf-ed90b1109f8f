# WebSocket??
# ??multi-chat-service?chat-service???WebSocket??
# ========== ???? ==========
# ?????
websocket.max-connections=5000
# ?????? - 30??
websocket.idle-timeout=1800000
# ========== ????? ==========
# ????????
websocket.thread-pool.core-size=20
# ????????
websocket.thread-pool.max-size=100
# ????
websocket.thread-pool.queue-capacity=2000
# ??????
websocket.thread-pool.keep-alive-seconds=60
# ========== ???? ==========
# ??????
websocket.heartbeat.enabled=true
# ??????
websocket.heartbeat.interval-seconds=30
# ??????
websocket.heartbeat.timeout-seconds=300
# ========== ????? ==========
# ????????
websocket.robustness.session-validation-tolerance-ms=30000
# ????????
websocket.robustness.session-close-tolerance-ms=10000
# ????????
websocket.robustness.heartbeat-failure-retry-count=5
# ????????
websocket.robustness.session-recovery-attempts=2
# ????????
websocket.robustness.session-recovery-delay-ms=1000
# ??????????
websocket.robustness.enable-session-auto-recovery=true
# ?????????????
websocket.robustness.enable-progressive-heartbeat-timeout=true
# ??????????
websocket.robustness.enable-session-state-repair=true
# ?????????
websocket.robustness.metadata-sync-check-interval-ms=300000
# ?????????
websocket.robustness.max-concurrent-recovery-operations=10
# ????????
websocket.robustness.session-recovery-timeout-ms=5000
# ???????????
websocket.robustness.enable-verbose-robustness-logging=true
# ????????
websocket.robustness.session-health-check-interval-ms=120000
# ??????????
websocket.robustness.enable-session-health-check=true
# ========== ???? ==========
# ?????
websocket.session.max-sessions=5000
# ??????
websocket.session.timeout-minutes=30
# ????????
websocket.session.cleanup-interval-minutes=5
# ========== ?????? ==========
# ??????
websocket.message-timeout-seconds=60
# ???????
websocket.lock-timeout-ms=500
# ========== ???? ==========
# ????????
websocket.cache.session.max-size=10000
# ????????
websocket.cache.session.expire-minutes=60
# ??????????
websocket.cache.active-time.max-size=10000
# ??????????
websocket.cache.active-time.expire-minutes=30
# ========== ???? ==========
# ????????
websocket.metrics.enabled=true
# ????????
websocket.metrics.collection-interval-minutes=5
# ========== ???? ==========
# ????????
websocket.backpressure.enabled=true
# ?????
websocket.backpressure.buffer-size=1000
