package com.gw.chat.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "评论数据")
public class CommentVO {

    @Schema(description = "评论ID")
    private String id;

    @Schema(description = "评论者用户名")
    private String commenter;

    @Schema(description = "用户昵称")
    private String commenterNickname;

    @Schema(description = "AI助手ID")
    private Long agentId;


    @Schema(description = "评论内容")
    private String content;

    @Schema(description = "父评论ID")
    private String parentId;

    @Schema(description = "回复目标评论ID")
    private String replyToId;

    @Schema(description = "回复目标用户名")
    private String replyToUsername;

    @Schema(description = "评论深度")
    private Integer depth;

    @Schema(description = "回复数量")
    private Integer replyCount;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
} 