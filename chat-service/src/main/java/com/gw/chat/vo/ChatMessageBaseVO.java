package com.gw.chat.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gw.chat.entity.ChatMessage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatMessageBaseVO {
    private String chatId;
    private String content;
    private String role;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    public ChatMessageBaseVO(ChatMessage entity){
        this.chatId = entity.getId();
        this.content = entity.getContent();
        this.updateTime = entity.getUpdatedAt();
        this.role = entity.getRole();
    }
}
