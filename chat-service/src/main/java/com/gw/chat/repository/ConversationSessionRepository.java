package com.gw.chat.repository;

import com.gw.chat.entity.ConversationSession;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.mongodb.repository.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 对话会话数据访问接口
 */
@Repository
public interface ConversationSessionRepository extends MongoRepository<ConversationSession, String> {

    /**
     * 根据用户ID分页查询会话
     */
    Page<ConversationSession> findByUsernameAndStatusOrderByUpdatedAtDesc(
            String username, ConversationSession.SessionStatus status, Pageable pageable);

    /**
     * 根据用户ID和会话ID查询会话
     */
    Optional<ConversationSession> findByIdAndUsername(String id, String username);

    @NotNull Optional<ConversationSession> findById(@NotNull String id);

    /**
     * 查询用户的活跃会话数量
     */
    long countByUsernameAndStatus(String username, ConversationSession.SessionStatus status);

    /**
     * 根据用户ID和智能体ID查询最近创建的会话
     */
    Optional<ConversationSession> findFirstByUsernameAndAgentIdOrderByCreatedAtDesc(String username, Long agentId);

    Optional<ConversationSession> findFirstByUsernameAndAgentIdAndStatusOrderByCreatedAtDesc(String username, Long agentId, ConversationSession.SessionStatus status);

    List<ConversationSession> findByAgentIdAndStatusOrderByCreatedAtDesc(Long agentId, ConversationSession.SessionStatus status);

    /**
     * 根据用户名和智能体ID更新会话状态
     */
    @Query("{ 'username': ?0, 'agentId': ?1 }")
    @Update("{ '$set': { 'status': ?2 } }")
    long updateStatusByUsernameAndAgentId(String username, Long agentId, ConversationSession.SessionStatus status);
}