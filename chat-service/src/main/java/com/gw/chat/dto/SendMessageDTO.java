package com.gw.chat.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class SendMessageDTO {
    @Schema(description = "会话ID")
    @NotBlank(message = "会话ID不能为空")
    private String conversationId;

    @Schema(description = "机器人ID")
    @NotBlank(message = "机器人ID不能为空")
    private String botId;

    @Schema(description = "消息内容")
    @NotBlank(message = "消息内容不能为空")
    private String message;
}