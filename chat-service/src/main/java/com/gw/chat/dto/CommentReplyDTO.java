package com.gw.chat.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
@Schema(description = "回复评论请求")
public class CommentReplyDTO {

    @Schema(description = "父评论ID")
    @NotBlank(message = "父评论ID不能为空")
    private String parentId;

    @Schema(description = "回复目标评论ID")
    @NotBlank(message = "回复目标评论ID不能为空")
    private String replyToId;

    @Schema(description = "回复目标用户名")
    @NotBlank(message = "回复目标用户名不能为空")
    private String replyToUsername;


    @Schema(description = "评论内容")
    @NotBlank(message = "评论内容不能为空")
    private String content;
} 