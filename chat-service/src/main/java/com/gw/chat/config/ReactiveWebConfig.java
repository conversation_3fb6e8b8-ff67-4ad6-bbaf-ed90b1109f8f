package com.gw.chat.config;

import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.boot.autoconfigure.web.WebProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.codec.ServerCodecConfigurer;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.reactive.config.ResourceHandlerRegistry;
import org.springframework.web.reactive.config.WebFluxConfigurer;

import java.util.ArrayList;
import java.util.List;

/**
 * 响应式 Web 配置
 */
@Configuration
public class ReactiveWebConfig implements WebFluxConfigurer {

    /**
     * 提供 WebProperties bean，用于错误处理
     */
    @Bean
    public WebProperties webProperties() {
        return new WebProperties();
    }

    /**
     * 提供 ServerCodecConfigurer bean，用于响应式错误处理器
     */
    @Bean
    public ServerCodecConfigurer serverCodecConfigurer() {
        return ServerCodecConfigurer.create();
    }

    /**
     * 提供 HttpMessageConverters bean，用于Feign客户端
     */
    @Bean
    public HttpMessageConverters httpMessageConverters() {
        List<HttpMessageConverter<?>> converters = new ArrayList<>();
        converters.add(new MappingJackson2HttpMessageConverter());
        return new HttpMessageConverters(converters);
    }

    /**
     * 配置静态资源路径，以支持Swagger UI
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // SpringDoc/Swagger UI 资源路径配置
        registry.addResourceHandler("/swagger-ui.html**", "/api/v1/chat/swagger-ui.html**")
                .addResourceLocations("classpath:/META-INF/resources/swagger-ui.html",
                        "classpath:/META-INF/resources/");

        registry.addResourceHandler("/webjars/**", "/api/v1/chat/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");

        registry.addResourceHandler("/swagger-resources/**", "/api/v1/chat/swagger-resources/**",
                        "/api/v1/chat/swagger-ui/**", "/swagger-ui/**")
                .addResourceLocations("classpath:/META-INF/resources/swagger-resources/",
                        "classpath:/META-INF/resources/");

        registry.addResourceHandler("/v3/api-docs/**", "/api/v1/chat/v3/api-docs/**")
                .addResourceLocations("classpath:/META-INF/resources/v3/api-docs/",
                        "classpath:/META-INF/resources/");
    }
}