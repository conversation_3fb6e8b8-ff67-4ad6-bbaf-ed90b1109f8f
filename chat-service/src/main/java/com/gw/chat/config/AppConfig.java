package com.gw.chat.config;

import com.gw.chat.repository.ConversationCommentRepository;
import com.gw.chat.repository.ConversationLikeRepository;
import com.gw.chat.repository.ConversationSessionRepository;
import com.gw.chat.service.AgentCozeRemoteService;
import com.gw.chat.service.AgentRemoteVolvanoService;
import com.gw.chat.service.ChatMessageService;
import com.gw.chat.service.WebSocketService;
import com.gw.chat.service.impl.ChatServiceImpl;
import com.gw.common.agent.service.AgentProxyService;
import jakarta.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Primary;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * 应用配置类
 */
@Configuration
@Log4j2
public class AppConfig {

    private static final Logger log = LogManager.getLogger(AppConfig.class);
    private final ApplicationEventPublisher eventPublisher;

    public AppConfig(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
        log.info("AppConfig constructor called with eventPublisher: {}", eventPublisher);
    }

    @PostConstruct
    public void init() {
        log.info("AppConfig initialized, eventPublisher: {}", eventPublisher);
    }

    /**
     * 显式定义WebSocketService Bean
     */
    @Bean
    @Primary
    public WebSocketService webSocketService() {
        log.info("Creating WebSocketService bean with eventPublisher: {}", eventPublisher);
        WebSocketService service = new WebSocketService(eventPublisher);
        log.info("WebSocketService bean created: {}", service);
        return service;
    }

    /**
     * 显式定义ChatServiceImpl Bean，手动注入所有依赖
     * 并依赖于webSocketService以确保Bean创建顺序
     */
    @Bean
    @Primary
    @DependsOn("webSocketService")
    public ChatServiceImpl chatService(
            ConversationSessionRepository sessionRepository,
            ChatMessageService chatMessageService,
            RedisTemplate<String, Object> redisTemplate,
            WebSocketService webSocketService,
            AgentCozeRemoteService agentCozeRemoteService, AgentRemoteVolvanoService agentRemoteVolvanoService,
            AgentProxyService agentProxyService,
            MongoTemplate mongoTemplate,
            ConversationCommentRepository commentRepository,
            ConversationLikeRepository likeRepository, CacheProperties cacheProperties) {
        log.info("Creating ChatServiceImpl bean with components:");
        log.info("- sessionRepository: {}", sessionRepository);
        log.info("- chatMessageService: {}", chatMessageService);
        log.info("- redisTemplate: {}", redisTemplate);
        log.info("- webSocketService: {}", webSocketService);
        log.info("- agentRemoteService: {}", agentCozeRemoteService);

        ChatServiceImpl service = new ChatServiceImpl(
                agentProxyService,
                sessionRepository,
                chatMessageService,
                redisTemplate,
                agentCozeRemoteService,
                agentRemoteVolvanoService,
                mongoTemplate,
                commentRepository,
                likeRepository, cacheProperties);
        log.info("ChatServiceImpl bean created: {}", service);
        return service;
    }
}