package com.gw.chat.config;

import lombok.extern.log4j.Log4j2;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.support.DefaultHandshakeHandler;

import java.security.Principal;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * WebSocket握手处理器，主要处理用户身份验证
 * 优化版本：提高高并发场景下的性能
 */
@Component
@Log4j2
public class WebSocketHandshakeHandler extends DefaultHandshakeHandler {

    // 用户Principal缓存，提高频繁连接的性能
    private static final Map<String, CachedPrincipal> PRINCIPAL_CACHE = new ConcurrentHashMap<>();

    // 匿名用户的共享Principal实例，避免重复创建
    private static final Principal ANONYMOUS_PRINCIPAL = new UserPrincipal("anonymous");

    // 缓存过期时间（分钟）
    private static final long CACHE_EXPIRY_MINUTES = 30;

    // 用户信息的头部名称数组，用于批量处理
    private static final String[] USER_HEADER_NAMES = {
            "X-User-Id", "X-User-Name", "X-User-RealName", "X-User-Roles"
    };

    @Override
    protected Principal determineUser(ServerHttpRequest request, WebSocketHandler wsHandler,
                                      Map<String, Object> attributes) {
        // 首先检查父类是否已确定Principal
        Principal principal = super.determineUser(request, wsHandler, attributes);
        if (principal != null) {
            return principal;
        }

        // 高效地批量处理用户信息头
        String username = null;
        for (String headerName : USER_HEADER_NAMES) {
            String value = (String) attributes.get(headerName);
            if (value == null) {
                value = request.getHeaders().getFirst(headerName);
            }

            if (value != null) {
                attributes.put(headerName, value);
                if ("X-User-Name".equals(headerName)) {
                    username = value;
                }
            }
        }

        // 如果有用户名，尝试从缓存获取或创建Principal
        if (username != null) {
            // 检查缓存中是否有有效的Principal
            CachedPrincipal cachedPrincipal = PRINCIPAL_CACHE.get(username);
            if (cachedPrincipal != null && !cachedPrincipal.isExpired()) {
                return cachedPrincipal.getPrincipal();
            }

            // 创建新的Principal并缓存
            Principal userPrincipal = new UserPrincipal(username);
            PRINCIPAL_CACHE.put(username, new CachedPrincipal(userPrincipal));

            // 清理过期缓存（异步，不阻塞主流程）
            if (PRINCIPAL_CACHE.size() > 1000) { // 当缓存大小超过阈值时清理
                cleanExpiredCache();
            }

            return userPrincipal;
        }

        // 对于匿名连接，返回共享的匿名Principal实例
        return ANONYMOUS_PRINCIPAL;
    }

    /**
     * 清理过期的Principal缓存
     */
    private void cleanExpiredCache() {
        PRINCIPAL_CACHE.entrySet().removeIf(entry -> entry.getValue().isExpired());
    }

    /**
     * 带过期时间的Principal缓存项
     */
    private static class CachedPrincipal {
        private final Principal principal;
        private final long expiryTime;

        public CachedPrincipal(Principal principal) {
            this.principal = principal;
            this.expiryTime = System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(CACHE_EXPIRY_MINUTES);
        }

        public Principal getPrincipal() {
            return principal;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() > expiryTime;
        }
    }

    /**
     * 优化的Principal实现，增加了equals和hashCode方法
     */
    public static final class UserPrincipal implements Principal {
        private final String name;

        public UserPrincipal(String name) {
            this.name = name;
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            UserPrincipal that = (UserPrincipal) o;
            return Objects.equals(name, that.name);
        }

        @Override
        public int hashCode() {
            return Objects.hash(name);
        }

        @Override
        public String toString() {
            return "UserPrincipal{name='" + name + "'}";
        }
    }
}