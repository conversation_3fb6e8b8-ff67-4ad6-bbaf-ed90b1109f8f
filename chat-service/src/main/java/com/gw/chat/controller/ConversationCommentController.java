package com.gw.chat.controller;

import com.gw.chat.config.ReactiveUserContextUtil;
import com.gw.chat.dto.*;
import com.gw.chat.service.ChatService;
import com.gw.chat.service.ConversationCommentService;
import com.gw.chat.vo.CommentVO;
import com.gw.chat.vo.CommentWithRepliesVO;
import com.gw.common.dto.PageBaseRequest;
import com.gw.common.dto.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import static com.gw.common.exception.BusinessExceptionCode.FAIL_CODE;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/chat/comments")
@Tag(name = "会话评论", description = "会话评论相关接口")
@Log4j2
public class ConversationCommentController {

    private final ConversationCommentService commentService;
    private final ChatService chatService;

    @Operation(summary = "创建评论", description = "创建新的评论", responses = {
            @ApiResponse(responseCode = "200", description = "创建成功"),
            @ApiResponse(responseCode = "400", description = "参数错误")
    })
    @PostMapping("/create")
    public Mono<ResponseResult<CommentVO>> createComment(@RequestBody @Valid CommentCreateDTO commentDTO) {
        return ReactiveUserContextUtil.getCurrentUsername()
                .flatMap(username -> {
                    try {
                        ConversationSessionDto session = chatService.getSessionById(commentDTO.getSessionId());
                        CommentVO comment = commentService.createComment(username, commentDTO, session.getAgentId(), session.getUsername());
                        return Mono.just(ResponseResult.success(comment));
                    } catch (Exception e) {
                        log.error("创建评论失败", e);
                        return Mono.just(ResponseResult.failure(FAIL_CODE, e.getMessage()));
                    }
                });
    }

    @Operation(summary = "回复评论", description = "回复已有的评论", responses = {
            @ApiResponse(responseCode = "200", description = "回复成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "404", description = "评论不存在")
    })
    @PostMapping("/reply")
    public Mono<ResponseResult<CommentVO>> replyToComment(@RequestBody @Valid CommentReplyDTO replyDTO) {
        return ReactiveUserContextUtil.getCurrentUsername()
                .flatMap(username -> {
                    try {
                        CommentVO reply = commentService.replyToComment(username, replyDTO);
                        return Mono.just(ResponseResult.success(reply));
                    } catch (Exception e) {
                        log.error("回复评论失败", e);
                        return Mono.just(ResponseResult.failure(FAIL_CODE, e.getMessage()));
                    }
                });
    }

    @Operation(summary = "删除评论", description = "删除指定的评论", responses = {
            @ApiResponse(responseCode = "200", description = "删除成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "404", description = "评论不存在")
    })
    @PostMapping("/delete")
    public Mono<ResponseResult<Void>> deleteComment(@RequestBody @Valid CommentIdDTO commentIdDTO) {
        return ReactiveUserContextUtil.getCurrentUsername()
                .flatMap(username -> {
                    try {
                        commentService.deleteComment(commentIdDTO.getCommentId(), username);
                        return Mono.just(ResponseResult.success(null));
                    } catch (Exception e) {
                        log.error("删除评论失败", e);
                        return Mono.just(ResponseResult.failure(FAIL_CODE, e.getMessage()));
                    }
                });
    }

    @Operation(summary = "获取评论详情", description = "获取指定评论的详细信息", responses = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "404", description = "评论不存在")
    })
    @PostMapping("/get")
    public Mono<ResponseResult<CommentVO>> getComment(@RequestBody @Valid CommentIdDTO commentIdDTO) {
        try {
            CommentVO comment = commentService.getComment(commentIdDTO.getCommentId());
            return Mono.just(ResponseResult.success(comment));
        } catch (Exception e) {
            log.error("获取评论详情失败", e);
            return Mono.just(ResponseResult.failure(FAIL_CODE, e.getMessage()));
        }
    }

    @Operation(summary = "获取评论及回复", description = "获取指定评论及其所有回复", responses = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "404", description = "评论不存在")
    })
    @PostMapping("/get-with-replies")
    public Mono<ResponseResult<CommentWithRepliesVO>> getCommentWithReplies(@RequestBody @Valid CommentIdDTO commentIdDTO) {
        try {
            CommentWithRepliesVO commentWithReplies = commentService.getCommentWithReplies(commentIdDTO.getCommentId());
            return Mono.just(ResponseResult.success(commentWithReplies));
        } catch (Exception e) {
            log.error("获取评论及回复失败", e);
            return Mono.just(ResponseResult.failure(FAIL_CODE, e.getMessage()));
        }
    }

    @Operation(summary = "获取会话评论列表", description = "分页获取指定会话的评论列表")
    @PostMapping("/by-conversation")
    public Mono<ResponseResult<Page<CommentWithRepliesVO>>> getCommentsByConversationId(
            @RequestBody @Valid PageBaseRequest<CommentByConversationDTO> params) {
        try {
            if (params.getFilter() == null) {
                throw new IllegalArgumentException("会话ID不能为空");
            }
            int page = params.getCurrent() - 1;
            int size = params.getPageSize();
            Pageable pageable = PageRequest.of(page, size);
            Page<CommentWithRepliesVO> comments = commentService.getCommentsBySessionId(
                    params.getFilter().getSessionId(), pageable);
            return Mono.just(ResponseResult.success(comments));
        } catch (Exception e) {
            log.error("获取会话评论列表失败", e);
            return Mono.just(ResponseResult.failure(FAIL_CODE, e.getMessage()));
        }
    }

    @Operation(summary = "获取用户评论列表", description = "分页获取当前用户的评论列表")
    @PostMapping("/by-user")
    public Mono<ResponseResult<Page<CommentVO>>> getCommentsByUsername(@RequestBody @Valid PageBaseRequest<CommentByAgentDTO> params) {
        return ReactiveUserContextUtil.getCurrentUsername()
                .flatMap(username -> {
                    try {
                        if (params.getFilter() == null) {
                            throw new IllegalArgumentException("智能体ID不能为空");
                        }
                        int page = params.getCurrent() - 1;
                        int size = params.getPageSize();
                        Pageable pageable = PageRequest.of(page, size);
                        Page<CommentVO> comments = commentService.getCommentsByUsername(
                                username, params.getFilter().getAgentId(), pageable);
                        return Mono.just(ResponseResult.success(comments));
                    } catch (Exception e) {
                        log.error("获取用户评论列表失败", e);
                        return Mono.just(ResponseResult.failure(FAIL_CODE, e.getMessage()));
                    }
                });
    }
} 