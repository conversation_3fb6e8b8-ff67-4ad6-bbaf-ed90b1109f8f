package com.gw.chat.controller;

import com.gw.chat.config.ReactiveUserContextUtil;
import com.gw.chat.dto.*;
import com.gw.chat.service.ChatService;
import com.gw.chat.service.ConversationLikeService;
import com.gw.chat.vo.ConversationLikeVO;
import com.gw.chat.vo.LikeStatsVO;
import com.gw.common.dto.PageBaseRequest;
import com.gw.common.dto.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import static com.gw.common.exception.BusinessExceptionCode.FAIL_CODE;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/chat/likes")
@Tag(name = "会话点赞", description = "会话点赞相关接口")
@Log4j2
public class ConversationLikeController {

    private final ConversationLikeService likeService;
    private final ChatService chatService;

    @Operation(summary = "点赞/取消点赞会话", description = "用户对会话进行点赞或取消点赞", responses = {
            @ApiResponse(responseCode = "200", description = "操作成功"),
            @ApiResponse(responseCode = "400", description = "参数错误")
    })
    @PostMapping("/action")
    public Mono<ResponseResult<ConversationLikeVO>> likeConversation(@RequestBody @Valid LikeActionDTO likeDTO) {
        return ReactiveUserContextUtil.getCurrentUsername()
                .flatMap(username -> {
                    try {
                        ConversationSessionDto session = chatService.getSessionById(likeDTO.getSessionId());
                        ConversationLikeVO like = likeService.likeConversation(username, likeDTO, session.getAgentId(), session.getUsername());
                        return Mono.just(ResponseResult.success(like));
                    } catch (Exception e) {
                        log.error("点赞操作失败", e);
                        return Mono.just(ResponseResult.failure(FAIL_CODE, e.getMessage()));
                    }
                });
    }

    @Operation(summary = "获取会话点赞统计", description = "获取指定会话的点赞统计数据", responses = {
            @ApiResponse(responseCode = "200", description = "获取成功")
    })
    @PostMapping("/stats")
    public Mono<ResponseResult<LikeStatsVO>> getLikeStats(@RequestBody @Valid ConversationIdDTO idDTO) {
        return ReactiveUserContextUtil.getCurrentUsername()
                .flatMap(username -> {
                    try {
                        LikeStatsVO stats = likeService.getLikeStats(idDTO.getConversationId(), username);
                        return Mono.just(ResponseResult.success(stats));
                    } catch (Exception e) {
                        log.error("获取点赞统计失败", e);
                        return Mono.just(ResponseResult.failure(FAIL_CODE, e.getMessage()));
                    }
                });
    }

    @Operation(summary = "获取会话点赞列表", description = "分页获取指定会话的点赞记录")
    @PostMapping("/by-conversation")
    public Mono<ResponseResult<Page<ConversationLikeVO>>> getLikesByConversationId(
            @RequestBody @Valid PageBaseRequest<LikeByConversationDTO> params) {
        try {
            if (params.getFilter() == null) {
                throw new IllegalArgumentException("会话ID不能为空");
            }
            int page = params.getCurrent() - 1;
            int size = params.getPageSize();
            Pageable pageable = PageRequest.of(page, size);
            Page<ConversationLikeVO> likes = likeService.getLikesByConversationId(
                    params.getFilter().getSessionId(), pageable);
            return Mono.just(ResponseResult.success(likes));
        } catch (Exception e) {
            log.error("获取会话点赞列表失败", e);
            return Mono.just(ResponseResult.failure(FAIL_CODE, e.getMessage()));
        }
    }

    @Operation(summary = "获取用户点赞列表", description = "分页获取当前用户对指定AI助手的点赞记录")
    @PostMapping("/by-user")
    public Mono<ResponseResult<Page<ConversationLikeVO>>> getLikesByUsername(@RequestBody @Valid PageBaseRequest<LikeByAgentDTO> params) {
        return ReactiveUserContextUtil.getCurrentUsername()
                .flatMap(username -> {
                    try {
                        if (params.getFilter() == null) {
                            throw new IllegalArgumentException("智能体ID不能为空");
                        }
                        int page = params.getCurrent() - 1;
                        int size = params.getPageSize();
                        Pageable pageable = PageRequest.of(page, size);
                        Page<ConversationLikeVO> likes = likeService.getLikesByUsername(
                                username, params.getFilter().getAgentId(), pageable);
                        return Mono.just(ResponseResult.success(likes));
                    } catch (Exception e) {
                        log.error("获取用户点赞列表失败", e);
                        return Mono.just(ResponseResult.failure(FAIL_CODE, e.getMessage()));
                    }
                });
    }
} 