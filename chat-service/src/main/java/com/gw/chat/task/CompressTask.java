package com.gw.chat.task;

import com.gw.chat.config.CacheProperties;
import com.gw.chat.config.CompressTaskConfig;
import com.gw.chat.constant.ChatConstant;
import com.gw.chat.dto.ConversationSessionCompressDto;
import com.gw.chat.entity.ChatCompressMessage;
import com.gw.chat.entity.ChatMessage;
import com.gw.chat.service.AgentRemoteVolvanoService;
import com.gw.chat.service.ChatCompressMessageService;
import com.gw.chat.service.ChatMessageService;
import com.gw.chat.service.ChatService;
import com.gw.chat.util.MemoryMonitor;
import com.gw.chat.vo.ChatContextVO;
import com.gw.common.agent.constant.AgentCommonCacheConstant;
import com.gw.common.agent.service.AgentProxyService;
import com.gw.common.agent.vo.AgentBaseVO;
import com.gw.common.membership.constant.MemberCacheConstant;
import com.gw.common.membership.service.MembershipProxyService;
import com.gw.common.membership.vo.UserMembershipVO;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 聊天压缩任务处理类
 * 负责异步处理聊天消息压缩，使用专用线程池进行后台处理
 */
@Component
@Log4j2
@RequiredArgsConstructor
public class CompressTask {
    private final ChatCompressMessageService chatCompressMessageService;
    private final ChatService chatService;
    private final ChatMessageService chatMessageService;
    private final AgentRemoteVolvanoService agentRemoteVolvanoService;
    private final AgentProxyService agentProxyService;
    private final CacheProperties cacheProperties;
    private final CompressTaskConfig compressTaskConfig;

    // 配置参数 - 现在从CompressTaskConfig获取
    private int pageSize;
    private int maxPages;
    private int batchSize;
    private int intervalMinutes;
    private boolean taskEnabled;
    private int threadPoolSize;
    private long taskTimeoutMs;
    private int maxRetryAttempts;
    private long retryDelayMs;
    private double memoryThresholdPercent;
    private double forceGcThresholdPercent;
    private int memoryCheckInterval;

    // 线程安全控制和性能监控
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final AtomicLong totalProcessed = new AtomicLong(0);
    private final AtomicLong totalSkipped = new AtomicLong(0);
    private final AtomicLong totalErrors = new AtomicLong(0);
    private final AtomicLong totalMemoryErrors = new AtomicLong(0);
    private final AtomicInteger activeThreads = new AtomicInteger(0);

    // 线程池管理
    private ScheduledExecutorService scheduledExecutor;
    private ExecutorService compressionExecutor;
    private CompletionService<Boolean> completionService;
    private final MembershipProxyService membershipProxyService;
    @PostConstruct
    public void init() {
        // 从配置类加载配置参数
        loadConfigFromConfigClass();

        if (!taskEnabled) {
            log.info("压缩任务已禁用，跳过初始化");
            return;
        }

        log.info("初始化压缩任务 - 间隔: {} 分钟, 线程池大小: {}, 超时时间: {}ms, 内存阈值: {}%, 强制GC阈值: {}%",
                intervalMinutes, threadPoolSize, taskTimeoutMs, memoryThresholdPercent, forceGcThresholdPercent);

        log.info("配置摘要: {}", compressTaskConfig.getConfigSummary());

        // 创建调度线程池（单线程，用于定时任务）
        scheduledExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "compress-scheduler-thread");
            thread.setDaemon(true);
            thread.setUncaughtExceptionHandler((t, e) ->
                log.error("压缩调度线程发生未捕获异常", e));
            return thread;
        });

        // 创建压缩处理线程池（多线程，用于并行处理）
        compressionExecutor = Executors.newFixedThreadPool(threadPoolSize, r -> {
            Thread thread = new Thread(r, "compress-worker-" + activeThreads.incrementAndGet());
            thread.setDaemon(true);
            thread.setUncaughtExceptionHandler((t, e) -> {
                log.error("压缩工作线程 {} 发生未捕获异常", t.getName(), e);
                activeThreads.decrementAndGet();
            });
            return thread;
        });

        // 创建完成服务，用于管理并发任务
        completionService = new ExecutorCompletionService<>(compressionExecutor);

        // 延迟启动，避免影响应用启动性能
        scheduledExecutor.schedule(this::startPeriodicCompression, 2, TimeUnit.MINUTES);

        log.info("压缩任务初始化完成");
    }

    /**
     * 从配置类加载配置参数
     */
    private void loadConfigFromConfigClass() {
        this.pageSize = compressTaskConfig.getPageSize();
        this.maxPages = compressTaskConfig.getMaxPages();
        this.batchSize = compressTaskConfig.getBatchSize();
        this.intervalMinutes = compressTaskConfig.getIntervalMinutes();
        this.taskEnabled = compressTaskConfig.isEnabled();
        this.threadPoolSize = compressTaskConfig.getThreadPoolSize();
        this.taskTimeoutMs = compressTaskConfig.getTimeout();
        this.maxRetryAttempts = compressTaskConfig.getRetryMaxAttempts();
        this.retryDelayMs = compressTaskConfig.getRetryDelayMs();
        this.memoryThresholdPercent = compressTaskConfig.getMemoryThresholdPercent();
        this.forceGcThresholdPercent = compressTaskConfig.getForceGcThresholdPercent();
        this.memoryCheckInterval = compressTaskConfig.getMemoryCheckInterval();

        log.debug("配置参数加载完成: pageSize={}, batchSize={}, threadPoolSize={}, memoryThreshold={}%",
                pageSize, batchSize, threadPoolSize, memoryThresholdPercent);
    }

    /**
     * 优雅关闭线程池
     */
    @PreDestroy
    public void shutdown() {
        log.info("开始关闭压缩任务线程池");

        if (scheduledExecutor != null) {
            scheduledExecutor.shutdown();
            try {
                if (!scheduledExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                    log.warn("调度线程池未能在30秒内关闭，强制关闭");
                    scheduledExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                scheduledExecutor.shutdownNow();
            }
        }

        if (compressionExecutor != null) {
            compressionExecutor.shutdown();
            try {
                if (!compressionExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                    log.warn("压缩线程池未能在60秒内关闭，强制关闭");
                    compressionExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                compressionExecutor.shutdownNow();
            }
        }

        log.info("压缩任务线程池关闭完成");
    }

    /**
     * 启动定期压缩任务
     */
    private void startPeriodicCompression() {
        log.info("启动定期压缩任务，间隔: {} 分钟", intervalMinutes);
        scheduledExecutor.scheduleWithFixedDelay(
            this::executeCompressTaskSafely,
            0,
            intervalMinutes,
            TimeUnit.MINUTES
        );
    }

    /**
     * 安全执行压缩任务，包含异常处理和性能监控
     */
    private void executeCompressTaskSafely() {
        if (!isRunning.compareAndSet(false, true)) {
            log.warn("压缩任务正在运行中，跳过本次执行");
            return;
        }

        long startTime = System.currentTimeMillis();
        try {
            log.info("开始执行压缩任务 - 当前统计: 已处理={}, 已跳过={}, 错误={}",
                    totalProcessed.get(), totalSkipped.get(), totalErrors.get());

            compressTask();

            long duration = System.currentTimeMillis() - startTime;
            log.info("压缩任务执行完成，耗时: {}ms", duration);

        } catch (Exception e) {
            totalErrors.incrementAndGet();
            log.error("压缩任务执行失败", e);
        } finally {
            isRunning.set(false);
        }
    }
    /**
     * 执行压缩任务 - 获取所有需要压缩的会话并并行处理
     */
    public void compressTask() {
        if (!taskEnabled) {
            log.debug("压缩任务已禁用，跳过执行");
            return;
        }

        log.info("开始执行压缩任务，查找所有需要压缩的会话...");
        log.info("配置参数 - 页大小: {}, 最大页数: {}, 批处理大小: {}, 线程池大小: {}",
                pageSize, maxPages, batchSize, threadPoolSize);

        int pageNum = 0;
        int currentBatchProcessed = 0;
        int currentBatchSkipped = 0;
        int currentBatchErrors = 0;
        long startTime = System.currentTimeMillis();

        try {
            Page<ConversationSessionCompressDto> currentPage;
            List<ConversationSessionCompressDto> batchSessions = new ArrayList<>();

            // 分页循环获取所有需要压缩的会话
            do {
                log.debug("正在查询第 {} 页，页大小: {}", pageNum + 1, pageSize);

                // 查询当前页的会话
                currentPage = chatService.findUncompressedOrMatchingSeqSessions(pageNum, pageSize);

                if (currentPage.hasContent()) {
                    log.info("第 {} 页查询到 {} 个需要压缩的会话，总数: {}",
                            pageNum + 1,
                            currentPage.getNumberOfElements(),
                            currentPage.getTotalElements());

                    // 收集会话到批处理列表
                    batchSessions.addAll(currentPage.getContent());

                    // 当批处理列表达到指定大小或者是最后一页时，执行批处理
                    if (batchSessions.size() >= batchSize || !currentPage.hasNext()) {
                        var batchResult = processBatchSessions(batchSessions);
                        currentBatchProcessed += batchResult.processed;
                        currentBatchSkipped += batchResult.skipped;
                        currentBatchErrors += batchResult.errors;

                        // 更新全局统计
                        totalProcessed.addAndGet(batchResult.processed);
                        totalSkipped.addAndGet(batchResult.skipped);
                        totalErrors.addAndGet(batchResult.errors);

                        log.info("批处理完成 - 处理: {}, 跳过: {}, 错误: {}",
                                batchResult.processed, batchResult.skipped, batchResult.errors);

                        batchSessions.clear();
                    }
                } else {
                    log.debug("第 {} 页没有查询到数据", pageNum + 1);
                }

                pageNum++;

                // 防止无限循环的安全检查
                if (pageNum > maxPages) {
                    log.warn("分页查询超过{}页，可能存在问题，停止查询", maxPages);
                    break;
                }

                // 添加短暂休眠，避免过度占用资源
                if (pageNum % 10 == 0) {
                    try {
                        Thread.sleep(50); // 减少休眠时间，提高效率
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("压缩任务被中断");
                        break;
                    }
                }

            } while (currentPage.hasNext());

            long endTime = System.currentTimeMillis();
            log.info("压缩任务执行完成！本次处理: {} 个会话，跳过: {} 个，错误: {} 个，耗时: {} ms",
                    currentBatchProcessed, currentBatchSkipped, currentBatchErrors, (endTime - startTime));
            log.info("累计统计 - 总处理: {}, 总跳过: {}, 总错误: {}",
                    totalProcessed.get(), totalSkipped.get(), totalErrors.get());

        } catch (Exception e) {
            log.error("执行压缩任务时发生异常", e);
            totalErrors.incrementAndGet();
        }
    }

    /**
         * 批处理结果类
         */
        private record BatchResult(int processed, int skipped, int errors) {
    }

    /**
     * 并行处理批量会话（内存优化版本）
     */
    private BatchResult processBatchSessions(List<ConversationSessionCompressDto> sessions) {
        if (sessions.isEmpty()) {
            return new BatchResult(0, 0, 0);
        }

        log.info("开始并行处理 {} 个会话", sessions.size());
        long batchStartTime = System.currentTimeMillis();

        // 检查内存使用情况
        checkMemoryBeforeProcessing(sessions.size());

        // 提交所有任务到线程池
        List<Future<Boolean>> futures = new ArrayList<>(sessions.size());

        try {
            for (int i = 0; i < sessions.size(); i++) {
                ConversationSessionCompressDto session = sessions.get(i);
                if(session.getLastMessageTime() == null){
//                    log.warn("会话{}没有最后消息时间，跳过处理", session.getId());
                    chatService.updateSessionById(session.getId(),null,System.currentTimeMillis(),0L);
                    continue;
                }
//                log.info("开始处理压缩会话 {}", session);
                String cacheName = cacheProperties.getCacheName(MemberCacheConstant.MEMBERSHIP_BASE_CACHE_KEY + ":" + session.getUsername());

                UserMembershipVO member  = membershipProxyService.getMembershipByUsername(cacheName, session.getUsername());
                if(member == null || member.getVipLevel() < 1){
//                    log.info("用户{}没有会员，跳过压缩", session.getUsername());
                    continue;
                }
                Future<Boolean> future = completionService.submit(() -> {
                    try {
                        return processCompressionSessionWithRetry(session);
                    } catch (OutOfMemoryError e) {
                        log.error("处理会话 {} 时发生内存溢出", session.getId(), e);
                        System.gc();
                        return null; // 表示处理失败
                    } catch (Exception e) {
                        log.error("处理会话 {} 时发生错误", session.getId(), e);
                        return null; // 表示处理失败
                    }
                });
                futures.add(future);

                // 每提交5个任务检查一次内存
                if (i % 5 == 0 && i > 0) {
                    checkMemoryDuringProcessing();
                }
            }

            // 收集结果
            int processed = 0;
            int skipped = 0;
            int errors = 0;

            for (int i = 0; i < futures.size(); i++) {
                Future<Boolean> future = futures.get(i);
                try {
                    Boolean result = future.get(taskTimeoutMs, TimeUnit.MILLISECONDS);
                    if (result == null) {
                        errors++;
                    } else if (result) {
                        processed++;
                    } else {
                        skipped++;
                    }
                } catch (TimeoutException e) {
                    log.error("会话处理超时", e);
                    future.cancel(true);
                    errors++;
                } catch (Exception e) {
                    log.error("获取会话处理结果时发生错误", e);
                    errors++;
                }

                // 清理已完成的Future引用
                futures.set(i, null);

                // 每处理10个结果检查一次内存
                if (i % 10 == 0 && i > 0) {
                    checkMemoryDuringProcessing();
                }
            }

            long batchDuration = System.currentTimeMillis() - batchStartTime;
            log.info("批处理完成，耗时: {}ms，处理: {}, 跳过: {}, 错误: {}",
                    batchDuration, processed, skipped, errors);

            return new BatchResult(processed, skipped, errors);

        } catch (OutOfMemoryError e) {
            log.error("批处理会话时发生内存溢出", e);
            // 取消所有未完成的任务
            for (Future<Boolean> future : futures) {
                if (future != null && !future.isDone()) {
                    future.cancel(true);
                }
            }
            System.gc();
            return new BatchResult(0, 0, sessions.size());
        } finally {
            // 清理futures引用
            futures.clear();
        }
    }

    /**
     * 带重试机制的会话处理
     */
    private Boolean processCompressionSessionWithRetry(ConversationSessionCompressDto session) {
        int attempts = 0;
        Exception lastException = null;

        while (attempts < maxRetryAttempts) {
            try {
                return processCompressionSession(session);
            } catch (Exception e) {
                lastException = e;
                attempts++;

                if (attempts < maxRetryAttempts) {
                    long delay = retryDelayMs * (1L << (attempts - 1)); // 指数退避
                    log.warn("处理会话 {} 失败，第 {} 次重试，{}ms 后重试",
                            session.getId(), attempts, delay);

                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("重试被中断", ie);
                    }
                } else {
                    log.error("处理会话 {} 失败，已达到最大重试次数 {}",
                            session.getId(), maxRetryAttempts, lastException);
                }
            }
        }

        throw new RuntimeException("处理会话失败，已达到最大重试次数", lastException);
    }

    /**
     * 获取优化的压缩提示词
     * 要求详细提炼对话中的关键信息，包括地点、事件、时间、称呼等，
     * 分析人物性格和关系进展，作为后续对话的提示词
     */
    public String getCompressionPrompt(String agentName) {

        // 基础信息（限制1000 token）
        // 故事情节分析（限制3000 token）
        // 角色分析（限制4500 token）
        // 重要内容（限制3000 token）
        // 输出控制要求

        return "# 双人对话故事情节分析与压缩\n\n" +
                "**重要：输出总长度必须控制在12000 token以内，优先保留核心故事信息**\n\n" +
                "请分析以下两人对话的核心故事情节，按结构化格式输出：\n\n" +

                // 基础信息（限制1000 token）
                "## 【基础信息】\n" +
                "### " + agentName + "\n" +
                "- 身份背景：职业、社会身份、基本情况\n" +
                "- 性格特点：主要性格特征（3-5个关键词）\n" +
                "- 角色定位：在关系中的角色和态度\n\n" +
                "### 用户\n" +
                "- 性格特点：表现出的性格特征（3-5个关键词）\n" +
                "- 角色定位：在关系中的角色和态度\n\n" +
                "### 关系状态\n" +
                "- 关系类型：当前关系性质和发展阶段\n" +
                "- 互动模式：主要的交流方式和相处模式\n\n" +

                // 故事情节分析（限制3000 token）
                "## 【故事情节分析】\n" +
                "### 情节发展\n" +
                "- 起始：对话开始状态（2-3句）\n" +
                "- 发展：关键转折点（3-5个要点）\n" +
                "- 结果：当前结束状态（2-3句）\n\n" +
                "### 冲突与和解\n" +
                "- 主要冲突：核心分歧（简要描述）\n" +
                "- 解决过程：关键处理方式（要点式）\n" +
                "- 和谐时刻：重要正面互动（选择性记录）\n\n" +

                // 角色分析（限制4500 token）
                "## 【角色表现与喜好分析】\n" +
                "### " + agentName + "\n" +
                "- 情感状态：主要情绪变化（关键词+简述）\n" +
                "- 行为特点：核心特点（2-3个要点）\n" +
                "- 需求期待：主要期待/担忧（重点记录）\n" +
                "- 个人喜好：兴趣爱好、偏好选择、价值观倾向\n" +
                "- 互动喜好：喜欢的交流方式、互动类型、关系模式\n\n" +
                "### 用户\n" +
                "- 情感状态：主要情绪变化（关键词+简述）\n" +
                "- 行为特点：核心特点（2-3个要点）\n" +
                "- 需求期待：主要期待/担忧（重点记录）\n" +
                "- 个人喜好：表现出的兴趣、偏好、生活方式\n" +
                "- 对话喜好：喜欢的话题、交流风格、期待的互动\n\n" +
                "### 互动动态\n" +
                "- 关系变化：具体变化点（简要）\n" +
                "- 关键因素：影响关系的重要因素（3-5个）\n" +
                "- 喜好匹配：双方喜好的契合点和差异点\n" +
                "- 互动效果：哪些互动方式效果好/差\n\n" +

                // 重要内容（限制3000 token）
                "## 【重要内容】\n" +
                "### 承诺约定\n" +
                "- 明确承诺：具体约定（要点式，保留原话关键词）\n" +
                "- 暗示期待：重要暗示（筛选记录）\n" +
                "- 时间安排：具体的时间约定和计划\n\n" +
                "### 情感表达\n" +
                "- 重要表白：关键情感表达（保留核心原话）\n" +
                "- 亲密行为：重要互动（简要描述）\n" +
                "- 情感进展：关系情感层面的变化\n\n" +
                "### 喜好记录\n" +
                "- 共同兴趣：双方都感兴趣的话题和活动\n" +
                "- 个性化偏好：各自独特的喜好和特点\n" +
                "- 避雷点：不喜欢或需要避免的内容\n\n" +
                "### 最新状态总结\n" +
                "- 当前位置：记录角色和用户最新活动的具体地点和环境状态\n" +
                "- 核心对话要点：本次对话的关键话题、重要结论和待续内容\n" +

                // 输出控制要求
                "## 【输出控制要求】\n" +
                "**token分配优先级（总计12000 token）：**\n" +
                "1. **基础信息** - 900 token：角色背景和关系\n" +
                "2. **故事情节** - 2800 token：核心重点\n" +
                "3. **角色表现** - 4800 token：详细分析（含喜好）\n" +
                "4. **重要内容** - 3500 token：精确记录（含喜好记录）\n\n" +
                "**压缩策略：**\n" +
                "- 使用关键词和要点式表达\n" +
                "- 保留具体姓名、地点、时间、承诺的原文关键词\n" +
                "- 删除分析性描述，保留事实性信息\n" +
                "- 喜好信息要具体明确，避免泛泛而谈\n" +
                "- 如超出限制，优先保留故事核心情节和角色关系变化\n" +
                "- 次要细节可简化或合并表述\n\n" +
                "**质量要求：**\n" +
                "- 准确性：关键信息不得遗漏或错误\n" +
                "- 完整性：保留影响后续剧情的所有要素\n" +
                "- 可用性：确保信息足够支撑后续对话\n" +
                "- 简洁性：在token限制内达到最大信息密度\n" +
                "- 个性化：充分提炼出双方的个性喜好特征\n\n" +
                "请严格按照token分配和优先级要求输出分析结果，确保最后的状态总结简洁而完整。";
    }

    /**
     * 处理单个需要压缩的会话（优化版本）
     */
    private boolean processCompressionSession(ConversationSessionCompressDto session) {
        log.debug("开始处理会话: {}", session);

        try {
            // 检查是否真的需要压缩
            if (!session.needsCompression()) {
//                log.debug("会话 {} 不需要压缩，跳过处理", session.getId());
                return false;
            }

            // 获取智能体信息（缓存优化）
            AgentBaseVO agent = getAgentInfoCached(session.getAgentId());
            if (agent == null) {
                log.error("无法获取智能体 {} 信息，跳过会话 {}", session.getAgentId(), session.getId());
                return false;
            }

            // 获取上次压缩内容
            String lastCompressContent = getLastCompressContent(session, agent);

            // 流式处理历史消息，避免内存溢出
            String finalCompressContent = processHistoryMessagesStreaming(session, lastCompressContent, agent);

            // 获取最后处理的序列号
            Long lastCompressSeq = getLastProcessedSeqNum(session);

            log.info("处理需要压缩的会话 - ID: {}, 用户: {}, 智能体: {}, 状态: {}",
                    session.getId(),
                    session.getUsername(),
                    session.getAgentId(),
                    session.getCompressionStatus());

            // 保存压缩结果
            saveCompressedResult(session, finalCompressContent, lastCompressSeq);

            log.debug("会话 {} 压缩处理完成", session.getId());
            return true;
        } catch (Exception e) {
            log.error("处理会话 {} 压缩时发生错误", session.getId(), e);
            throw e; // 重新抛出异常，让上层处理
        }
    }

    /**
     * 缓存获取智能体信息
     */
    private AgentBaseVO getAgentInfoCached(Long agentId) {
        try {
            String cacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_MAP_CACHE_KEY);
            return agentProxyService.getAgentInfo(cacheKey, agentId);
        } catch (Exception e) {
            log.error("获取智能体 {} 信息失败", agentId, e);
            return null;
        }
    }

    /**
     * 获取上次压缩内容
     */
    private String getLastCompressContent(ConversationSessionCompressDto session, AgentBaseVO agent) {
        if (session.getCompressContextId() != null) {
            try {
                var compressMessage = chatCompressMessageService.findById(session.getCompressContextId());
                if (compressMessage.isPresent()) {
                    return compressMessage.get().getContent();
                }
            } catch (Exception e) {
                log.warn("获取会话 {} 上次压缩内容失败，使用默认内容", session.getId(), e);
            }
        }

        // 构建初始压缩内容
        StringBuilder prompt = new StringBuilder();
        if (agent.getProfile() != null && agent.getProfile().getBackground() != null &&
            !agent.getProfile().getBackground().isEmpty()) {
            prompt.append("\n### 你的出身背景是:\n").append(agent.getProfile().getBackground()).append("。");
        }
        if (agent.getIntroduction() != null && !agent.getIntroduction().isEmpty()) {
            String introduction = agent.getIntroduction().replaceAll("[你您]", "用户");
            introduction = introduction.replaceAll("[她他]", "你");
            prompt.append("\n ### 你跟用户的聊天场景简介:").append(introduction).append("。");
        }
        return prompt.toString().replace("你", agent.getName());
    }

    /**
     * 获取最后处理的序列号
     */
    private Long getLastProcessedSeqNum(ConversationSessionCompressDto session) {
        // 这里可以优化为直接查询最大序列号，而不是遍历所有消息
        return session.getLstSeqNum();
    }

    /**
     * 流式处理历史消息，避免内存溢出（内存优化版本）
     */
    private String processHistoryMessagesStreaming(ConversationSessionCompressDto session,
                                                  String lastCompressContent,
                                                  AgentBaseVO agent) {
        log.info("开始流式处理会话 {} 的历史消息", session.getId());

        try {
            int pageNum = 0;
            int streamPageSize = 200; // 减小页面大小，降低内存使用
            String currentCompressContent = lastCompressContent;
            int totalProcessedMessages = 0;

            Page<ChatMessage> currentPage;
            do {
                // 在每次循环开始时检查内存使用情况
                checkMemoryUsageAndGC(pageNum);

                Pageable pageable = PageRequest.of(pageNum, streamPageSize);
                currentPage = chatMessageService.pageBySessionIdOrderByCreatedAtAsc(
                    session.getId(), session.getAgentId(), pageable);

                if (currentPage.hasContent()) {
                    log.debug("流式处理会话 {} 第 {} 页历史消息，包含 {} 条记录",
                            session.getId(), pageNum + 1, currentPage.getNumberOfElements());

                    // 直接处理，不创建副本，减少内存占用
                    currentCompressContent = compressMessagesOptimized(
                            currentCompressContent, currentPage.getContent(), agent);

                    totalProcessedMessages += currentPage.getNumberOfElements();

                    // 显式清理页面引用
                    currentPage = null;
                } else {
                    log.debug("会话 {} 第 {} 页没有历史消息", session.getId(), pageNum + 1);
                }

                pageNum++;

                // 安全检查，防止无限循环
                if (pageNum > 1000) {
                    log.warn("会话 {} 历史消息分页查询超过1000页，可能存在问题，停止查询", session.getId());
                    break;
                }

                // 每处理5页休眠一下，避免过度占用资源，并强制GC
                if (pageNum % 5 == 0) {
                    try {
                        Thread.sleep(50); // 增加休眠时间，给GC更多时间
                        System.gc(); // 建议JVM进行垃圾回收
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("流式处理被中断");
                        break;
                    }
                }

            } while (currentPage != null && currentPage.hasNext());

            log.info("会话 {} 流式处理完成，总共处理 {} 条消息", session.getId(), totalProcessedMessages);
            return currentCompressContent;

        } catch (OutOfMemoryError e) {
            log.error("会话 {} 流式处理时发生内存溢出", session.getId(), e);
            // 尝试释放内存
            System.gc();
            throw new RuntimeException("内存不足，无法继续处理", e);
        } catch (Exception e) {
            log.error("流式处理会话 {} 历史消息时发生错误", session.getId(), e);
            throw e;
        } finally {
            // 确保在方法结束时进行内存清理
            System.gc();
        }
    }

    /**
     * 检查内存使用情况并在必要时进行垃圾回收（使用MemoryMonitor优化）
     */
    private void checkMemoryUsageAndGC(int pageNum) {
        MemoryMonitor.MemoryInfo memoryInfo = MemoryMonitor.getCurrentMemoryInfo();

        // 使用配置的阈值进行判断
        if (memoryInfo.getUsagePercent() > memoryThresholdPercent) {
            log.warn("内存使用率过高: {:.2f}%，强制进行垃圾回收", memoryInfo.getUsagePercent());

            MemoryMonitor.MemoryInfo afterGC = MemoryMonitor.forceGarbageCollection();

            // 如果垃圾回收后内存使用率仍然很高，记录内存错误并抛出警告
            if (afterGC.getUsagePercent() > 90.0) {
                totalMemoryErrors.incrementAndGet();
                log.error("警告：垃圾回收后内存使用率仍然很高: {:.2f}%，可能存在内存泄漏", afterGC.getUsagePercent());

                // 如果内存使用率超过95%，抛出异常停止处理
                if (afterGC.getUsagePercent() > 95.0) {
                    throw new RuntimeException("内存使用率过高，停止处理以防止OOM");
                }
            }
        }

        // 使用配置的间隔记录内存使用情况
        if (pageNum % memoryCheckInterval == 0 && pageNum > 0) {
            log.debug("第{}页处理完成，{}", pageNum, memoryInfo);
        }
    }

    /**
     * 优化的消息压缩方法（内存优化版本）
     */
    private String compressMessagesOptimized(String lastCompressContent,
                                           List<ChatMessage> messages,
                                           AgentBaseVO agent) {
        if (messages.isEmpty()) {
            return lastCompressContent;
        }

        log.debug("开始AI智能压缩 {} 条消息", messages.size());

        StringBuilder historyContent = null;
        try {
            // 预先检查内存使用情况
            checkMemoryBeforeProcessing(messages.size());

            // 构建历史对话内容（优化内存使用）
            int estimatedSize = Math.min(messages.size() * 80, 10000); // 限制预估容量，防止过大
            historyContent = new StringBuilder(estimatedSize);

            // 流式处理消息，避免一次性加载所有内容到内存
            for (int i = 0; i < messages.size(); i++) {
                ChatMessage message = messages.get(i);

                if (ChatConstant.CHAT_USER_ROLE.equals(message.getRole())) {
                    historyContent.append("[用户] : ").append(message.getContent()).append("\n");
                } else if (ChatConstant.CHAT_ASSISTANT_ROLE.equals(message.getRole())) {
                    historyContent.append("[").append(agent.getName()).append("] : ")
                                 .append(message.getContent()).append("\n");
                }

                // 每处理10条消息检查一次内存
                if (i % 10 == 0 && i > 0) {
                    checkMemoryDuringProcessing();
                }

                // 如果内容过长，提前截断避免内存溢出
                if (historyContent.length() > 30000) {
                    log.warn("历史内容长度超过30000字符，提前截断以避免内存问题");
                    break;
                }
            }

            // 检查内容长度，避免过长的内容导致AI服务超时
            String historyStr = historyContent.toString();

            // 释放StringBuilder引用
            historyContent = null;

            if (historyStr.length() > 25000) { // 降低阈值，更早进行分段处理
                log.warn("历史内容过长 ({} 字符)，进行分段处理", historyStr.length());
                return compressLongContentOptimized(lastCompressContent, historyStr, agent);
            }

            // 构建压缩上下文
            List<ChatContextVO> contexts = buildCompressContextsOptimized(lastCompressContent, historyStr, agent);

            // 调用AI服务进行智能压缩（带超时控制）
            String compressedResult = callAIServiceWithTimeout(contexts);

            if (compressedResult != null && !compressedResult.trim().isEmpty()) {
                log.debug("AI压缩完成，原始长度: {}, 压缩后长度: {}",
                        historyStr.length(), compressedResult.length());
                return compressedResult;
            } else {
                log.warn("AI压缩返回空结果，使用简单摘要");
                return createSimpleSummary(lastCompressContent, messages.size());
            }

        } catch (OutOfMemoryError e) {
            log.error("消息压缩时发生内存溢出，消息数量: {}", messages.size(), e);
            // 尝试释放内存
            historyContent = null;
            System.gc();
            return createSimpleSummary(lastCompressContent, messages.size());
        } catch (Exception e) {
            log.error("AI压缩失败，使用简单摘要: {}", e.getMessage());
            return createSimpleSummary(lastCompressContent, messages.size());
        } finally {
            // 确保释放StringBuilder引用
            historyContent = null;
        }
    }

    /**
     * 处理前检查内存使用情况（使用MemoryMonitor优化）
     */
    private void checkMemoryBeforeProcessing(int messageCount) {
        MemoryMonitor.MemoryInfo memoryInfo = MemoryMonitor.getCurrentMemoryInfo();

        if (memoryInfo.getUsagePercent() > 75.0) {
            log.warn("处理{}条消息前内存使用率已达{:.2f}%，建议进行垃圾回收", messageCount, memoryInfo.getUsagePercent());
            MemoryMonitor.forceGarbageCollection();
        }

        // 使用MemoryMonitor估算处理这些消息需要的内存
        long estimatedMemoryNeeded = MemoryMonitor.estimateMemoryForMessages(messageCount);
        if (!MemoryMonitor.hasEnoughMemory(estimatedMemoryNeeded)) {
            log.warn("可用内存可能不足以处理{}条消息，估算需要{}MB",
                    messageCount, estimatedMemoryNeeded / 1024 / 1024);
        }
    }

    /**
     * 处理过程中检查内存使用情况（使用MemoryMonitor优化）
     */
    private void checkMemoryDuringProcessing() {
        MemoryMonitor.MemoryInfo memoryInfo = MemoryMonitor.getCurrentMemoryInfo();

        if (memoryInfo.getUsagePercent() > forceGcThresholdPercent) {
            log.warn("处理过程中内存使用率达到{:.2f}%，强制进行垃圾回收", memoryInfo.getUsagePercent());

            MemoryMonitor.MemoryInfo afterGC = MemoryMonitor.forceGarbageCollection();

            if (afterGC.getUsagePercent() > 92.0) {
                totalMemoryErrors.incrementAndGet();
                log.error("垃圾回收后内存使用率仍然很高: {:.2f}%", afterGC.getUsagePercent());
                throw new RuntimeException("内存使用率过高，停止当前处理");
            }
        }
    }

    /**
     * 处理过长内容的分段压缩（内存优化版本）
     */
    private String compressLongContentOptimized(String lastCompressContent, String historyContent, AgentBaseVO agent) {
        // 将长内容分段处理，减小分段大小以降低内存使用
        int maxChunkSize = 15000; // 减小分段大小
        List<String> chunks = null;

        try {
            chunks = splitContentOptimized(historyContent, maxChunkSize);

            String currentContent = lastCompressContent;
            for (int i = 0; i < chunks.size(); i++) {
                log.debug("处理第 {} 段内容，共 {} 段", i + 1, chunks.size());

                // 在处理每段前检查内存
                checkMemoryDuringProcessing();

                try {
                    String chunk = chunks.get(i);
                    List<ChatContextVO> contexts = buildCompressContextsOptimized(currentContent, chunk, agent);
                    String chunkResult = callAIServiceWithTimeout(contexts);

                    if (chunkResult != null && !chunkResult.trim().isEmpty()) {
                        currentContent = chunkResult;
                    } else {
                        log.warn("第 {} 段压缩失败，保持原内容", i + 1);
                    }

                    // 清理当前段的引用
                    chunks.set(i, null);

                } catch (OutOfMemoryError e) {
                    log.error("第 {} 段压缩时发生内存溢出", i + 1, e);
                    System.gc();
                    break; // 停止处理剩余段落
                } catch (Exception e) {
                    log.error("第 {} 段压缩失败: {}", i + 1, e.getMessage());
                }

                // 每处理3段休眠一下，给GC时间
                if (i % 3 == 0 && i > 0) {
                    try {
                        Thread.sleep(100);
                        System.gc();
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }

            return currentContent;

        } catch (OutOfMemoryError e) {
            log.error("分段压缩时发生内存溢出", e);
            System.gc();
            return createSimpleSummary(lastCompressContent, 0);
        } finally {
            // 清理chunks引用
            if (chunks != null) {
                chunks.clear();
                chunks = null;
            }
        }
    }

    /**
     * 分割长内容（内存优化版本）
     */
    private List<String> splitContentOptimized(String content, int maxChunkSize) {
        if (content == null || content.isEmpty()) {
            return new ArrayList<>();
        }

        // 预估分段数量，避免List频繁扩容
        int estimatedChunks = (content.length() / maxChunkSize) + 1;
        List<String> chunks = new ArrayList<>(estimatedChunks);

        int start = 0;
        int contentLength = content.length();

        try {
            while (start < contentLength) {
                int end = Math.min(start + maxChunkSize, contentLength);

                // 尝试在句号或换行符处分割，避免截断句子
                if (end < contentLength) {
                    int lastPeriod = content.lastIndexOf('。', end);
                    int lastNewline = content.lastIndexOf('\n', end);
                    int splitPoint = Math.max(lastPeriod, lastNewline);

                    if (splitPoint > start) {
                        end = splitPoint + 1;
                    }
                }

                // 创建子字符串时检查内存
                try {
                    String chunk = content.substring(start, end);
                    chunks.add(chunk);
                } catch (OutOfMemoryError e) {
                    log.error("创建内容分段时发生内存溢出，当前分段: {}", chunks.size(), e);
                    System.gc();
                    break;
                }

                start = end;

                // 每创建5个分段检查一次内存
                if (chunks.size() % 5 == 0) {
                    checkMemoryDuringProcessing();
                }
            }

        } catch (OutOfMemoryError e) {
            log.error("分割内容时发生内存溢出", e);
            System.gc();
        }

        log.debug("内容分割完成，共生成 {} 个分段", chunks.size());
        return chunks;
    }

    /**
     * 带超时的AI服务调用
     */
    private String callAIServiceWithTimeout(List<ChatContextVO> contexts) throws Exception {
        try {
            // 这里可以添加超时控制
            return agentRemoteVolvanoService.sendCompressChatMessage(contexts);
        } catch (Exception e) {
            log.error("AI服务调用失败", e);
            throw e;
        }
    }

    /**
     * 创建简单摘要
     */
    private String createSimpleSummary(String lastCompressContent, int messageCount) {
        return lastCompressContent + "\n[系统摘要] 包含" + messageCount + "条对话记录（AI压缩失败）";
    }

    /**
     * 优化的构建压缩上下文方法（内存优化版本）
     */
    private List<ChatContextVO> buildCompressContextsOptimized(String lastCompressContent,
                                                              String historyContent,
                                                              AgentBaseVO agent) {
        List<ChatContextVO> contexts = new ArrayList<>(2); // 预分配容量

        try {
            // 添加系统提示（使用优化的压缩提示词）
            contexts.add(ChatContextVO.builder()
                    .role(ChatConstant.CHAT_SYSTEM_ROLE)
                    .type("text")
                    .content(getCompressionPrompt(agent.getName()))
                    .contentType("text")
                    .build());

            // 构建用户消息（优化字符串拼接，使用StringBuilder减少内存分配）
            StringBuilder userMessageBuilder = new StringBuilder();
            userMessageBuilder.append("以下是需要压缩的故事内容：\n\n");

            if (lastCompressContent != null && !lastCompressContent.trim().isEmpty()) {
                userMessageBuilder.append(lastCompressContent);
            }

            userMessageBuilder.append("\n\n");

            if (historyContent != null && !historyContent.trim().isEmpty()) {
                userMessageBuilder.append(historyContent);
            }

            userMessageBuilder.append("\n\n请按照系统要求进行深度分析和压缩。");

            contexts.add(ChatContextVO.builder()
                    .role("user")
                    .type("text")
                    .content(userMessageBuilder.toString())
                    .contentType("text")
                    .build());

            // 清理StringBuilder引用
            userMessageBuilder = null;

        } catch (OutOfMemoryError e) {
            log.error("构建压缩上下文时发生内存溢出", e);
            System.gc();
            throw new RuntimeException("内存不足，无法构建压缩上下文", e);
        }

        return contexts;
    }

    /**
     * 保存压缩结果（示例实现）
     *
     * @param session 会话信息
     * @param compressedContent 压缩内容
     */
    private void saveCompressedResult(ConversationSessionCompressDto session, String compressedContent, Long lastCompressSeq) {
        log.debug("保存会话 {} 的压缩结果", session.getId());

        try {

            ChatCompressMessage compressMessage = ChatCompressMessage.builder()
                    .sessionId(session.getId())
                    .username(session.getUsername())
                    .agentId(session.getAgentId())
                    .content(compressedContent)
                    .endSeqNum(lastCompressSeq)
                    .compressedTime(System.currentTimeMillis())
                    .createdAt(LocalDateTime.now())
                    .build();

            chatCompressMessageService.save(compressMessage);
            chatService.updateSessionById(session.getId(),compressMessage.getId(),compressMessage.getCompressedTime(),lastCompressSeq);
            log.info("会话 {} 压缩结果保存成功", session.getId());

        } catch (Exception e) {
            log.error("保存会话 {} 压缩结果时发生错误", session.getId(), e);
            throw e;
        }
    }

    /**
     * 获取任务性能统计信息（包含内存统计）
     */
    public String getPerformanceStats() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        double memoryUsagePercent = (double) usedMemory / maxMemory * 100;

        return String.format(
            "压缩任务性能统计 - 总处理: %d, 总跳过: %d, 总错误: %d, 内存错误: %d, 活跃线程: %d, " +
            "运行状态: %s, 当前内存使用率: %.2f%%, 已用内存: %dMB, 最大内存: %dMB",
            totalProcessed.get(),
            totalSkipped.get(),
            totalErrors.get(),
            totalMemoryErrors.get(),
            activeThreads.get(),
            isRunning.get() ? "运行中" : "空闲",
            memoryUsagePercent,
            usedMemory / 1024 / 1024,
            maxMemory / 1024 / 1024
        );
    }

    /**
     * 重置性能统计
     */
    public void resetStats() {
        totalProcessed.set(0);
        totalSkipped.set(0);
        totalErrors.set(0);
        totalMemoryErrors.set(0);
        log.info("压缩任务性能统计已重置");
    }

    /**
     * 检查任务健康状态
     */
    public boolean isHealthy() {
        // 检查线程池状态
        if (scheduledExecutor != null && scheduledExecutor.isShutdown()) {
            return false;
        }
        if (compressionExecutor != null && compressionExecutor.isShutdown()) {
            return false;
        }

        // 检查错误率（如果错误率超过50%认为不健康）
        long total = totalProcessed.get() + totalSkipped.get() + totalErrors.get();
        return total <= 100 || totalErrors.get() * 2 <= total;
    }

    /**
     * 手动触发压缩任务（用于测试或紧急处理）
     */
    public void triggerManualCompression() {
        if (!taskEnabled) {
            log.warn("压缩任务已禁用，无法手动触发");
            return;
        }

        if (isRunning.get()) {
            log.warn("压缩任务正在运行中，无法重复触发");
            return;
        }

        log.info("手动触发压缩任务");
        scheduledExecutor.submit(this::executeCompressTaskSafely);
    }

}
