package com.gw.chat.service;

import com.gw.chat.dto.CommentCreateDTO;
import com.gw.chat.dto.CommentReplyDTO;
import com.gw.chat.vo.CommentVO;
import com.gw.chat.vo.CommentWithRepliesVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface ConversationCommentService {

    /**
     * 创建评论
     */
    CommentVO createComment(String commenter, CommentCreateDTO commentDTO, Long agentId, String agentUser);

    /**
     * 回复评论
     */
    CommentVO replyToComment(String username, CommentReplyDTO replyDTO);

    /**
     * 删除评论（逻辑删除）
     */
    void deleteComment(String commentId, String username);

    /**
     * 获取评论详情
     */
    CommentVO getComment(String commentId);

    /**
     * 获取评论及其回复
     */
    CommentWithRepliesVO getCommentWithReplies(String commentId);

    /**
     * 分页查询会话的评论列表（不包括回复）
     */
    Page<CommentWithRepliesVO> getCommentsBySessionId(String conversationId, Pageable pageable);

    /**
     * 分页查询用户的评论列表
     */
    Page<CommentVO> getCommentsByUsername(String username, Long agentId, Pageable pageable);
} 