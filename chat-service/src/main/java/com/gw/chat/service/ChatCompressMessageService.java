package com.gw.chat.service;

import com.gw.chat.entity.ChatCompressMessage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 聊天压缩消息服务接口
 */
public interface ChatCompressMessageService {

    /**
     * 保存压缩消息
     *
     * @param chatCompressMessage 压缩消息实体
     * @return 保存后的压缩消息
     */
    ChatCompressMessage save(ChatCompressMessage chatCompressMessage);

    /**
     * 批量保存压缩消息
     *
     * @param chatCompressMessages 压缩消息列表
     * @return 保存后的压缩消息列表
     */
    List<ChatCompressMessage> saveAll(List<ChatCompressMessage> chatCompressMessages);

    /**
     * 根据ID查询压缩消息
     *
     * @param id 压缩消息ID
     * @return 压缩消息
     */
    Optional<ChatCompressMessage> findById(String id);

    /**
     * 根据会话ID查询压缩消息
     *
     * @param sessionId 会话ID
     * @return 压缩消息列表
     */
    List<ChatCompressMessage> findBySessionId(String sessionId);

    /**
     * 根据会话ID分页查询压缩消息
     *
     * @param sessionId 会话ID
     * @param pageable  分页参数
     * @return 分页压缩消息
     */
    Page<ChatCompressMessage> findBySessionId(String sessionId, Pageable pageable);

    /**
     * 根据会话ID查询最新的压缩消息
     *
     * @param sessionId 会话ID
     * @param limit     限制数量
     * @return 压缩消息列表
     */
    List<ChatCompressMessage> findLatestBySessionId(String sessionId, int limit);

    /**
     * 根据用户名查询压缩消息
     *
     * @param username 用户名
     * @param pageable 分页参数
     * @return 分页压缩消息
     */
    Page<ChatCompressMessage> findByUsername(String username, Pageable pageable);

    /**
     * 根据智能体ID查询压缩消息
     *
     * @param agentId  智能体ID
     * @param pageable 分页参数
     * @return 分页压缩消息
     */
    Page<ChatCompressMessage> findByAgentId(Long agentId, Pageable pageable);

    /**
     * 根据用户名和智能体ID查询压缩消息
     *
     * @param username 用户名
     * @param agentId  智能体ID
     * @param pageable 分页参数
     * @return 分页压缩消息
     */
    Page<ChatCompressMessage> findByUsernameAndAgentId(String username, Long agentId, Pageable pageable);

    /**
     * 根据用户名和会话ID查询压缩消息
     *
     * @param username  用户名
     * @param sessionId 会话ID
     * @return 压缩消息列表
     */
    List<ChatCompressMessage> findByUsernameAndSessionId(String username, String sessionId);

    /**
     * 根据会话ID和结束序列号查询压缩消息
     *
     * @param sessionId  会话ID
     * @param endSeqNum  结束序列号
     * @return 压缩消息列表
     */
    List<ChatCompressMessage> findBySessionIdAndEndSeqNum(String sessionId, Long endSeqNum);

    /**
     * 根据会话ID和创建时间范围查询压缩消息
     *
     * @param sessionId 会话ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 压缩消息列表
     */
    List<ChatCompressMessage> findBySessionIdAndTimeRange(String sessionId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据压缩时间查询压缩消息
     *
     * @param compressedTime 压缩时间
     * @return 压缩消息列表
     */
    List<ChatCompressMessage> findByCompressedTimeAfter(Long compressedTime);

    /**
     * 统计会话的压缩消息数量
     *
     * @param sessionId 会话ID
     * @return 压缩消息数量
     */
    long countBySessionId(String sessionId);

    /**
     * 统计用户的压缩消息数量
     *
     * @param username 用户名
     * @return 压缩消息数量
     */
    long countByUsername(String username);

    /**
     * 统计智能体的压缩消息数量
     *
     * @param agentId 智能体ID
     * @return 压缩消息数量
     */
    long countByAgentId(Long agentId);

    /**
     * 更新压缩消息
     *
     * @param chatCompressMessage 压缩消息实体
     * @return 更新后的压缩消息
     */
    ChatCompressMessage update(ChatCompressMessage chatCompressMessage);

    /**
     * 根据ID删除压缩消息
     *
     * @param id 压缩消息ID
     */
    void deleteById(String id);

    /**
     * 删除指定会话的所有压缩消息
     *
     * @param sessionId 会话ID
     */
    void deleteBySessionId(String sessionId);

    /**
     * 删除指定用户的所有压缩消息
     *
     * @param username 用户名
     */
    void deleteByUsername(String username);

    /**
     * 检查压缩消息是否存在
     *
     * @param id 压缩消息ID
     * @return 是否存在
     */
    boolean existsById(String id);

}
