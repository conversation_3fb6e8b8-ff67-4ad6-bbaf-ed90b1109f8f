package com.gw.chat.service.impl;

import com.gw.chat.client.AgentUsageClient;
import com.gw.common.agent.service.AgentUsageProxyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * 智能体使用代理服务实现 (临时版本)
 */
@Service
@Log4j2
@RequiredArgsConstructor
public class AgentUsageProxyServiceImpl implements AgentUsageProxyService {
    private final AgentUsageClient agentUsageClient;

    @Override
    public boolean recordAgentUsage(Long agentId, String username) {
        try {
            CompletableFuture.runAsync(() -> {
                try {
                    agentUsageClient.recordAgentUsage(agentId, username);
                    log.info("记录用户[{}]使用智能体[{}]", username, agentId);
                } catch (Exception e) {
                    log.error("记录智能体使用失败", e);
                }
            });
            return true;
        } catch (Exception e) {
            log.error("异步任务执行失败", e);
            return false;
        }
    }

    @Override
    public int getAgentUserCount(Long agentId) {
        // 临时实现：返回默认值
        log.info("获取智能体[{}]的使用人数", agentId);
        return 0;
    }
}