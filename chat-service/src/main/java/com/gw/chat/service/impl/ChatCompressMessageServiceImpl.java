package com.gw.chat.service.impl;

import com.gw.chat.entity.ChatCompressMessage;
import com.gw.chat.repository.ChatCompressMessageRepository;
import com.gw.chat.service.ChatCompressMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@Log4j2
@RequiredArgsConstructor
public class ChatCompressMessageServiceImpl implements ChatCompressMessageService {

    private final ChatCompressMessageRepository chatCompressMessageRepository;
    @Override
    public ChatCompressMessage save(ChatCompressMessage chatCompressMessage) {
        log.debug("Saving chat compress message: {}", chatCompressMessage);
        if (chatCompressMessage.getCreatedAt() == null) {
            chatCompressMessage.setCreatedAt(LocalDateTime.now());
        }
        ChatCompressMessage saved = chatCompressMessageRepository.save(chatCompressMessage);
        log.debug("Successfully saved chat compress message with id: {}", saved.getId());
        return saved;
    }

    @Override
    public List<ChatCompressMessage> saveAll(List<ChatCompressMessage> chatCompressMessages) {
        log.debug("Saving {} chat compress messages", chatCompressMessages.size());
        // Set creation time for messages that don't have it
        LocalDateTime now = LocalDateTime.now();
        chatCompressMessages.forEach(message -> {
            if (message.getCreatedAt() == null) {
                message.setCreatedAt(now);
            }
        });
        List<ChatCompressMessage> saved = chatCompressMessageRepository.saveAll(chatCompressMessages);
        log.debug("Successfully saved {} chat compress messages", saved.size());
        return saved;
    }

    @Override
    public Optional<ChatCompressMessage> findById(String id) {
        log.debug("Finding chat compress message by id: {}", id);
        return chatCompressMessageRepository.findById(id);
    }

    @Override
    public List<ChatCompressMessage> findBySessionId(String sessionId) {
        log.debug("Finding chat compress messages by sessionId: {}", sessionId);
        return chatCompressMessageRepository.findBySessionIdOrderByCreatedAtAsc(sessionId);
    }

    @Override
    public Page<ChatCompressMessage> findBySessionId(String sessionId, Pageable pageable) {
        log.debug("Finding chat compress messages by sessionId: {} with pagination", sessionId);
        return chatCompressMessageRepository.findBySessionIdOrderByCreatedAtDesc(sessionId, pageable);
    }

    @Override
    public List<ChatCompressMessage> findLatestBySessionId(String sessionId, int limit) {
        log.debug("Finding latest {} chat compress messages by sessionId: {}", limit, sessionId);
        Pageable pageable = PageRequest.of(0, limit);
        return chatCompressMessageRepository.findBySessionIdOrderByCreatedAtDescLimit(sessionId, pageable);
    }

    @Override
    public Page<ChatCompressMessage> findByUsername(String username, Pageable pageable) {
        log.debug("Finding chat compress messages by username: {} with pagination", username);
        return chatCompressMessageRepository.findByUsernameOrderByCreatedAtDesc(username, pageable);
    }

    @Override
    public Page<ChatCompressMessage> findByAgentId(Long agentId, Pageable pageable) {
        log.debug("Finding chat compress messages by agentId: {} with pagination", agentId);
        return chatCompressMessageRepository.findByAgentIdOrderByCreatedAtDesc(agentId, pageable);
    }

    @Override
    public Page<ChatCompressMessage> findByUsernameAndAgentId(String username, Long agentId, Pageable pageable) {
        log.debug("Finding chat compress messages by username: {} and agentId: {} with pagination", username, agentId);
        return chatCompressMessageRepository.findByUsernameAndAgentIdOrderByCreatedAtDesc(username, agentId, pageable);
    }

    @Override
    public List<ChatCompressMessage> findByUsernameAndSessionId(String username, String sessionId) {
        log.debug("Finding chat compress messages by username: {} and sessionId: {}", username, sessionId);
        return chatCompressMessageRepository.findByUsernameAndSessionIdOrderByCreatedAtAsc(username, sessionId);
    }

    @Override
    public List<ChatCompressMessage> findBySessionIdAndEndSeqNum(String sessionId, Long endSeqNum) {
        log.debug("Finding chat compress messages by sessionId: {} and endSeqNum: {}", sessionId, endSeqNum);
        return chatCompressMessageRepository.findBySessionIdAndEndSeqNumOrderByCreatedAtAsc(sessionId, endSeqNum);
    }

    @Override
    public List<ChatCompressMessage> findBySessionIdAndTimeRange(String sessionId, LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("Finding chat compress messages by sessionId: {} between {} and {}", sessionId, startTime, endTime);
        return chatCompressMessageRepository.findBySessionIdAndCreatedAtBetweenOrderByCreatedAtAsc(sessionId, startTime, endTime);
    }

    @Override
    public List<ChatCompressMessage> findByCompressedTimeAfter(Long compressedTime) {
        log.debug("Finding chat compress messages with compressedTime after: {}", compressedTime);
        return chatCompressMessageRepository.findByCompressedTimeGreaterThanOrderByCreatedAtAsc(compressedTime);
    }

    @Override
    public long countBySessionId(String sessionId) {
        log.debug("Counting chat compress messages by sessionId: {}", sessionId);
        return chatCompressMessageRepository.countBySessionId(sessionId);
    }

    @Override
    public long countByUsername(String username) {
        log.debug("Counting chat compress messages by username: {}", username);
        return chatCompressMessageRepository.countByUsername(username);
    }

    @Override
    public long countByAgentId(Long agentId) {
        log.debug("Counting chat compress messages by agentId: {}", agentId);
        return chatCompressMessageRepository.countByAgentId(agentId);
    }

    @Override
    public ChatCompressMessage update(ChatCompressMessage chatCompressMessage) {
        log.debug("Updating chat compress message: {}", chatCompressMessage);
        if (chatCompressMessage.getId() == null) {
            throw new IllegalArgumentException("Cannot update chat compress message without ID");
        }
        ChatCompressMessage updated = chatCompressMessageRepository.save(chatCompressMessage);
        log.debug("Successfully updated chat compress message with id: {}", updated.getId());
        return updated;
    }

    @Override
    public void deleteById(String id) {
        log.debug("Deleting chat compress message by id: {}", id);
        chatCompressMessageRepository.deleteById(id);
        log.debug("Successfully deleted chat compress message with id: {}", id);
    }

    @Override
    public void deleteBySessionId(String sessionId) {
        log.debug("Deleting chat compress messages by sessionId: {}", sessionId);
        chatCompressMessageRepository.deleteBySessionId(sessionId);
        log.debug("Successfully deleted chat compress messages for sessionId: {}", sessionId);
    }

    @Override
    public void deleteByUsername(String username) {
        log.debug("Deleting chat compress messages by username: {}", username);
        chatCompressMessageRepository.deleteByUsername(username);
        log.debug("Successfully deleted chat compress messages for username: {}", username);
    }

    @Override
    public boolean existsById(String id) {
        log.debug("Checking if chat compress message exists by id: {}", id);
        return chatCompressMessageRepository.existsById(id);
    }
}
