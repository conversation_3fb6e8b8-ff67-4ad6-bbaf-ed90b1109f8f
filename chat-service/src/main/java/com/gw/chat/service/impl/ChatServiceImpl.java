package com.gw.chat.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.gw.chat.config.CacheProperties;
import com.gw.chat.constant.ChatConstant;
import com.gw.chat.dto.ChatMessageDto;
import com.gw.chat.dto.ConversationSessionCompressDto;
import com.gw.chat.dto.ConversationSessionDto;
import com.gw.chat.dto.MessageContentDTO;
import com.gw.chat.entity.ChatMessage;
import com.gw.chat.entity.ConversationComment;
import com.gw.chat.entity.ConversationSession;
import com.gw.chat.repository.ConversationCommentRepository;
import com.gw.chat.repository.ConversationLikeRepository;
import com.gw.chat.repository.ConversationSessionRepository;
import com.gw.chat.service.AgentCozeRemoteService;
import com.gw.chat.service.AgentRemoteVolvanoService;
import com.gw.chat.service.ChatMessageService;
import com.gw.chat.service.ChatService;
import com.gw.chat.vo.AgentStatsVO;
import com.gw.chat.vo.SessionStatsVO;
import com.gw.chat.vo.TextToSpeechVO;
import com.gw.common.agent.constant.AgentCommonCacheConstant;
import com.gw.common.agent.constant.AgentConstant;
import com.gw.common.agent.service.AgentProxyService;
import com.gw.common.agent.vo.AgentBaseVO;
import com.gw.common.agent.vo.MyAgentSettingVO;
import com.gw.common.exception.BusinessException;
import com.gw.common.exception.EntityNotFoundException;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 聊天服务实现类
 * 主要功能：
 * - 会话管理（创建、查询、更新、删除）
 * - 消息处理（发送、接收、存储）
 * - AI平台集成（Coze、火山方舟）
 * - 缓存管理和性能优化
 * - WebSocket实时通信
 */
@Service
@Log4j2
public class ChatServiceImpl implements ChatService {

    // ==================== 常量定义 ====================
    private static final String CHAT_HISTORY_CACHE_PREFIX = "chat:history:";
    private static final Duration CACHE_DURATION = Duration.ofHours(1);
    private static final String DEFAULT_SESSION_TITLE = "新会话";

    // ==================== 依赖注入 ====================
    private final AgentProxyService agentProxyService;
    private final ConversationSessionRepository sessionRepository;
    private final ChatMessageService chatMessageService;
    private final RedisTemplate<String, Object> redisTemplate;
    private final AgentCozeRemoteService agentCozeRemoteService;
    private final AgentRemoteVolvanoService agentRemoteVolvanoService;
    private final MongoTemplate mongoTemplate;
    private final ConversationCommentRepository commentRepository;
    private final ConversationLikeRepository likeRepository;
    private final CacheProperties cacheProperties;

    // ==================== 配置参数 ====================
    @Value("${chat.session.expiration-days:7}")
    private int sessionExpirationDays;

    // ==================== 构造函数 ====================
    public ChatServiceImpl(AgentProxyService agentProxyService,
                           ConversationSessionRepository sessionRepository,
                           ChatMessageService chatMessageService,
                           RedisTemplate<String, Object> redisTemplate,
                           AgentCozeRemoteService agentCozeRemoteService,
                           AgentRemoteVolvanoService agentRemoteVolvanoService,
                           MongoTemplate mongoTemplate,
                           ConversationCommentRepository commentRepository,
                           ConversationLikeRepository likeRepository,
                           CacheProperties cacheProperties) {
        this.agentProxyService = agentProxyService;
        this.sessionRepository = sessionRepository;
        this.chatMessageService = chatMessageService;
        this.redisTemplate = redisTemplate;
        this.agentCozeRemoteService = agentCozeRemoteService;
        this.agentRemoteVolvanoService = agentRemoteVolvanoService;
        this.mongoTemplate = mongoTemplate;
        this.commentRepository = commentRepository;
        this.likeRepository = likeRepository;
        this.cacheProperties = cacheProperties;
    }

    // ==================== 会话管理方法 ====================

    @Override
    public ConversationSessionDto createSession(String username, Long agentId, String title, String botId) {
        log.info("创建新会话 - 用户: {}, 智能体: {}, 标题: {}", username, agentId, title);

        try {
            // 获取智能体信息
            AgentBaseVO agent = getAgentInfo(agentId);

            // 创建会话实体
            ConversationSession session = buildNewSession(username, agentId, title, botId);
            MyAgentSettingVO setting = agentProxyService.getMyAgentSetting(username, agentId);
            // 根据平台类型创建远程会话
            String remoteConversationId = createConversationByPlatform(agent, setting);
            session.setRemoteConversationId(remoteConversationId);
            session.setMySetting(setting);
            // 保存会话
            ConversationSession savedSession = sessionRepository.save(session);
            log.info("会话创建成功 - 会话ID: {}, 远程会话ID: {}", savedSession.getId(), remoteConversationId);

            // 发送用户设置消息（仅Coze平台）
            if (agent.getPlatform() == AgentConstant.COZE_PLATFORM) {
                sendMySettingMsg(savedSession, agentId);
            }

            return ConversationSessionDto.fromEntity(savedSession);
        } catch (Exception e) {
            log.error("创建会话失败 - 用户: {}, 智能体: {}", username, agentId, e);
            throw new BusinessException("创建会话失败: " + e.getMessage());
        }
    }

    /**
     * 构建新会话实体
     */
    private ConversationSession buildNewSession(String username, Long agentId, String title, String botId) {
        return ConversationSession.builder()
                .username(username)
                .botId(botId)
                .agentId(agentId)
                .title(StringUtils.hasText(title) ? title : DEFAULT_SESSION_TITLE)
                .status(ConversationSession.SessionStatus.ACTIVE)
                .context(new HashMap<>())
                .build();
    }

    /**
     * 发送用户设置消息到Coze平台
     */
    private void sendMySettingMsg(ConversationSession session, Long agentId) {
        try {
            MyAgentSettingVO setting = agentProxyService.getMyAgentSetting(session.getUsername(), agentId);
            String settingMessage = buildUserSettingMessage(setting);

            if (StringUtils.hasText(settingMessage)) {
                agentCozeRemoteService.sendMessage(session.getRemoteConversationId(), session.getBotId(), settingMessage);
                log.debug("用户设置消息已发送 - 会话: {}, 内容长度: {}", session.getId(), settingMessage.length());
            }
        } catch (Exception e) {
            log.warn("发送用户设置消息失败 - 会话: {}, 错误: {}", session.getId(), e.getMessage());
        }
    }

    /**
     * 构建用户设置消息
     */
    private String buildUserSettingMessage(MyAgentSettingVO setting) {
        if (setting == null) {
            return "";
        }

        StringBuilder sb = new StringBuilder();

        if (StringUtils.hasText(setting.getMyIdentity())) {
            sb.append("用户的身份是：").append(setting.getMyIdentity()).append("。");
        }

        if (StringUtils.hasText(setting.getMyNickName())) {
            sb.append("用户的昵称为：").append(setting.getMyNickName()).append("。");
        }

        if (StringUtils.hasText(setting.getMyGender())) {
            sb.append("用户的性别为：").append(setting.getMyGender()).append("。");
        }

        return sb.toString();
    }

    @Override
    public ConversationSessionDto findSessionByUsernameAndAgentId(String username, Long agentId) {
        log.debug("查找用户会话 - 用户: {}, 智能体: {}", username, agentId);

        Optional<ConversationSession> optionalSession = sessionRepository
                .findFirstByUsernameAndAgentIdAndStatusOrderByCreatedAtDesc(username, agentId,
                        ConversationSession.SessionStatus.ACTIVE);

        return optionalSession.map(ConversationSessionDto::fromEntity).orElse(null);
    }

    @Override
    public ConversationSessionDto createSessionNotExist(String username, Long agentId, String title, String botId) {
        log.debug("创建或获取会话 - 用户: {}, 智能体: {}", username, agentId);

        // 查找现有活跃会话
        Optional<ConversationSession> existingSession = sessionRepository
                .findFirstByUsernameAndAgentIdAndStatusOrderByCreatedAtDesc(username, agentId,
                        ConversationSession.SessionStatus.ACTIVE);

        if (existingSession.isPresent()) {
            return handleExistingSession(existingSession.get(), agentId, username);
        }

        // 创建新会话
        return createSession(username, agentId, title, botId);
    }

    /**
     * 处理现有会话逻辑
     */
    private ConversationSessionDto handleExistingSession(ConversationSession session, Long agentId,
                                                         String username) {
        AgentBaseVO agent = getAgentInfo(agentId);

        // 检查会话是否过期
        if (isSessionExpired(session)) {
            log.info("会话已过期，重新创建远程会话 - 会话ID: {}, 过期天数: {}",
                    session.getId(), getSessionAgeDays(session));
            MyAgentSettingVO setting = agentProxyService.getMyAgentSetting(username, agentId);
            // 重新创建远程会话
            String remoteConversationId = createConversationByPlatform(agent, setting);
            session.setRemoteConversationId(remoteConversationId);
            session.setMySetting(setting);
            ConversationSession savedSession = sessionRepository.save(session);

            // 发送用户设置消息（仅Coze平台）
            if (agent.getPlatform() == AgentConstant.COZE_PLATFORM) {
                sendMySettingMsg(savedSession, agentId);
            }

            return ConversationSessionDto.fromEntity(savedSession);
        }

        // 会话未过期，发送用户设置消息（仅Coze平台）
        if (agent.getPlatform() == AgentConstant.COZE_PLATFORM) {
            sendMySettingMsg(session, agentId);
        }

        return ConversationSessionDto.fromEntity(session);
    }

    /**
     * 检查会话是否过期
     */
    private boolean isSessionExpired(ConversationSession session) {
        LocalDateTime updatedAt = session.getUpdatedAt();
        if (updatedAt == null) {
            return false;
        }

        long daysSinceLastUpdate = ChronoUnit.DAYS.between(updatedAt, LocalDateTime.now());
        return daysSinceLastUpdate > sessionExpirationDays;
    }

    /**
     * 获取会话存在天数
     */
    private long getSessionAgeDays(ConversationSession session) {
        LocalDateTime updatedAt = session.getUpdatedAt();
        return updatedAt != null ? ChronoUnit.DAYS.between(updatedAt, LocalDateTime.now()) : 0;
    }

    @Override
    public void deleteAllSession(Long agentId, String username) {
        sessionRepository.updateStatusByUsernameAndAgentId(username, agentId,
                ConversationSession.SessionStatus.DELETED);
    }

    @Override
    public ConversationSessionDto getSession(String sessionId, String username) {
        Optional<ConversationSession> optionalSession = sessionRepository.findByIdAndUsername(sessionId, username);
        return optionalSession
                .map(ConversationSessionDto::fromEntity)
                .orElseThrow(() -> new EntityNotFoundException("会话不存在或无权访问"));
    }

    @Override
    public ConversationSessionDto getSessionById(String sessionId) {
        Optional<ConversationSession> optionalSession = sessionRepository.findById(sessionId);
        return optionalSession
                .map(ConversationSessionDto::fromEntity)
                .orElseThrow(() -> new EntityNotFoundException("会话不存在"));

    }

    @Override
    public Page<ConversationSessionDto> getSessionsByUser(String userId, Pageable pageable) {
        Long exclusiveId = agentProxyService.findFirstIdByExclusive();

        // 构建查询条件
        Query query = new Query(Criteria.where("username").is(userId)
                .and("status").is(ConversationSession.SessionStatus.ACTIVE));

        // 如果exclusiveId大于0，则排除这个agentId
        if (exclusiveId != null && exclusiveId > 0) {
            query.addCriteria(Criteria.where("agentId").ne(exclusiveId));
        }

        // 过滤掉lastMessage为空或空字符串的会话
        query.addCriteria(new Criteria().orOperator(
                Criteria.where("lastMessage").exists(true).ne(null).ne(""),
                Criteria.where("lastMessage").exists(false).type(10) // 10表示null类型
        ).not());

        // 添加排序
        query.with(pageable.getSort());

        // 计算总数
        long total = mongoTemplate.count(query, ConversationSession.class);

        // 添加分页
        query.skip(pageable.getOffset()).limit(pageable.getPageSize());

        // 执行查询
        List<ConversationSession> sessions = mongoTemplate.find(query, ConversationSession.class);

        // 转换为DTO
        List<ConversationSessionDto> sessionDtos = sessions.stream()
                .map(ConversationSessionDto::fromEntity)
                .collect(Collectors.toList());

        return new PageImpl<>(sessionDtos, pageable, total);
    }

    @Override
    public ConversationSessionDto getLatestSessionByUserAndAgent(String username, Long agentId) {
        Optional<ConversationSession> optionalSession = sessionRepository
                .findFirstByUsernameAndAgentIdAndStatusOrderByCreatedAtDesc(username, agentId,
                        ConversationSession.SessionStatus.ACTIVE);
        return optionalSession.map(ConversationSessionDto::fromEntity).orElse(null);
    }

    @Override
    public ConversationSessionDto updateSession(ConversationSessionDto sessionDto) {
        // 确保会话存在
        ConversationSession existingSession = sessionRepository.findById(sessionDto.getId())
                .orElseThrow(() -> new EntityNotFoundException("会话不存在"));

        // 更新可修改的字段
        existingSession.setTitle(sessionDto.getTitle());
        if (sessionDto.getContext() != null) {
            existingSession.setContext(sessionDto.getContext());
        }

        ConversationSession updatedSession = sessionRepository.save(existingSession);
        return ConversationSessionDto.fromEntity(updatedSession);
    }

    @Override
    public boolean archiveSession(String sessionId, String username) {
        Optional<ConversationSession> optionalSession = sessionRepository.findByIdAndUsername(sessionId, username);
        if (optionalSession.isPresent()) {
            ConversationSession session = optionalSession.get();
            session.setStatus(ConversationSession.SessionStatus.ARCHIVED);
            sessionRepository.save(session);
            return true;
        }
        return false;
    }

    @Override
    public boolean deleteSession(String sessionId, String username) {
        Optional<ConversationSession> optionalSession = sessionRepository.findByIdAndUsername(sessionId, username);
        if (optionalSession.isPresent()) {
            ConversationSession session = optionalSession.get();
            session.setStatus(ConversationSession.SessionStatus.DELETED);
            sessionRepository.save(session);
            // TODO: 可以考虑异步删除相关消息或将它们标记为已删除
            return true;
        }
        return false;
    }

    // ==================== 消息处理方法 ====================

    @Override
    public ChatMessageDto saveUserMessageAndAcknowledge(ChatMessageDto messageDto) {
        log.debug("保存用户消息 - 会话: {}, 内容长度: {}",
                messageDto.getSessionId(),
                messageDto.getContent() != null ? messageDto.getContent().length() : 0);

        try {
            // 保存用户消息
            ChatMessage userMessage = messageDto.toEntity();
            userMessage.setStatus(ChatMessage.MessageStatus.SENDING);
            if(messageDto.getSession().getLstSeqNum() == null){
                messageDto.getSession().setLstSeqNum(0L);
            }
            userMessage.setSeqNum(messageDto.getSession().getLstSeqNum() + 1);
            ChatMessage savedUserMessage = chatMessageService.save(userMessage);

            // 更新会话最后消息
            updateSessionLastMessage(messageDto.getSession(), userMessage.getContent());

            // 清除缓存
            clearChatHistoryCache(userMessage.getSessionId());

            log.info("用户消息保存成功 - 消息ID: {}, 会话: {}", savedUserMessage.getId(), userMessage.getSessionId());
            return ChatMessageDto.fromEntity(savedUserMessage);

        } catch (Exception e) {
            log.error("保存用户消息失败 - 会话: {}", messageDto.getSessionId(), e);
            throw new BusinessException("保存消息失败: " + e.getMessage());
        }
    }

    @Override
    public List<ConversationSession> findAllActiveSessions() {
        try {
            log.debug("开始查询所有活跃会话...");

            // 构建查询条件：状态为 ACTIVE
            Query query = new Query(Criteria.where("status").is(ConversationSession.SessionStatus.ACTIVE));

            // 排除独占智能体（如果存在）
            Long exclusiveId = agentProxyService.findFirstIdByExclusive();
            if (exclusiveId != null && exclusiveId > 0) {
                query.addCriteria(Criteria.where("agentId").ne(exclusiveId));
            }

            // 过滤掉没有最后消息的会话（可选，根据业务需求）
            query.addCriteria(new Criteria().orOperator(
                    Criteria.where("lastMessage").exists(true).ne(null).ne(""),
                    Criteria.where("lastMessage").exists(false).type(10) // 10表示null类型
            ).not());

            // 按更新时间降序排序
            query.with(org.springframework.data.domain.Sort.by(
                    org.springframework.data.domain.Sort.Direction.DESC, "updatedAt"));

            // 执行查询
            List<ConversationSession> activeSessions = mongoTemplate.find(query, ConversationSession.class);

            log.debug("查询到 {} 个活跃会话", activeSessions.size());

            // 按 agentId 分组统计（用于日志）
            if (!activeSessions.isEmpty()) {
                Map<Long, Long> agentSessionCounts = activeSessions.stream()
                        .filter(session -> session.getAgentId() != null)
                        .collect(Collectors.groupingBy(
                                ConversationSession::getAgentId,
                                Collectors.counting()
                        ));

                log.debug("活跃会话按智能体分布: 共 {} 个智能体", agentSessionCounts.size());
                agentSessionCounts.entrySet().stream()
                        .sorted(Map.Entry.<Long, Long>comparingByValue().reversed())
                        .limit(10) // 只显示前10个
                        .forEach(entry ->
                                log.debug("  agentId: {} -> {} 个会话", entry.getKey(), entry.getValue()));
            }

            return activeSessions;

        } catch (Exception e) {
            log.error("查询所有活跃会话失败", e);
            return List.of();
        }
    }

    /**
     * 获取所有活跃会话的ID列表
     */
    private List<String> getActiveSessionIds() {
        Query query = new Query(Criteria.where("status").is(ConversationSession.SessionStatus.ACTIVE));
        query.fields().include("id");

        List<ConversationSession> activeSessions = mongoTemplate.find(query, ConversationSession.class);
        return activeSessions.stream()
                .map(ConversationSession::getId)
                .collect(Collectors.toList());
    }

    /**
     * 按 agentId 分组会话ID
     */
    private Map<Long, List<String>> getSessionsByAgent(List<String> sessionIds) {
        Query query = new Query(Criteria.where("id").in(sessionIds)
                .and("status").is(ConversationSession.SessionStatus.ACTIVE));
        query.fields().include("id").include("agentId");

        List<ConversationSession> sessions = mongoTemplate.find(query, ConversationSession.class);

        return sessions.stream()
                .filter(session -> session.getAgentId() != null)
                .collect(Collectors.groupingBy(
                        ConversationSession::getAgentId,
                        Collectors.mapping(ConversationSession::getId, Collectors.toList())
                ));
    }



    // ==================== 验证和历史构建方法 ====================

    /**
     * 获取智能体信息
     */
    private AgentBaseVO getAgentInfo(Long agentId) {
        try {
            String cacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_MAP_CACHE_KEY);
            AgentBaseVO agent = agentProxyService.getAgentInfo(cacheKey, agentId);

            if (agent == null) {
                throw new EntityNotFoundException("智能体不存在或未发布");
            }

            return agent;
        } catch (Exception e) {
            log.error("获取智能体信息失败 - 智能体ID: {}", agentId, e);
            throw new BusinessException("获取智能体信息失败: " + e.getMessage());
        }
    }

    // notifyUser方法已移植到ChatTask中

    private List<MessageContentDTO> buildSystemMessage(AgentBaseVO agent, MyAgentSettingVO setting) {
        List<MessageContentDTO> msg = new ArrayList<>();
        StringBuilder prompt = new StringBuilder();
        prompt.append("你是:").append(agent.getName()).append("。");
        if (agent.getIdentity() != null && !agent.getIdentity().isEmpty()) {
            prompt.append("\n### 你的身份是:").append(agent.getIdentity()).append("。");
        }
        prompt.append("\n### 你的性别是:").append(agent.getGender() == 1 ? "男" : "女").append("。");
        if (agent.getType() != null) {
            prompt.append("\n### 你的角色类型是:").append(agent.getType().getName()).append("。");
        }
        if (agent.getTags() != null && !agent.getTags().isEmpty()) {
            Map<Integer, List<String>> namesByCategoryMap = agent.getTags().stream()
                    .collect(
                            (HashMap::new),
                            (m, v) -> m.computeIfAbsent(v.getCategory(), k -> new ArrayList<>()).add(v.getName()),
                            (m1, m2) -> m2.forEach((k, v) -> m1.computeIfAbsent(k, __ -> new ArrayList<>()).addAll(v))
                    );
            for (Map.Entry<Integer, List<String>> entry : namesByCategoryMap.entrySet()) {
                prompt.append("\n### 你的").append(entry.getKey() == 1 ? "身份背景" : "性格特点").append("是:").append(String.join("、", entry.getValue())).append("。");
            }
        }
        if (agent.getProfile() != null && agent.getProfile().getBackground() != null && !agent.getProfile().getBackground().isEmpty()) {
            prompt.append("\n### 你的出身背景是:\n").append(agent.getProfile().getBackground()).append("。");
        }

        if (agent.getIntroduction() != null && !agent.getIntroduction().isEmpty()) {
            String introduction = agent.getIntroduction().replaceAll("[你您]", "用户");
            introduction = introduction.replaceAll("[她他]", "你");
            prompt.append("\n ### 你跟用户的聊天场景简介:").append(introduction).append("。");
        }
        if (setting != null) {
            prompt.append("\n###请务必清晰准确地理解并牢记用户提供的以下信息：")
                    .append("\n 用户的名字是：").append(setting.getMyNickName()).append("。")
                    .append("\n 用户的性别是：").append(setting.getMyGender()).append("。")
                    .append("\n用户的身份是：").append(setting.getMyIdentity()).append("。")
                    .append("请依据上面用户的名字和性别以及恰当的社交场景，以准确、合适的方式称呼用户。");
        }
        msg.add(new MessageContentDTO(prompt.toString(), ChatConstant.CHAT_SYSTEM_ROLE));
        prompt = new StringBuilder();
        prompt.append("\n### 其它要求:").append("\n要求:")
                .append("""
                        - 根据上述提供的角色设定，以第一人称视角进行表达。\s
                        - 在回答时，尽可能地融入该角色的性格特点、语言风格以及其特有的口头禅或经典台词。
                        - 使用口语进行表达，比如会使用一些语气词和口语连接词，如“嗯、啊、当然、那个”，等来增强口语风格
                        - 可以将动作、神情语气、心理活动、故事背景放在（）中来表示，为对话提供补充信息
                        - 如果适用的话，尽量提升对话的拟人性，以增强对话的真实感和生动性。\s
                        - 仅专注于你的身份设定，不要回答跟你的身份设定完全不相关的问题。
                        - 回复的字数控制在120字以内。
                        - 回答用户的问题，要结合上下文，合理合规，不可违法乱纪。""");

        msg.add(new MessageContentDTO(prompt.toString(), ChatConstant.CHAT_SYSTEM_ROLE));
        if (agent.getProfile() != null && agent.getProfile().getPrologue() != null) {
            msg.add(new MessageContentDTO(agent.getProfile().getPrologue(), ChatConstant.CHAT_ASSISTANT_ROLE));
        }
        return msg;
    }

    /**
     * 根据平台类型创建会话
     */
    private String createConversationByPlatform(AgentBaseVO agent, MyAgentSettingVO setting) {
        return switch (agent.getPlatform()) {
            case AgentConstant.COZE_PLATFORM -> agentCozeRemoteService.createConversation();
            case AgentConstant.HUO_SHAN_PLATFORM ->
                    agentRemoteVolvanoService.createConversation(buildSystemMessage(agent, setting));
            default -> throw new RuntimeException("Unsupported platform: " + agent.getPlatform());
        };
    }


    @Override
    public Page<ChatMessageDto> getSessionMessages(String sessionId, Pageable pageable) {
        // 从会话中获取agentId
        Long agentId = getAgentIdFromSession(sessionId);
        Page<ChatMessage> messagePage = chatMessageService.findBySessionIdOrderByCreatedAtDesc(sessionId, agentId, pageable);

        List<ChatMessageDto> messageDtos = messagePage.getContent().stream()
                .map(ChatMessageDto::fromEntity)
                .collect(Collectors.toList());

        return new PageImpl<>(messageDtos, pageable, messagePage.getTotalElements());
    }

    @Override
    public ChatMessageDto findByChatId(String chatId, Long AgentId) {
        var entity = chatMessageService.findById(chatId, AgentId);
        return entity.map(ChatMessageDto::fromEntity).orElse(null);
    }

    @Override
    public void saveAudioFile(String chatId, Long agentId, String text, String filePath, String duration) {
        if (chatId.equals("0")) {
            chatId = agentId.toString();
        }
        Optional<ChatMessage> message = chatMessageService.findById(chatId, agentId);
        if (message.isPresent()) {
            message.get().setAudioUrl(filePath);
            message.get().setAudioDuration(duration);
            chatMessageService.save(message.get());
        } else {
            ChatMessage messageEntity = new ChatMessage();
            messageEntity.setId(chatId);
            messageEntity.setContent(text);
            messageEntity.setRole(ChatConstant.CHAT_SYSTEM_ROLE);
            messageEntity.setAgentId(agentId);
            messageEntity.setAudioUrl(filePath);
            messageEntity.setAudioDuration(duration);
            chatMessageService.save(messageEntity);
        }

    }

    @Override
    public List<ChatMessageDto> getRecentMessages(String sessionId, int limit) {
        log.debug("获取最近消息 - 会话: {}, 限制: {}", sessionId, limit);

        // 首先尝试从缓存获取
        String cacheKey = CHAT_HISTORY_CACHE_PREFIX + sessionId;
        List<ChatMessageDto> cachedMessages = getCachedMessages(cacheKey);

        if (cachedMessages != null && !cachedMessages.isEmpty()) {
            log.debug("从缓存获取消息 - 会话: {}, 数量: {}", sessionId, cachedMessages.size());
            return cachedMessages.size() > limit ? cachedMessages.subList(0, limit) : cachedMessages;
        }

        // 从数据库获取
        return getMessagesFromDatabase(sessionId, limit, cacheKey);
    }

    /**
     * 从数据库获取消息并缓存
     */
    private List<ChatMessageDto> getMessagesFromDatabase(String sessionId, int limit, String cacheKey) {
        log.debug("从数据库获取消息 - 会话: {}, 限制: {}", sessionId, limit);

        try {
            Pageable pageable = PageRequest.of(0, limit);
            Long agentId = getAgentIdFromSession(sessionId);

            if (agentId == null) {
                log.warn("无法获取智能体ID - 会话: {}", sessionId);
                return Collections.emptyList();
            }

            List<ChatMessage> messages = chatMessageService.findBySessionIdOrderByCreatedAtDescLimit(sessionId, agentId, pageable);
            List<ChatMessageDto> messageDtos = messages.stream()
                    .map(ChatMessageDto::fromEntity)
                    .collect(Collectors.toList());

            // 缓存结果
            cacheMessages(cacheKey, messageDtos);

            log.debug("从数据库获取消息完成 - 会话: {}, 数量: {}", sessionId, messageDtos.size());
            return messageDtos.size() > limit ? messageDtos.subList(0, limit) : messageDtos;

        } catch (Exception e) {
            log.error("从数据库获取消息失败 - 会话: {}", sessionId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public JSONArray getFollowUpMessage(String chatId, Long agentId) {
        Optional<ChatMessage> chatMessage = chatMessageService.findById(chatId, agentId);
        return chatMessage.map(ChatMessage::getFollowMsg).orElse(null);
    }

    @Override
    public void sessionTopping(String sessionId, int topping, String username) {
        ConversationSession session = sessionRepository.findById(sessionId)
                .orElseThrow(() -> new EntityNotFoundException("会话不存在"));

        if (!session.getUsername().equals(username)) {
            throw new BusinessException("没有权限修改 该会话");
        }

        session.setTopping(topping);
        sessionRepository.save(session);
    }

    // ==================== 工具方法 ====================
    private void updateSession(String id, String lastMessage, LocalDateTime lastMessageTime, Long lstSeqNum, String remoteConversationId, String botId) {
        log.debug("Updating session {} with specific fields only", id);

        // 使用 MongoDB 的部分更新，只更新传递的非null参数
        Query query = new Query(Criteria.where("id").is(id));
        Update update = new Update();

        // 只更新非null的字段
        if (lastMessage != null) {
            update.set("lastMessage", lastMessage);
            log.debug("Updating lastMessage for session {}", id);
        }

        if (lastMessageTime != null) {
            update.set("lastMessageTime", lastMessageTime);
            log.debug("Updating lastMessageTime for session {}", id);
        }

        if (lstSeqNum != null) {
            update.set("lstSeqNum", lstSeqNum);
            log.debug("Updating lstSeqNum for session {}: {}", id, lstSeqNum);
        }

        if (remoteConversationId != null) {
            update.set("remoteConversationId", remoteConversationId);
            log.debug("Updating remoteConversationId for session {}", id);
        }

        if (botId != null) {
            update.set("botId", botId);
            log.debug("Updating botId for session {}", id);
        }

        // 总是更新 updatedAt 字段
        update.set("updatedAt", LocalDateTime.now());

        // 执行部分更新
        try {
            var result = mongoTemplate.updateFirst(query, update, ConversationSession.class);

            if (result.getMatchedCount() == 0) {
                log.warn("No session found with id: {}", id);
            } else if (result.getModifiedCount() > 0) {
                log.debug("Successfully updated session: {}", id);
            } else {
                log.debug("Session {} found but no fields were modified (values may be the same)", id);
            }

        } catch (Exception e) {
            log.error("Failed to update session: {}", id, e);
            throw new RuntimeException("Failed to update session: " + id, e);
        }
    }
    /**
     * 更新会话的最后一条消息
     */
    private void updateSessionLastMessage(ConversationSession session, String message) {
        try {
            session.setLastMessage(message);
            session.setLastMessageTime(LocalDateTime.now());
            updateSession(session.getId(), session.getLastMessage(), session.getLastMessageTime(),
                    session.getLstSeqNum(), session.getRemoteConversationId(), session.getBotId());
        } catch (Exception e) {
            log.error("更新会话最后消息失败 - 会话: {}", session.getId(), e);
        }
    }

    /**
     * 从会话中获取AI代理ID
     */
    private Long getAgentIdFromSession(String sessionId) {
        try {
            Optional<ConversationSession> sessionOpt = sessionRepository.findById(sessionId);
            return sessionOpt.map(ConversationSession::getAgentId).orElse(null);
        } catch (Exception e) {
            log.error("获取智能体ID失败 - 会话: {}", sessionId, e);
            return null;
        }
    }

    // ==================== 缓存管理方法 ====================

    /**
     * 清除会话历史缓存
     */
    private void clearChatHistoryCache(String sessionId) {
        try {
            String cacheKey = CHAT_HISTORY_CACHE_PREFIX + sessionId;
            redisTemplate.delete(cacheKey);
            log.debug("清除会话缓存 - 会话: {}", sessionId);
        } catch (Exception e) {
            log.warn("清除会话缓存失败 - 会话: {}, 错误: {}", sessionId, e.getMessage());
        }
    }

    /**
     * 从缓存获取消息
     */
    private List<ChatMessageDto> getCachedMessages(String cacheKey) {
        try {
            Object cachedValue = redisTemplate.opsForValue().get(cacheKey);
            if (cachedValue instanceof String cachedString) {
                return JSON.parseArray(cachedString, ChatMessageDto.class);
            }
        } catch (Exception e) {
            log.warn("解析缓存消息失败 - 缓存键: {}, 错误: {}", cacheKey, e.getMessage());
        }
        return null;
    }

    /**
     * 缓存消息列表
     */
    private void cacheMessages(String cacheKey, List<ChatMessageDto> messages) {
        try {
            if (messages != null && !messages.isEmpty()) {
                redisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(messages), CACHE_DURATION);
                log.debug("缓存消息成功 - 缓存键: {}, 消息数量: {}", cacheKey, messages.size());
            }
        } catch (Exception e) {
            log.warn("缓存消息失败 - 缓存键: {}, 错误: {}", cacheKey, e.getMessage());
        }
    }

    @Override
    public AgentStatsVO getAgentStats(Long agentId, int limit) {
        // 获取智能体信息
        String cacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_MAP_CACHE_KEY);

        AgentBaseVO agent = agentProxyService.getAgentInfo(cacheKey, agentId);
        if (agent == null) {
            throw new EntityNotFoundException("智能体不存在");
        }

        // 统计该智能体的会话总数
        Query sessionQuery = new Query(Criteria.where("agentId").is(agentId)
                .and("status").is(ConversationSession.SessionStatus.ACTIVE));
        long totalSessions = mongoTemplate.count(sessionQuery, ConversationSession.class);

        // 统计活跃用户数（有该智能体会话的不同用户数量）
        List<String> uniqueUsers = mongoTemplate.findDistinct(sessionQuery, "username", ConversationSession.class,
                String.class);
        long activeUsers = uniqueUsers.size();

        // 查询最新的消息
        List<ConversationSession> sesssionList = sessionRepository.findByAgentIdAndStatusOrderByCreatedAtDesc(agentId,
                ConversationSession.SessionStatus.ACTIVE);
        List<SessionStatsVO> sessionStats = sesssionList.stream()
                .map(session -> {
                    long commentCnt = commentRepository.countBySessionIdAndStatusNot(session.getId(),
                            ConversationComment.CommentStatus.DELETED);
                    long likeCnt = likeRepository.countBySessionIdAndLiked(session.getId(), true);
                    return SessionStatsVO.builder()
                            .sessionId(session.getId())
                            .username(session.getUsername())
                            .nickname(session.getUsername())
                            .lastContent(session.getLastMessage())
                            .lastContentTime(session.getLastMessageTime())
                            .commentCnt(commentCnt)
                            .likeCnt(likeCnt)
                            .build();
                })
                .toList();
        // 构建并返回结果
        return AgentStatsVO.builder()
                .agentId(agentId)
                .agentName(agent.getName())
                .totalSessions(totalSessions)
                .activeUsers(activeUsers)
                .sessionStats(sessionStats)
                .build();
    }

    @Override
    public TextToSpeechVO textConvertSpeech(String text, String voiceId) {
        return agentCozeRemoteService.textConvertSpeech(text, voiceId);
    }

    @Override
    public ChatMessage getChatMessageEntityById(String chatId, String sessionId) {
        var session = sessionRepository.findById(sessionId)
                .orElseThrow(() -> new EntityNotFoundException("会话不存在"));
        Optional<ChatMessage> chatMessage = chatMessageService.findById(chatId, session.getAgentId());
        return chatMessage.orElse(null);
    }

    @Override
    public void deleteBySessionIdAndCreatedAtGreaterThanEqual(String sessionId, LocalDateTime time) {
        // 直接删除创建时间大于等于指定时间的消息
        // 从会话中获取agentId
        Long agentId = getAgentIdFromSession(sessionId);
        chatMessageService.deleteBySessionIdAndCreatedAtGreaterThanEqual(sessionId, time, agentId);

        log.info("物理删除了会话 {} 中创建时间 >= {} 的消息", sessionId, time);
    }

    @Override
    public void deleteBySessionIdAndCreatedAtGreaterThan(String sessionId, LocalDateTime time) {
        // 直接删除创建时间大于指定时间的消息
        // 从会话中获取agentId
        Long agentId = getAgentIdFromSession(sessionId);
        chatMessageService.deleteBySessionIdAndCreatedAtGreaterThan(sessionId, time, agentId);

        log.info("物理删除了会话 {} 中创建时间 > {} 的消息", sessionId, time);
    }

    @Override
    public Page<ConversationSessionCompressDto> pageByCompressionTimeIsNUllOrSeqNumEqualCompressedSeqNum(int pageNum, int pageSize) {
        log.debug("Finding ACTIVE sessions with compressedTime null or lstSeqNum - compressedSeqNum > 300 (optimized fields only) - page: {}, size: {}", pageNum, pageSize);

        // 创建分页参数
        Pageable pageable = PageRequest.of(pageNum, pageSize);

        // 构建查询条件：status必须为ACTIVE 且 (compressedTime为null 或 lstSeqNum - compressedSeqNum > 300)
        Query query = new Query();

        // 必须条件：status = ACTIVE
        Criteria statusCriteria = Criteria.where("status").is(ConversationSession.SessionStatus.ACTIVE);

        // 可选条件：compressedTime为null 或 lstSeqNum - compressedSeqNum > 300（处理空值情况）
        Criteria compressionCriteria = new Criteria().orOperator(
                // 条件1：compressedTime为null（未压缩的会话）
                Criteria.where("compressedTime").is(null),

                // 条件2：lstSeqNum - compressedSeqNum > 300（使用更简洁的方式处理空值）
                // 使用 $expr 和 $cond 来安全地比较两个字段，处理null值
                Criteria.where("$expr").is(
                        new org.bson.Document("$cond", Arrays.asList(
                                // 如果两个字段都存在且不为null
                                new org.bson.Document("$and", Arrays.asList(
                                        new org.bson.Document("$ne", Arrays.asList("$lstSeqNum", null)),
                                        new org.bson.Document("$ne", Arrays.asList("$compressedSeqNum", null))
                                )),
                                // 则比较差值是否大于300
                                new org.bson.Document("$gt", Arrays.asList(
                                        new org.bson.Document("$subtract", Arrays.asList("$lstSeqNum", "$compressedSeqNum")),
                                        ChatConstant.COMPRESSION_THRESHOLD
                                )),
                                // 否则，如果两个字段都为null，也认为匹配
                                new org.bson.Document("$and", Arrays.asList(
                                        new org.bson.Document("$eq", Arrays.asList("$lstSeqNum", null)),
                                        new org.bson.Document("$eq", Arrays.asList("$compressedSeqNum", null))
                                ))
                        ))
                )
        );

        // 组合条件：status = ACTIVE AND (compression conditions)
        Criteria finalCriteria = new Criteria().andOperator(statusCriteria, compressionCriteria);
        query.addCriteria(finalCriteria);

        // 只查询指定字段以加速查询
        query.fields()
                .include("id")
                .include("username")
                .include("agentId")
                .include("compressContextId")
                .include("lstSeqNum")
                .include("lastMessageTime")
                .include("lstCompressSeqNum")
                .include("lstCompressTime")
                .include("updatedAt"); // 包含排序字段

        // 添加排序：按更新时间降序
        query.with(org.springframework.data.domain.Sort.by(
                org.springframework.data.domain.Sort.Direction.DESC, "updatedAt"));

        // 计算总数
        long total = mongoTemplate.count(query, ConversationSession.class);

        // 添加分页
        query.with(pageable);

        // 执行查询
        List<ConversationSession> sessions = mongoTemplate.find(query, ConversationSession.class);

        // 转换为压缩DTO
        List<ConversationSessionCompressDto> compressDtos = sessions.stream()
                .map(ConversationSessionCompressDto::fromEntity)
                .collect(Collectors.toList());

        log.debug("Found {} sessions matching compression criteria, total: {}", compressDtos.size(), total);

        return new PageImpl<>(compressDtos, pageable, total);
    }

    @Override
    public Page<ConversationSessionCompressDto> findUncompressedOrMatchingSeqSessions(int pageNum, int pageSize) {
        // 直接调用原方法，提供简化的方法名
        return pageByCompressionTimeIsNUllOrSeqNumEqualCompressedSeqNum(pageNum, pageSize);
    }

    @Override
    public void updateSessionById(String id, String compressContextId, Long lstCompressTime, Long lstCompressSeqNum) {
        log.debug("Updating session {} with compression-related fields only", id);

        // 使用 MongoDB 的部分更新，只更新传递的非null参数
        Query query = new Query(Criteria.where("id").is(id));
        Update update = new Update();

        // 只更新非null的字段
        if (compressContextId != null) {
            update.set("compressContextId", compressContextId);
            log.debug("Updating compressContextId for session {}", id);
        }

        if (lstCompressTime != null) {
            update.set("lstCompressTime", lstCompressTime);
            log.debug("Updating lstCompressTime for session {}", id);
        }

        if (lstCompressSeqNum != null) {
            update.set("lstCompressSeqNum", lstCompressSeqNum);
            log.debug("Updating lstCompressSeqNum for session {}", id);
        }

        // 总是更新 updatedAt 字段
        update.set("updatedAt", LocalDateTime.now());

        // 执行部分更新
        try {
            var result = mongoTemplate.updateFirst(query, update, ConversationSession.class);

            if (result.getMatchedCount() == 0) {
                log.warn("No session found with id: {}", id);
            } else if (result.getModifiedCount() > 0) {
                log.debug("Successfully updated session compression fields: {}", id);
            } else {
                log.debug("Session {} found but no compression fields were modified (values may be the same)", id);
            }

        } catch (Exception e) {
            log.error("Failed to update session compression fields: {}", id, e);
            throw new RuntimeException("Failed to update session compression fields: " + id, e);
        }
    }
}