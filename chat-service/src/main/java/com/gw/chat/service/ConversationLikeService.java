package com.gw.chat.service;

import com.gw.chat.dto.LikeActionDTO;
import com.gw.chat.vo.ConversationLikeVO;
import com.gw.chat.vo.LikeStatsVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface ConversationLikeService {

    /**
     * 点赞/取消点赞会话
     */
    ConversationLikeVO likeConversation(String username, LikeActionDTO likeDTO, Long agentId, String agentUsername);

    /**
     * 获取会话的点赞统计
     */
    LikeStatsVO getLikeStats(String conversationId, String username);

    /**
     * 判断用户是否对会话点赞
     */
    boolean hasUserLiked(String username, String conversationId);

    /**
     * 分页获取会话的点赞记录
     */
    Page<ConversationLikeVO> getLikesByConversationId(String conversationId, Pageable pageable);

    /**
     * 分页获取用户对特定AI助手的点赞记录
     */
    Page<ConversationLikeVO> getLikesByUsername(String username, Long agentId, Pageable pageable);
}