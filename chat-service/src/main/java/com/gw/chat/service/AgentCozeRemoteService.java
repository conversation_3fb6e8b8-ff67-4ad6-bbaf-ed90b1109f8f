package com.gw.chat.service;


import com.gw.chat.vo.AIResponseVO;
import com.gw.chat.vo.ChatContextVO;
import com.gw.chat.vo.TextToSpeechVO;

import java.util.List;
import java.util.Map;

public interface AgentCozeRemoteService {
    String createConversation();

    /**
     * Convert text to speech and return file path and duration
     *
     * @param text    The text content to convert to speech
     * @param voiceId The voice ID to use for the speech
     * @return String containing file path and duration in format "path:duration"
     */
    TextToSpeechVO textConvertSpeech(String text, String voiceId);

    void sendMessage(String conversationId, String botId, String message);

    AIResponseVO sendChatMessage(String conversationId, String botId, String uid, List<ChatContextVO> message, Map<String, String> customVariables, boolean audio, String voiceId) throws Exception;
}
