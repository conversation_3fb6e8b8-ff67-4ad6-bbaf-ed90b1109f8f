# CompressTask 配置使用指南

## 配置文件说明

### 1. 配置文件结构

项目中有以下几个配置文件：

- `application.yml` - 主配置文件
- `application-compress.yml` - 基础压缩任务配置
- `application-compress-memory-optimized.yml` - 内存优化配置

### 2. 启用内存优化配置的方法

#### 方法一：修改主配置文件（推荐）

在 `application.yml` 中修改：

```yaml
spring:
  profiles:
    include: 
      - compress-memory-optimized  # 启用内存优化配置
```

#### 方法二：启动参数指定

```bash
java -jar chat-service.jar --spring.profiles.active=compress-memory-optimized
```

#### 方法三：环境变量

```bash
export SPRING_PROFILES_ACTIVE=compress-memory-optimized
java -jar chat-service.jar
```

#### 方法四：使用提供的启动脚本

```bash
# Linux/Mac
./scripts/start-with-memory-optimization.sh

# Windows
scripts\start-with-memory-optimization.bat
```

## 配置参数详解

### 基础配置

```yaml
compress:
  task:
    enabled: true                    # 是否启用压缩任务
    interval-minutes: 30             # 执行间隔（分钟）
    page-size: 500                   # 分页大小（内存优化：减小）
    batch-size: 50                   # 批处理大小（内存优化：减小）
    thread-pool-size: 3              # 线程池大小（内存优化：减小）
    timeout: 1800000                 # 超时时间（毫秒）
```

### 内存优化配置

```yaml
compress:
  task:
    memory-threshold-percent: 75.0      # 内存使用率超过此值时进行垃圾回收
    force-gc-threshold-percent: 80.0    # 内存使用率超过此值时强制垃圾回收
    memory-check-interval: 5            # 每处理多少页检查一次内存
```

## 配置验证

### 1. 启动日志检查

启动应用后，查看日志中是否有以下信息：

```
=== 压缩任务配置加载完成 ===
启用状态: true
分页大小: 500
批处理大小: 50
线程池大小: 3
执行间隔: 30 分钟
内存阈值: 75.0%
强制GC阈值: 80.0%
===============================
```

### 2. 配置类验证

`CompressTaskConfig` 会在启动时验证配置的合理性，如果配置不合理会自动使用默认值并记录警告日志。

### 3. 运行时验证

可以通过以下方式检查配置是否生效：

```java
@Autowired
private CompressTask compressTask;

// 获取性能统计（包含内存信息）
String stats = compressTask.getPerformanceStats();
log.info("当前配置和状态: {}", stats);
```

## 不同环境的配置建议

### 开发环境

```yaml
compress:
  task:
    enabled: true
    interval-minutes: 60             # 较长间隔，减少开发时干扰
    page-size: 100                   # 小数据量
    batch-size: 10
    thread-pool-size: 2
    memory-threshold-percent: 70.0
```

### 测试环境

```yaml
compress:
  task:
    enabled: true
    interval-minutes: 30
    page-size: 300
    batch-size: 30
    thread-pool-size: 3
    memory-threshold-percent: 75.0
```

### 生产环境（内存优化）

```yaml
compress:
  task:
    enabled: true
    interval-minutes: 30
    page-size: 500
    batch-size: 50
    thread-pool-size: 3
    memory-threshold-percent: 75.0
    force-gc-threshold-percent: 80.0
    memory-check-interval: 5
```

### 生产环境（高性能）

```yaml
compress:
  task:
    enabled: true
    interval-minutes: 15
    page-size: 1000
    batch-size: 100
    thread-pool-size: 5
    memory-threshold-percent: 85.0
    force-gc-threshold-percent: 90.0
    memory-check-interval: 10
```

## 故障排查

### 1. 配置未生效

**问题**：配置修改后没有生效

**解决方案**：
- 检查 Profile 是否正确激活
- 查看启动日志中的配置加载信息
- 确认配置文件路径和格式正确

### 2. 内存优化效果不明显

**问题**：启用内存优化后仍然出现内存问题

**解决方案**：
- 降低 `page-size` 和 `batch-size`
- 降低 `memory-threshold-percent`
- 增加 JVM 堆内存大小
- 检查是否有内存泄漏

### 3. 性能下降

**问题**：启用内存优化后处理速度变慢

**解决方案**：
- 适当增加 `page-size` 和 `batch-size`
- 增加 `thread-pool-size`
- 调整 `memory-check-interval`

## 监控和调优

### 1. 内存监控

```java
// 获取详细内存报告
String report = MemoryMonitor.getDetailedMemoryReport();
log.info(report);

// 检查内存使用率
MemoryMonitor.MemoryInfo info = MemoryMonitor.getCurrentMemoryInfo();
if (info.getUsagePercent() > 80) {
    log.warn("内存使用率过高: {}%", info.getUsagePercent());
}
```

### 2. 性能调优建议

1. **根据实际数据量调整分页大小**
2. **根据服务器内存调整批处理大小**
3. **根据CPU核数调整线程池大小**
4. **根据业务需求调整执行间隔**
5. **定期检查内存错误统计**

### 3. 告警设置

建议设置以下告警：
- 内存使用率超过85%
- 内存错误数量增长
- 压缩任务执行失败率超过10%
- 压缩任务执行时间过长
