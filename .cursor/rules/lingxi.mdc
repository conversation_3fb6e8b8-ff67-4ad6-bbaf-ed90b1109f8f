---
description: 
globs: *.java
alwaysApply: false
---
1. 项目架构规范
严格遵循现有微服务架构：gateway、common、user、membership-service、agent、chat-service、notify
每个微服务必须独立部署，通过Nacos实现服务注册与发现
公共代码必须存放在common模块，禁止跨模块代码复制
各服务间通过OpenFeign进行通信，服务调用接口必须定义在client包中
2. 包结构规范
包命名必须遵循com.gw.{模块名}模式
模块内部必须按照功能划分子包：controller、service、repository、entity、dto、vo、config、constant、exception、client、event
DTO对象存放在dto包，用于服务间数据传输
VO对象存放在vo包，用于前端展示
实体类存放在entity包，对应数据库表结构
3. 异常处理规范
业务异常必须继承BusinessException
异常码必须在BusinessExceptionCode中统一定义
全局异常处理通过GlobalExceptionHandler统一处理，禁止使用try-catch直接返回
异常信息必须包含详细上下文：用户ID、操作类型、失败原因
开发环境记录完整堆栈，生产环境脱敏处理
5. RESTful API设计规范
API路径必须遵循/api/v1/{模块}/{资源}格式
使用HTTP方法表达操作类型：GET(查询)、POST(创建)
查询接口必须支持分页，使用PageHelper实现
响应格式统一：{code, message, data}
API文档必须使用Springdoc注解完整描述
6. 数据库操作规范
实体类必须使用Lombok注解减少冗余代码
必须使用MyBatis-Plus进行数据库操作，禁止手写SQL
数据库表必须包含id、create_time、update_time字段
删除操作优先使用逻辑删除（软删除）

大批量数据操作必须使用分批处理，避免OOM
7. 忽略所有Lombok的问题，因为是用idea产生的