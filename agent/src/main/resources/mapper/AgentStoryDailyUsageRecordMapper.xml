<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gw.agent.mapper.sql.AgentStoryDailyUsageRecordMapper">
    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.gw.agent.entity.AgentStoryDailyUsageRecordEntity">
        <id column="id" property="id"/>
        <result column="agent_id" property="agentId"/>
        <result column="username" property="username"/>
        <result column="use_date" property="useDate"/>
        <result column="use_count" property="useCount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="status" property="status"/>
    </resultMap>

    <!-- 分页查询 -->
    <select id="page" resultMap="BaseResultMap">
        SELECT * FROM t_agent_daily_usage_record
        <where>
            deleted = 0
            <if test="query.agentId != null">
                AND agent_id = #{query.agentId}
            </if>
            <if test="query.username != null and query.username != ''">
                AND username = #{query.username}
            </if>
            <if test="query.startDate != null and query.endDate != null">
                AND use_date BETWEEN #{query.startDate} AND #{query.endDate}
            </if>
            <choose>
                <when test="orderField.field == 'useDate'">
                    ORDER BY use_date ${orderField.order}
                </when>
                <otherwise>
                    ORDER BY use_date DESC
                </otherwise>
            </choose>
        </where>
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 根据ID列表查询 -->
    <select id="selectBatchIds" resultMap="BaseResultMap">
        SELECT * FROM t_agent_daily_usage_record
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted = 0
    </select>
</mapper>