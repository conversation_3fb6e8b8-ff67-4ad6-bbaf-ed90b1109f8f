<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gw.agent.mapper.sql.AgentSearchMapper">
    <select id="searchAgents" resultType="com.gw.agent.entity.AgentSearchEntity">
        SELECT DISTINCT a.id,
        a.name as keyword,
        a.popularity
        FROM t_agent a
        WHERE a.deleted = 0
        AND (
        a.name LIKE CONCAT('%', #{keyword}, '%')
        )
        <if test="isPublic != null and isPublic > 0">
            AND is_public = #{isPublic}
        </if>
        <if test="status != null and status > 0">
            AND status = #{status}
        </if>
        ORDER BY a.popularity DESC
        LIMIT #{limit}
    </select>
</mapper> 