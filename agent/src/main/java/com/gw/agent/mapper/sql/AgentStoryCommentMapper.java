package com.gw.agent.mapper.sql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.agent.entity.AgentCountEntity;
import com.gw.agent.entity.AgentStoryCommentEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 智能体剧情评论数据访问接口
 */
@Mapper
public interface AgentStoryCommentMapper extends BaseMapper<AgentStoryCommentEntity> {

    /**
     * 根据剧情ID查询评论列表（只查询顶级评论）
     *
     * @param storyId 剧情ID
     * @return 评论列表
     */
    @Select("SELECT * FROM t_agent_story_comment WHERE story_id = #{storyId} AND parent_id IS NULL AND status = 1 AND deleted = 0 ORDER BY create_time DESC")
    List<AgentStoryCommentEntity> findByStoryId(@Param("storyId") Long storyId);

    @Select("SELECT * FROM t_agent_story_comment WHERE id = #{id} AND status = 1 AND deleted = 0")
    Optional<AgentStoryCommentEntity> findById(@Param("id") Long id);

    /**
     * 批量根据ID查询评论
     *
     * @param ids 评论ID列表
     * @return 评论列表
     */
    @Select("<script>" +
            "SELECT * FROM t_agent_story_comment WHERE id IN " +
            "<foreach item='id' collection='ids' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "AND status = 1 AND deleted = 0" +
            "</script>")
    List<AgentStoryCommentEntity> findByIds(@Param("ids") List<Long> ids);

    /**
     * 根据父评论ID查询回复列表
     *
     * @param parentId 父评论ID
     * @return 回复列表
     */
    @Select("SELECT * FROM t_agent_story_comment WHERE parent_id = #{parentId} AND status = 1 AND deleted = 0 ORDER BY create_time ASC")
    List<AgentStoryCommentEntity> findRepliesByParentId(@Param("parentId") Long parentId);

    /**
     * 根据用户名查询评论列表
     *
     * @param username 用户名
     * @return 评论列表
     */
    @Select("SELECT * FROM t_agent_story_comment WHERE username = #{username} AND status = 1 AND deleted = 0 ORDER BY create_time DESC")
    List<AgentStoryCommentEntity> findByUsername(@Param("username") String username);

    @Select("SELECT DISTINCT story_id FROM t_agent_story_comment WHERE username = #{username} AND status = 1 AND deleted = 0")
    List<Long> findStoryIdsCommentedByUser(@Param("username") String username);

    /**
     * 统计评论的回复数量
     *
     * @param parentId 父评论ID
     * @return 回复数量
     */
    @Select("SELECT COUNT(*) FROM t_agent_story_comment WHERE parent_id = #{parentId} AND status = 1 AND deleted = 0")
    int countRepliesByParentId(@Param("parentId") Long parentId);

    /**
     * 批量统计多个评论的回复数量
     *
     * @param parentIds 父评论ID列表
     * @return 父评论ID和回复数量的映射
     */
    @Select("<script>" +
            "SELECT parent_id, COUNT(*) as reply_count " +
            "FROM t_agent_story_comment " +
            "WHERE parent_id IN " +
            "<foreach item='id' collection='parentIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "AND status = 1 AND deleted = 0 " +
            "GROUP BY parent_id" +
            "</script>")
    List<Map<String, Object>> batchCountRepliesByParentIds(@Param("parentIds") List<Long> parentIds);

    /**
     * 根据评论ID查询被回复的用户名
     *
     * @param commentId 评论ID
     * @return 被回复的用户名
     */
    @Select("SELECT username FROM t_agent_story_comment WHERE id = #{commentId} AND status = 1 AND deleted = 0")
    Optional<String> findUsernameByCommentId(@Param("commentId") Long commentId);

    /**
     * 更新评论状态
     *
     * @param id     评论ID
     * @param status 状态：1-正常，0-隐藏
     * @return 影响行数
     */
    @Update("UPDATE t_agent_story_comment SET status = #{status}, update_time = NOW() WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 统计剧情的评论数
     *
     * @param storyId 剧情ID
     * @return 评论数
     */
    @Select("SELECT COUNT(*) FROM t_agent_story_comment WHERE story_id = #{storyId} AND status = 1 AND deleted = 0")
    int countByStoryId(@Param("storyId") Long storyId);

    @Select("SELECT COUNT(*) FROM t_agent_story_comment WHERE username = #{username} AND status = 1 AND deleted = 0")
    int countByUsername(@Param("username") String username);

    @Select("SELECT COUNT(1) FROM t_agent_story_comment WHERE username = #{username} AND status = 1 AND deleted = 0")
    int countByUsernameAndDistinctStory(@Param("username") String username);

    /**
     * 批量统计多个剧情的评论数
     *
     * @param storyIds 剧情ID列表
     * @return 剧情ID到评论数的映射
     */
    @Select("<script>SELECT story_id as agent_id, COUNT(*) as count FROM t_agent_story_comment " +
            "WHERE story_id IN " +
            "<foreach collection='storyIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            " AND status = 1 AND deleted = 0 GROUP BY story_id</script>")
    List<AgentCountEntity> batchCountByStoryIds(@Param("storyIds") List<Long> storyIds);

    /**
     * 分页查询剧情评论列表
     *
     * @param storyId 剧情ID
     * @param parentId 父评论ID，为null时查询顶级评论，不为null时查询指定父评论的回复
     * @return 评论列表
     */
    @Select("<script>" +
            "SELECT * FROM t_agent_story_comment WHERE story_id = #{storyId} " +
            "<choose>" +
            "<when test='parentId == null'>" +
            "AND parent_id IS NULL " +
            "</when>" +
            "<otherwise>" +
            "AND parent_id = #{parentId} " +
            "</otherwise>" +
            "</choose>" +
            "AND status = 1 AND deleted = 0 ORDER BY create_time DESC" +
            "</script>")
    List<AgentStoryCommentEntity> pageStoryComments(@Param("storyId") Long storyId, @Param("parentId") Long parentId);

    /**
     * 后台分页查询剧情评论列表（支持多条件查询）
     *
     * @param storyIds 剧情ID列表
     * @param parentId 父评论ID，为null时查询顶级评论，不为null时查询指定父评论的回复
     * @param status 评论状态
     * @param username 用户名
     * @return 评论列表
     */
    @Select("<script>" +
            "SELECT * FROM t_agent_story_comment WHERE 1=1 " +
            "<if test='storyIds != null and storyIds.size() > 0'>" +
            "AND story_id IN " +
            "<foreach collection='storyIds' item='storyId' open='(' separator=',' close=')'>" +
            "#{storyId}" +
            "</foreach> " +
            "</if>" +
            "<choose>" +
            "<when test='parentId == null'>" +
            "AND parent_id IS NULL " +
            "</when>" +
            "<otherwise>" +
            "AND parent_id = #{parentId} " +
            "</otherwise>" +
            "</choose>" +
            "<if test='status != null'>" +
            "AND status = #{status} " +
            "</if>" +
            "<if test='username != null and username != \"\"'>" +
            "AND username = #{username} " +
            "</if>" +
            "AND deleted = 0 ORDER BY create_time DESC" +
            "</script>")
    List<AgentStoryCommentEntity> pageStoryCommentsAdmin(@Param("storyIds") List<Long> storyIds,
                                                         @Param("parentId") Long parentId,
                                                         @Param("status") Integer status,
                                                         @Param("username") String username);

    /**
     * 更新评论的子评论数量
     *
     * @param commentId 评论ID
     * @param increment 增量（可以是负数）
     * @return 影响行数
     */
    @Update("UPDATE t_agent_story_comment SET child_cnt = child_cnt + #{increment}, update_time = NOW() WHERE id = #{commentId}")
    int updateChildCount(@Param("commentId") Long commentId, @Param("increment") Integer increment);

    /**
     * 更新评论的点赞数量
     *
     * @param commentId 评论ID
     * @param increment 增量（可以是负数）
     * @return 影响行数
     */
    @Update("UPDATE t_agent_story_comment SET like_cnt = GREATEST(0, like_cnt + #{increment}), update_time = NOW() WHERE id = #{commentId}")
    int updateLikeCount(@Param("commentId") Long commentId, @Param("increment") Integer increment);

    /**
     * 更新评论的最新子评论ID
     *
     * @param parentId 父评论ID
     * @param lastChildId 最新子评论ID
     * @return 影响行数
     */
    @Update("UPDATE t_agent_story_comment SET last_child_id = #{lastChildId}, update_time = NOW() WHERE id = #{parentId}")
    int updateLastChildId(@Param("parentId") Long parentId, @Param("lastChildId") Long lastChildId);

    /**
     * 重新计算并更新评论的最新子评论ID
     *
     * @param parentId 父评论ID
     * @return 影响行数
     */
    @Update("UPDATE t_agent_story_comment SET last_child_id = (" +
            "SELECT MAX(id) FROM t_agent_story_comment child " +
            "WHERE child.parent_id = #{parentId} AND child.status = 1 AND child.deleted = 0" +
            "), update_time = NOW() WHERE id = #{parentId}")
    int recalculateLastChildId(@Param("parentId") Long parentId);
}