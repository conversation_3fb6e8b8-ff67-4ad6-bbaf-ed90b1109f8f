package com.gw.agent.mapper.sql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.agent.entity.ImageCheckProcessEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface ImageCheckProcessMapper extends BaseMapper<ImageCheckProcessEntity> {
    @Select("SELECT * FROM image_check_process WHERE trace_id = #{traceId}")
    ImageCheckProcessEntity findFirstByTraceId(String traceId);
}
