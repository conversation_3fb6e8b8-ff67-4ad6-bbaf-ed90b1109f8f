package com.gw.agent.mapper;

import com.gw.agent.dto.AgentSubmitDTO;
import com.gw.agent.entity.AgentEntity;
import com.gw.agent.entity.AgentStoryEntity;
import com.gw.agent.entity.AgentStorySceneEntity;
import com.gw.agent.vo.AgentDetailVO;
import com.gw.agent.vo.AgentSceneVO;
import com.gw.agent.vo.AgentStoryDetailVO;
import com.gw.common.agent.vo.AgentBaseVO;
import com.gw.common.agent.vo.AgentStoryBaseVO;
import com.gw.common.agent.vo.AgentStorySceneBaseVO;
import org.modelmapper.ModelMapper;
import org.modelmapper.config.Configuration;
import org.modelmapper.convention.MatchingStrategies;

public class ModelMapperConvert {

    // 单例模式 - 所有ModelMapper实例只创建一次
    private static final ModelMapper AGENT_MODEL_MAPPER = createAgentModelMapper();
    private static final ModelMapper AGENT_STORY_MODEL_MAPPER = createAgentStoryModelMapper();
    private static final ModelMapper AGENT_BASE_STORY_MODEL_MAPPER = createAgentBaseStoryModelMapper();
    private static final ModelMapper STORY_BASE_SCENE_MODEL_MAPPER = createStoryBaseSceneModelMapper();
    private static final ModelMapper STORY_SCENE_MODEL_MAPPER = createStorySceneModelMapper();
    private static final ModelMapper AGENT_BASE_MODEL_MAPPER = createAgentBaseModelMapper();
    private static final ModelMapper BASE_MODEL_MAPPER = new ModelMapper();
    private static final ModelMapper AGENT_SUBMIT_MODEL_MAPPER = createAgentSubmitModelMapper();

    // 私有创建方法 - 只在类加载时调用一次
    private static ModelMapper createAgentModelMapper() {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration()
                .setMatchingStrategy(MatchingStrategies.STRICT)
                .setPropertyCondition(context -> !(
                        context.getSource() instanceof AgentEntity &&
                                (context.getDestination() instanceof AgentDetailVO)
                ));
        modelMapper.createTypeMap(AgentEntity.class, AgentDetailVO.class)
                .addMapping(AgentEntity::getId, AgentDetailVO::setId)
                .addMappings(mapper -> mapper.map(AgentEntity::getTypeId, AgentDetailVO::setTypeId));
        return modelMapper;
    }

    private static ModelMapper createAgentStoryModelMapper() {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration()
                .setMatchingStrategy(MatchingStrategies.STRICT)
                .setPropertyCondition(context -> !(
                        context.getSource() instanceof AgentStoryEntity &&
                                (context.getDestination() instanceof AgentStoryDetailVO)
                ));
        modelMapper.createTypeMap(AgentStoryEntity.class, AgentStoryDetailVO.class)
                .addMapping(AgentStoryEntity::getId, AgentStoryDetailVO::setId)
                .addMappings(mapper -> mapper.map(AgentStoryEntity::getStoryTypeId, AgentStoryDetailVO::setStoryTypeId));
        return modelMapper;
    }

    private static ModelMapper createAgentBaseStoryModelMapper() {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration()
                .setMatchingStrategy(MatchingStrategies.STRICT)
                .setPropertyCondition(context -> !(
                        context.getSource() instanceof AgentStoryEntity &&
                                (context.getDestination() instanceof AgentStoryBaseVO)
                ));
        modelMapper.createTypeMap(AgentStoryEntity.class, AgentStoryBaseVO.class)
                .addMapping(AgentStoryEntity::getId, AgentStoryBaseVO::setId)
                .addMappings(mapper -> mapper.map(AgentStoryEntity::getStoryTypeId, AgentStoryBaseVO::setStoryTypeId));
        return modelMapper;
    }

    private static ModelMapper createStoryBaseSceneModelMapper() {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration()
                .setMatchingStrategy(MatchingStrategies.STRICT)
                .setPropertyCondition(context -> !(
                        context.getSource() instanceof AgentStorySceneEntity &&
                                (context.getDestination() instanceof AgentStorySceneBaseVO)
                ));
        modelMapper.createTypeMap(AgentStorySceneEntity.class, AgentStorySceneBaseVO.class)
                .addMapping(AgentStorySceneEntity::getId, AgentStorySceneBaseVO::setId);
        return modelMapper;
    }

    private static ModelMapper createStorySceneModelMapper() {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration()
                .setMatchingStrategy(MatchingStrategies.STRICT)
                .setPropertyCondition(context -> !(
                        context.getSource() instanceof AgentStorySceneEntity &&
                                (context.getDestination() instanceof AgentSceneVO)
                ));
        modelMapper.createTypeMap(AgentStorySceneEntity.class, AgentSceneVO.class)
                .addMapping(AgentStorySceneEntity::getId, AgentSceneVO::setId);
        return modelMapper;
    }

    private static ModelMapper createAgentBaseModelMapper() {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration()
                .setMatchingStrategy(MatchingStrategies.STRICT)
                .setPropertyCondition(context -> !(
                        context.getSource() instanceof AgentEntity &&
                                (context.getDestination() instanceof AgentBaseVO)
                ));
        modelMapper.createTypeMap(AgentEntity.class, AgentBaseVO.class)
                .addMapping(AgentEntity::getId, AgentBaseVO::setId)
                .addMappings(mapper -> mapper.map(AgentEntity::getTypeId, AgentBaseVO::setTypeId));
        return modelMapper;
    }

    /**
     * 创建专门用于AgentSubmitDTO到AgentEntity映射的ModelMapper
     * 解决typeId和draftId字段与BaseEntity.setId()方法冲突的问题
     */
    private static ModelMapper createAgentSubmitModelMapper() {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration()
                .setMatchingStrategy(MatchingStrategies.STRICT)
                .setFieldMatchingEnabled(true)
                .setFieldAccessLevel(Configuration.AccessLevel.PRIVATE)
                .setSkipNullEnabled(true)
                .setAmbiguityIgnored(true);

        // 创建明确的类型映射，避免字段名冲突
        modelMapper.createTypeMap(com.gw.agent.dto.AgentSubmitDTO.class, com.gw.agent.entity.AgentEntity.class)
                .addMappings(mapper -> {
                    // 跳过id字段的自动映射，避免typeId和draftId与setId()冲突
                    mapper.skip(AgentEntity::setId);
                    // 明确映射typeId到typeId字段
                    mapper.map(AgentSubmitDTO::getTypeId, AgentEntity::setTypeId);
                    // draftId字段会被自动忽略，因为AgentEntity中没有对应的setter方法
                });

        return modelMapper;
    }

    // 公共方法 - 返回单例实例
    public static ModelMapper getAgentModelMapper() {
        return AGENT_MODEL_MAPPER;
    }

    public static ModelMapper getBaseModelMapper() {
        return BASE_MODEL_MAPPER;
    }

    public static ModelMapper getAgentStoryModelMapper() {
        return AGENT_STORY_MODEL_MAPPER;
    }

    public static ModelMapper getAgentBaseStoryModelMapper() {
        return AGENT_BASE_STORY_MODEL_MAPPER;
    }

    public static ModelMapper getStoryBaseSceneModelMapper() {
        return STORY_BASE_SCENE_MODEL_MAPPER;
    }

    public static ModelMapper getStorySceneModelMapper() {
        return STORY_SCENE_MODEL_MAPPER;
    }

    public static ModelMapper getAgentBaseModelMapper() {
        return AGENT_BASE_MODEL_MAPPER;
    }

    public static ModelMapper getAgentSubmitModelMapper() {
        return AGENT_SUBMIT_MODEL_MAPPER;
    }

}