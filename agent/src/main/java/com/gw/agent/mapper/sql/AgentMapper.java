package com.gw.agent.mapper.sql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.agent.dto.AgentQueryDTO;
import com.gw.agent.dto.MyCommentAgentQueryDTO;
import com.gw.agent.dto.MyFavoriteAgentQueryDTO;
import com.gw.agent.dto.MyLikeAgentQueryDTO;
import com.gw.agent.entity.AgentEntity;
import com.gw.agent.entity.AgentStatusEntity;
import com.gw.agent.vo.AgentCacheVO;
import com.gw.agent.vo.AgentTypeStatisticsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 智能体数据访问接口
 */
@Mapper
public interface AgentMapper extends BaseMapper<AgentEntity> {

    // ======== Select Methods ========

    @Select("SELECT * FROM t_agent WHERE deleted = 0 ORDER BY create_time DESC")
    List<AgentEntity> findAll();

    @Select("SELECT id,name,type_id,gender,update_time,popularity,recommend_idx FROM t_agent WHERE deleted = 0 AND t_agent.is_public = 2 AND status = 256 AND t_agent.shelf_status = 1 ORDER BY create_time DESC")
    List<AgentCacheVO> findAllByPublic();

    @Select("SELECT * FROM t_agent WHERE creator = #{username} AND deleted = 0 ORDER BY create_time DESC")
    List<AgentEntity> findByUsername(@Param("username") String username);

    Optional<AgentEntity> findById(@Param("agentId") Long agentId);

    Optional<AgentEntity> findFirstByExclusive();

    @Select("SELECT id FROM t_agent WHERE is_public = 3 AND deleted = 0 ORDER BY create_time DESC LIMIT 1")
    Long findFirstIdByExclusive();

    @Select("SELECT COUNT(1) FROM t_agent WHERE creator = #{creator} AND deleted = 0")
    int countByCreator(@Param("creator") String creator);

    @Select("SELECT * FROM t_agent WHERE name = #{name} AND creator = #{creator} AND deleted = 0")
    Optional<AgentEntity> findByNameAndCreator(String name, String creator);

    @Select("SELECT id FROM t_agent WHERE name LIKE CONCAT('%', #{agentName}, '%') AND deleted = 0")
    List<Long> findAgentIdsByNameLike(@Param("agentName") String agentName);

    List<AgentEntity> page(@Param("query") AgentQueryDTO query, @Param("orderField") com.gw.common.dto.PageOrderField orderField);

    List<AgentEntity> selectBatchIds(List<Long> agentIds);

    List<Long> pageIds(@Param("query") AgentQueryDTO query, @Param("orderField") com.gw.common.dto.PageOrderField orderField);

    List<AgentEntity> findByIdsWithTagCount(@Param("agentIds") List<Long> agentIds, @Param("tagIds") List<Long> tagIds);

    List<AgentEntity> findAllByIds(@Param("ids") List<Long> ids);

    List<AgentEntity> findAllByIdsNotSort(@Param("ids") List<Long> ids);

    List<Long> findValidPublicIdsOrderByPopularityAndUpdate(@Param("limit") int limit);

    List<AgentEntity> pageLikedByUsername(MyLikeAgentQueryDTO query);

    List<AgentEntity> pageFavoriteByUsername(MyFavoriteAgentQueryDTO query);

    List<AgentEntity> pageCommentByUsername(MyCommentAgentQueryDTO query);

    List<AgentEntity> pageFavoriteByUsername(@Param("username") String username);

    List<AgentEntity> pageCommentByUsername(@Param("username") String username);
    List<AgentStatusEntity> findAllStatusByAgentIds(@Param("agentIds")List< Long> agentIds);
    @Select("SELECT COUNT(*) FROM t_agent WHERE create_time BETWEEN #{startTime} AND #{endTime} AND deleted = 0")
    int countNewAgentsByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    @Select("SELECT COUNT(*) FROM t_agent WHERE deleted = 0")
    int countTotalAgents();

    @Select("SELECT t.id as typeId, t.name, t.description, COUNT(a.id) as count " +
            "FROM t_agent_type t " +
            "LEFT JOIN t_agent a ON t.id = a.type_id AND a.deleted = 0 " +
            "GROUP BY t.id, t.name, t.description")
    List<AgentTypeStatisticsVO> countAgentsByType();

    // ======== Update Methods ========

    @Update("UPDATE t_agent SET popularity = popularity + #{increment} WHERE id = #{agentId}")
    void updatePopularity(@Param("agentId") Long agentId, @Param("increment") Integer increment);

    @Update("UPDATE t_agent SET status = #{status} WHERE id = #{agentId}")
    void updateStatus(@Param("agentId") Long agentId, @Param("status") Integer status);

    @Update("UPDATE t_agent SET status = #{status}, remote_bot_id = #{remoteBotId} WHERE id = #{agentId}")
    void updateStatusAndBotID(@Param("agentId") Long agentId, @Param("status") Integer status,
                              @Param("remoteBotId") String remoteBotId);

    @Update("UPDATE t_agent SET bg_url = #{bgUrl}, bg_thumbnail_url = null WHERE id = #{agentId}")
    void updateBgUrlAndThumbBgUrlNull(@Param("agentId") Long agentId, @Param("bgUrl") String bgUrl);

    @Update("UPDATE t_agent SET avatar_url = #{avatarUrl} WHERE id = #{agentId}")
    void updateAvatarUrl(@Param("agentId") Long agentId, @Param("avatarUrl") String avatarUrl);

    @Update("UPDATE t_agent SET bg_thumbnail_url = #{bgThumbnailUrl} WHERE id = #{agentId}")
    void updateBgThumbnailUrl(@Param("agentId") Long agentId, @Param("bgThumbnailUrl") String bgThumbnailUrl);

    // ======== Delete Methods ========

    @Update("UPDATE t_agent SET deleted = 1, update_time = #{updateTime}, updater = #{updater} WHERE id = #{agentId}")
    void updateDeleted(@Param("agentId") Long agentId, @Param("updateTime") LocalDateTime updateTime,
                       @Param("updater") String updater);

    @Update("UPDATE t_agent SET security_check_result = #{securityCheckResult} WHERE id = #{id}")
    void updateSecurityCheckResultById(@Param("id") Long id, @Param("securityCheckResult") String securityCheckResult);

    @Update("UPDATE t_agent SET recommend_idx = #{recommendIdx} WHERE id = #{id}")
    void updateRecommendIdxById(@Param("id") Long id, @Param("recommendIdx") Integer recommendIdx);
}