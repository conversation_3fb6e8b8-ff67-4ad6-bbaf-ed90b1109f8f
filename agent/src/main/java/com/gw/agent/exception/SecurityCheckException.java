package com.gw.agent.exception;

import lombok.Getter;

/**
 * 安全检查异常类
 * 提供统一的安全检查错误处理机制
 */
@Getter
public class SecurityCheckException extends RuntimeException {

    /**
     * 错误代码
     */
    private final String errorCode;

    /**
     * 检查类型（如：角色名称、角色简介、形象图片等）
     */
    private final String checkType;

    /**
     * 智能体ID
     */
    private final Long businessId;

    /**
     * 是否可重试
     */
    private final boolean retryable;

    /**
     * 审核平台（如：微信、腾讯云等）
     */
    private final String platform;

    public SecurityCheckException(String checkType, String message) {
        super(message);
        this.errorCode = "SECURITY_CHECK_FAILED";
        this.checkType = checkType;
        this.businessId = null;
        this.retryable = false;
        this.platform = "UNKNOWN";
    }

    public SecurityCheckException(String errorCode, String checkType, String message, Long businessId) {
        super(message);
        this.errorCode = errorCode;
        this.checkType = checkType;
        this.businessId = businessId;
        this.retryable = false;
        this.platform = "UNKNOWN";
    }

    public SecurityCheckException(String errorCode, String checkType, String message, Long businessId,
                                  boolean retryable, String platform) {
        super(message);
        this.errorCode = errorCode;
        this.checkType = checkType;
        this.businessId = businessId;
        this.retryable = retryable;
        this.platform = platform;
    }

    public SecurityCheckException(String errorCode, String checkType, String message, Long businessId,
                                  boolean retryable, String platform, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.checkType = checkType;
        this.businessId = businessId;
        this.retryable = retryable;
        this.platform = platform;
    }

    /**
     * 微信安全检查异常
     */
    public static class WechatSecurityCheckException extends SecurityCheckException {
        public WechatSecurityCheckException(String checkType, String message, Long agentId) {
            super("WECHAT_SECURITY_CHECK_FAILED", checkType, message, agentId, true, "WECHAT");
        }

        public WechatSecurityCheckException(String checkType, String message, Long agentId, Throwable cause) {
            super("WECHAT_SECURITY_CHECK_FAILED", checkType, message, agentId, true, "WECHAT", cause);
        }
    }

    /**
     * 腾讯云安全检查异常
     */
    public static class TencentCloudSecurityCheckException extends SecurityCheckException {
        public TencentCloudSecurityCheckException(String checkType, String message, Long agentId) {
            super("TENCENT_CLOUD_SECURITY_CHECK_FAILED", checkType, message, agentId, true, "TENCENT_CLOUD");
        }

        public TencentCloudSecurityCheckException(String checkType, String message, Long agentId, Throwable cause) {
            super("TENCENT_CLOUD_SECURITY_CHECK_FAILED", checkType, message, agentId, true, "TENCENT_CLOUD", cause);
        }
    }

    /**
     * 安全检查配置异常
     */
    public static class SecurityCheckConfigException extends SecurityCheckException {
        public SecurityCheckConfigException(String message) {
            super("SECURITY_CHECK_CONFIG_ERROR", "配置检查", message, null, false, "SYSTEM");
        }

        public SecurityCheckConfigException(String message, Throwable cause) {
            super("SECURITY_CHECK_CONFIG_ERROR", "配置检查", message, null, false, "SYSTEM", cause);
        }
    }

    /**
     * 安全检查业务异常
     */
    public static class SecurityCheckBusinessException extends SecurityCheckException {
        public SecurityCheckBusinessException(String checkType, String message, Long agentId) {
            super("SECURITY_CHECK_BUSINESS_ERROR", checkType, message, agentId, false, "BUSINESS");
        }

        public SecurityCheckBusinessException(String checkType, String message, Long agentId, Throwable cause) {
            super("SECURITY_CHECK_BUSINESS_ERROR", checkType, message, agentId, false, "BUSINESS", cause);
        }
    }

    /**
     * 获取用户友好的错误信息
     */
    public String getUserFriendlyMessage() {
        return checkType + "内容不符合平台规范，请修改后重试";
    }
}
