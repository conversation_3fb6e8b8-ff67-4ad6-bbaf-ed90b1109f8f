package com.gw.agent.handler;


import com.github.pagehelper.PageInfo;
import com.gw.agent.constant.AgentRecommendationConstants;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 推荐服务缓存辅助类
 */
@Component
public class RecommendationCacheHelper {

    private final RedisTemplate<String, Object> redisTemplate;

    public RecommendationCacheHelper(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }


    /**
     * 生成分页缓存键
     */
    public String generatePageCacheKey(String username, int pageNum, int pageSize, Long firstAgentId) {
        return AgentRecommendationConstants.AGENT_RECOMMEND_PAGE_KEY_PREFIX +
                (username != null ? username : "anonymous") +
                ":" + pageNum +
                ":" + pageSize +
                ":" + (firstAgentId != null ? firstAgentId : 0);
    }

    /**
     * 从列表创建分页结果
     */
    public PageInfo<Long> createPageFromList(List<Long> list, int pageNum, int pageSize) {
        int start = (pageNum - 1) * pageSize;
        int end = Math.min(start + pageSize, list.size());

        if (start >= list.size()) {
            return new PageInfo<>(new ArrayList<>());
        }

        List<Long> pageData = list.subList(start, end);
        PageInfo<Long> pageInfo = new PageInfo<>(pageData);

        // 设置分页信息
        pageInfo.setTotal(list.size());
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setSize(pageData.size());
        pageInfo.setPages((int) Math.ceil((double) list.size() / pageSize));

        return pageInfo;
    }
}
