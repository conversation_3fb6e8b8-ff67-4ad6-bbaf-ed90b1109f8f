package com.gw.agent.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "智能体搜索结果")
public class AgentSearchResponseVO {
    @Schema(description = "搜索关键字")
    private String keyword;
//    @Schema(description = "智能体ID")
//    private Long id;
//    @Schema(description = "搜索关键字")
//    private String keyword;
//    @Schema(description = "智能体名称")
//    private String name;
//
//    @Schema(description = "智能体类型")
//    private String typeName;
//
//    @Schema(description = "智能体标签，多个标签用逗号分隔")
//    private String tags;
} 