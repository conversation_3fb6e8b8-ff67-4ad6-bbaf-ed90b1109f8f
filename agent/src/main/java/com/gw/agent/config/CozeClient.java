package com.gw.agent.config;

import com.coze.openapi.service.auth.TokenAuth;
import com.coze.openapi.service.service.CozeAPI;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class CozeClient {

    @Bean
    public CozeAPI cozeAPI() {
        String token = System.getenv("COZE_API_TOKEN");
        TokenAuth authCli = new TokenAuth(token);
        return new CozeAPI.Builder()
                .baseURL(System.getenv("COZE_API_BASE"))
                .auth(authCli)
                .readTimeout(100000)
                .connectTimeout(10000)
                .build();
    }
}
