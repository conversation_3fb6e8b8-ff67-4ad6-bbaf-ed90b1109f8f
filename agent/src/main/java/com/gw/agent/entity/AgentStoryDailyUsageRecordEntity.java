package com.gw.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.baomidou.mybatisplus.annotation.IdType.AUTO;

/**
 * 用户每日使用智能体记录实体
 */
@Data
@TableName("t_agent_story_daily_usage_record")
public class AgentStoryDailyUsageRecordEntity {
    /**
     * 主键ID
     */
    @TableId(type = AUTO)
    private Long id;

    /**
     * 智能体ID
     */
    private Long storyId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 使用日期
     */
    private LocalDate useDate;

    /**
     * 当日使用次数
     */
    private Integer useCount;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志：0-未删除，1-已删除
     */
    @TableLogic
    private Integer deleted = 0;

    /**
     * 状态：1-正常，0-禁用
     */
    private Integer status = 1;
} 