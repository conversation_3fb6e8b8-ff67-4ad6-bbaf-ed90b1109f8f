package com.gw.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

import static com.baomidou.mybatisplus.annotation.IdType.AUTO;

/**
 * 智能体评论实体
 */
@Data
@TableName("t_agent_comment")
public class AgentCommentEntity {
    @TableId(type = AUTO)
    private Long id;
    private Long agentId;
    private String username;
    private String content;
    /**
     * 父评论ID 顶级评论为null
     */
    private Long parentId;
    /**
     * 被回复的用户名（仅在回复评论时有值）
     */
    private String replyToUsername;
    /**
     * 评论状态 状态：1-正常，0-隐藏 默认是1
     */
    private Integer status;
    /**
     * 评论图片列表（JSON格式存储，最多9张）
     */
    private String images;
    /**
     * 评论消息列表（JSON格式存储）
     */
    private String messages;

    /**
     * 子评论数量（数据库字段，用于快速查询）
     */
    private Integer childCnt = 0;

    /**
     * 点赞数量（数据库字段，用于快速查询）
     */
    private Integer likeCnt = 0;

    /**
     * 最新子评论ID（数据库字段，用于快速查询）
     */
    private Long lastChildId;

    @TableLogic
    private Integer deleted = 0;
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime = LocalDateTime.now();
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime = LocalDateTime.now();

    /**
     * 用户昵称
     */
    @TableField(exist = false)
    private String nickname;

    /**
     * 用户头像
     */
    @TableField(exist = false)
    private String avatarUrl;

    /**
     * 回复列表
     */
    @TableField(exist = false)
    private List<AgentCommentEntity> replies;

    /**
     * 最新子评论
     */
    @TableField(exist = false)
    private AgentCommentEntity latestChild;

    /**
     * 评论点赞数
     */
    @TableField(exist = false)
    private Integer likeCount = 0;

    /**
     * 当前用户是否已点赞该评论（1-已点赞，0-未点赞）
     */
    @TableField(exist = false)
    private Integer isLiked = 0;

    /**
     * 子评论数量
     */
    @TableField(exist = false)
    private Integer replyCount = 0;

    /**
     * 图片列表（解析后的列表）
     */
    @TableField(exist = false)
    private List<String> imageList;

    /**
     * 消息列表（解析后的列表）
     */
    @TableField(exist = false)
    private List<String> messageList;
}