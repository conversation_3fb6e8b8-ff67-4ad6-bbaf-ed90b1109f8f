package com.gw.agent.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import static com.baomidou.mybatisplus.annotation.IdType.AUTO;

/**
 * 智能体实体
 */
@Data
public class AgentStatusEntity {
    @TableId(type = AUTO)
    private Long id;
    private String name;
    private Integer isPublic;
    private Integer status;
    private Integer shelfStatus = 1;
    private Integer deleted = 0;
}