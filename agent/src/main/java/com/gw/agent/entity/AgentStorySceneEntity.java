package com.gw.agent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gw.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 智能体剧情场景实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_agent_story_scene")
public class AgentStorySceneEntity extends BaseEntity {

    /**
     * 所属剧情ID
     */
    private Long storyId = 0L;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 场景简介
     */
    private String sceneDescription;

    /**
     * 场景顺序
     */
    private Integer sceneOrder;

    /**
     * 剧情封面图片URL
     */
    @TableField("bg_url")
    private String bgUrl;
    /**
     * 剧情封面缩略图图片URL
     */
    private String bgThumbnailUrl;
    /**
     * 剧情背景音乐URL
     */
    @TableField("bg_music_url")
    private String bgMusicUrl;
    /**
     * 场景氛围：温馨、紧张、神秘、浪漫等
     */
    private String atmosphere;

    /**
     * 天气设置：晴天、雨天、雪天等
     */
    private String weather;

    /**
     * 时间段：早晨、中午、黄昏、夜晚等
     */
    private String timePeriod;

    /**
     * 地点描述
     */
    private String location;

    /**
     * 场景开场白
     */
    private String openingText;
    private Long openingAgentId;
    /**
     * 场景脚本内容
     */
    private String sceneScript;

    /**
     * 场景状态：0-禁用，1-正常
     */
    private Integer status;

    /**
     * 热度值
     */
    private Integer popularity = 0;

    /**
     * 所属剧情 - 不存储在数据库中
     */
    @TableField(exist = false)
    private AgentStoryEntity story;

    /**
     * 场景中的智能体关联列表 - 不存储在数据库中
     */
    @TableField(exist = false)
    private List<SceneAgentRelationEntity> agentRelations;

    /**
     * 场景中的智能体列表 - 不存储在数据库中
     */
    @TableField(exist = false)
    private List<AgentEntity> agents;
} 