package com.gw.agent.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gw.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 敏感词实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_sensitive_word")
public class SensitiveWordEntity extends BaseEntity {
    
    /**
     * 敏感词内容
     */
    private String word;
    
    /**
     * 分类ID
     */
    private Long categoryId;
    
    /**
     * 敏感词级别：1-低级，2-中级，3-高级，4-严重
     */
    private Integer level;
    
    /**
     * 处理动作：1-拦截，2-替换，3-警告
     */
    private Integer action = 1;
    
    /**
     * 替换内容（当action为2时使用）
     */
    private String replacement;
    
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
    
    /**
     * 使用次数
     */
    private Integer useCount = 0;
}
