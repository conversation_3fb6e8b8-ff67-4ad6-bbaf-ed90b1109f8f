package com.gw.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@Schema(description = "用户喜好设置DTO")
public class UserPreferenceDTO {

    /**
     * 喜好的智能体性别：1-男，2-女，3-男女都可以
     */
    @Schema(description = "喜好的智能体性别：1-男，2-女，3-男女都可以")
    private Integer preferredGender;

    /**
     * 喜好的智能体类型，多选
     */
    @Schema(description = "喜好的智能体类型ID列表，多选")
    private List<Long> preferredAgentTypes = new ArrayList<>();
} 