package com.gw.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

@Data
public class AgentStoryShelfStatusDTO {
    @Schema(description = "群聊故事ID")
    @NotNull(message = "群聊故事ID不能为空")
    @Positive(message = "群聊故事ID必须大于0")
    private Long storyId;

    @Schema(description = "上架状态：1-上架，2-下架")
    @NotNull(message = "上架状态不能为空")
    @Min(value = 1, message = "上架状态只能是1或2")
    @Max(value = 2, message = "上架状态只能是1或2")
    private Integer status;
    private String shelfReason;
}
