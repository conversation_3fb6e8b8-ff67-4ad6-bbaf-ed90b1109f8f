package com.gw.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

@Data
@Schema(description = "智能体搜索请求")
public class SearchAgentRequestDTO {

    //    @NotBlank(message = "搜索关键词不能为空")
    @Schema(description = "搜索关键词", required = true)
    private String keyword;

    @Min(value = 1, message = "返回数量最小为1")
    @Max(value = 100, message = "返回数量最大为100")
    @Schema(description = "返回数量限制，默认为20", defaultValue = "20")
    private Integer limit = 20;
} 