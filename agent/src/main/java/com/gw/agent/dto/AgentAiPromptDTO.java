package com.gw.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.util.List;

@Data
public class AgentAiPromptDTO {
    private String name;
    @Schema(description = "智能体类型ID")
    @Positive(message = "智能体类型ID不能为空")
    private Long typeId;
    @Schema(description = "性别：1-男，2-女，3-其他")
    @Positive(message = "性别不能为空")
    private Integer gender;
    @Schema(description = "智能体身份")
    private String identity;
    @Schema(description = "简介")
    private String introduction;
    @Schema(description = "标签ID列表")
    private List<Long> tagIds;
}
