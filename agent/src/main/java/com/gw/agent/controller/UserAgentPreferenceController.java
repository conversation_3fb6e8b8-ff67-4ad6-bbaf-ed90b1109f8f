package com.gw.agent.controller;

import com.gw.agent.dto.UserPreferenceDTO;
import com.gw.agent.entity.AgentTypeEntity;
import com.gw.agent.entity.UserAgentPreferenceEntity;
import com.gw.agent.service.AgentTypeService;
import com.gw.agent.service.UserPreferenceService;
import com.gw.agent.vo.UserPreferenceVO;
import com.gw.common.dto.ResponseResult;
import com.gw.common.user.context.UserContextUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 用户喜好设置控制器
 */
@RestController
@RequestMapping("/api/v1/agent/preference")
@RequiredArgsConstructor
@Tag(name = "用户喜好设置", description = "用户喜好设置相关API")
@Log4j2
public class UserAgentPreferenceController {

    private final UserPreferenceService userPreferenceService;
    private final AgentTypeService agentTypeService;

    @PostMapping("/save")
    @Operation(summary = "保存用户喜好设置", description = "保存用户喜好设置")
    public ResponseResult<?> save(@RequestBody UserPreferenceDTO dto) {
        String username = UserContextUtil.getCurrentUsername();

        UserAgentPreferenceEntity entity = userPreferenceService.findByUsername(username);
        if (entity == null) {
            entity = new UserAgentPreferenceEntity();
            entity.setCreator(username);
        }
        entity.setUsername(username);
        // 设置用户喜好的性别
        entity.setPreferredGender(dto.getPreferredGender());

        // 设置用户喜好的智能体类型
        entity.setPreferredAgentTypes(dto.getPreferredAgentTypes());

        // 创建者和更新者设置为当前用户
        entity.setCreator(username);
        entity.setUpdater(username);

        if (entity.getId() == null) {
            userPreferenceService.savePreference(entity);
        } else {
            userPreferenceService.update(entity);
        }

        return ResponseResult.success(null);
    }

    @GetMapping("/get")
    @Operation(summary = "获取用户喜好设置", description = "获取用户喜好设置")
    public ResponseResult<UserPreferenceVO> get() {
        String username = UserContextUtil.getCurrentUsername();

        UserAgentPreferenceEntity entity = userPreferenceService.findByUsername(username);
        if (entity == null) {
            // 如果没有设置，返回默认值
            UserPreferenceVO defaultVO = new UserPreferenceVO();
            defaultVO.setPreferredGender(3); // 默认男女都可以
            return ResponseResult.success(defaultVO);
        }

        // 获取智能体类型信息
        List<AgentTypeEntity> typeEntities = new ArrayList<>();
        if (entity.getPreferredAgentTypes() != null && !entity.getPreferredAgentTypes().isEmpty()) {
            Map<Long, AgentTypeEntity> typesMap = agentTypeService.findTypesMap();
            for (Long typeId : entity.getPreferredAgentTypes()) {
                AgentTypeEntity typeEntity = typesMap.get(typeId);
                if (typeEntity != null) {
                    typeEntities.add(typeEntity);
                }
            }
        }

        UserPreferenceVO vo = new UserPreferenceVO(entity, typeEntities);
        return ResponseResult.success(vo);
    }
} 