package com.gw.agent.controller;

import com.gw.agent.constant.AgentTagConstant;
import com.gw.agent.dto.AgentTagSubmitDTO;
import com.gw.agent.dto.AgentTagUpdateDTO;
import com.gw.agent.entity.AgentTagEntity;
import com.gw.agent.service.AgentTagService;
import com.gw.agent.vo.AgentTagGroupCategoryVO;
import com.gw.agent.vo.AgentTagVO;
import com.gw.common.dto.ItemIdDTO;
import com.gw.common.dto.ResponseResult;
import com.gw.common.exception.BusinessExceptionCode;
import com.gw.common.user.context.UserContextUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 智能体标签控制器
 */
@RestController
@RequestMapping("/api/v1/agent/tags")
@RequiredArgsConstructor
@Tag(name = "智能体标签管理", description = "智能体标签相关API")
@Log4j2
public class AgentTagController {

    private final AgentTagService tagService;

    private void fillEntity(AgentTagEntity entity, AgentTagSubmitDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        BeanUtils.copyProperties(req, entity);
        entity.setType(AgentTagConstant.SYSTEM_TYPE_TAG);
        entity.setUpdater(username);
        entity.setUpdateTime(LocalDateTime.now());
    }

    /**
     * 创建标签
     */
    @Operation(summary = "创建标签", description = "创建新的智能体标签")
    @PostMapping
    public ResponseResult<?> createTag(@RequestBody @Valid AgentTagSubmitDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        boolean exist = tagService.isNameExistByCategory(req.getName(), req.getCategory());
        if (!exist) {
            return ResponseResult.failure(BusinessExceptionCode.FAIL_CODE.getCode(), "标签名称已存在");
        }
        AgentTagEntity entity = new AgentTagEntity();
        fillEntity(entity, req);
        entity.setCreator(username);
        entity.setCreateTime(LocalDateTime.now());
        tagService.insert(entity);
        return ResponseResult.success(null);
    }

    /**
     * 更新标签
     */
    @Operation(summary = "更新标签", description = "更新标签信息")
    @PostMapping("update")
    public ResponseResult<?> updateTag(@RequestBody @Valid AgentTagUpdateDTO req) {
        AgentTagEntity entity = tagService.findById(req.getId());
        if (!entity.getName().equals(req.getName())) {
            boolean exist = tagService.isNameExistByCategory(req.getName(), req.getCategory());
            if (!exist) {
                return ResponseResult.failure(BusinessExceptionCode.FAIL_CODE.getCode(), "标签名称已存在");
            }
        }
        fillEntity(entity, req);
        tagService.update(entity);
        return ResponseResult.success(null);
    }

    /**
     * 删除标签
     */
    @Operation(summary = "删除标签", description = "删除指定的标签")
    @PostMapping("/delete")
    public ResponseResult<?> deleteTag(@RequestBody ItemIdDTO req) {
        AgentTagEntity entity = tagService.findById(req.getId());
        String username = UserContextUtil.getCurrentUsername();
        entity.setUpdater(username);
        entity.setUpdateTime(LocalDateTime.now());
        tagService.delete(entity);
        return ResponseResult.success(null);
    }

    /**
     * 获取所有标签
     */
    @Operation(summary = "获取所有标签", description = "获取所有可用的标签")
    @GetMapping("list_all")
    public ResponseResult<List<AgentTagGroupCategoryVO>> getAllTags() {
        return ResponseResult.success(
                tagService.findAll().stream()
                        .map(AgentTagVO::new)
                        .collect(Collectors.groupingBy(AgentTagVO::getCategory))
                        .entrySet().stream()
                        .map(entry -> new AgentTagGroupCategoryVO(entry.getKey(), entry.getValue()))
                        .toList());
    }
}