package com.gw.agent.controller;

import com.gw.agent.dto.AgentTypeSubmitDTO;
import com.gw.agent.dto.AgentTypeUpdateDTO;
import com.gw.agent.entity.AgentTypeEntity;
import com.gw.agent.service.AgentTypeService;
import com.gw.agent.vo.AgentTypeVO;
import com.gw.common.dto.ItemIdDTO;
import com.gw.common.dto.ResponseResult;
import com.gw.common.exception.BusinessExceptionCode;
import com.gw.common.user.context.UserContextUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 智能体类型控制器
 */
@RestController
@RequestMapping("/api/v1/agent/types")
@RequiredArgsConstructor
@Tag(name = "智能体类型管理", description = "智能体类型相关API")
@Log4j2
public class AgentTypeController {

    private final AgentTypeService typeService;

    private void fillEntity(AgentTypeEntity entity, AgentTypeSubmitDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        BeanUtils.copyProperties(req, entity);

        entity.setUpdater(username);
        entity.setUpdateTime(LocalDateTime.now());
    }

    /**
     * 创建类型
     */
    @Operation(summary = "创建类型", description = "创建新的智能体类型")
    @PostMapping
    public ResponseResult<?> createTag(@RequestBody @Valid AgentTypeSubmitDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        boolean exist = typeService.isNameExist(req.getName());
        if (!exist) {
            return ResponseResult.failure(BusinessExceptionCode.FAIL_CODE.getCode(), "类型名称已存在");
        }
        AgentTypeEntity entity = new AgentTypeEntity();
        fillEntity(entity, req);
        entity.setCreator(username);
        entity.setCreateTime(LocalDateTime.now());
        typeService.insert(entity);
        return ResponseResult.success(null);
    }

    /**
     * 更新类型
     */
    @Operation(summary = "更新类型", description = "更新类型信息")
    @PostMapping("update")
    public ResponseResult<?> updateTag(@RequestBody @Valid AgentTypeUpdateDTO req) {
        AgentTypeEntity entity = typeService.findById(req.getId());
        if (!entity.getName().equals(req.getName())) {
            boolean exist = typeService.isNameExist(req.getName());
            if (!exist) {
                return ResponseResult.failure(BusinessExceptionCode.FAIL_CODE.getCode(), "类型名称已存在");
            }
        }
        fillEntity(entity, req);
        typeService.update(entity);
        return ResponseResult.success(null);
    }

    /**
     * 删除类型
     */
    @Operation(summary = "删除类型", description = "删除指定的类型")
    @PostMapping("/delete")
    public ResponseResult<?> deleteTag(@RequestBody ItemIdDTO req) {
        AgentTypeEntity entity = typeService.findById(req.getId());
        String username = UserContextUtil.getCurrentUsername();
        entity.setUpdater(username);
        entity.setUpdateTime(LocalDateTime.now());
        typeService.delete(entity);
        return ResponseResult.success(null);
    }

    /**
     * 获取所有类型
     */
    @Operation(summary = "获取所有类型", description = "获取所有可用的类型")
    @GetMapping("list_all")
    public ResponseResult<List<AgentTypeVO>> getAllTags() {
        return ResponseResult.success(
                typeService.findAll().stream().map(AgentTypeVO::new).collect(Collectors.toList()));
    }
}