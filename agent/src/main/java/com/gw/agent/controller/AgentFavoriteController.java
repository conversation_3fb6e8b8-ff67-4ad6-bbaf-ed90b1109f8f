package com.gw.agent.controller;

import com.gw.agent.dto.AgentFavoriteDTO;
import com.gw.agent.entity.AgentEntity;
import com.gw.agent.entity.AgentFavoriteEntity;
import com.gw.agent.mapper.ModelMapperConvert;
import com.gw.agent.service.AgentFavoriteService;
import com.gw.agent.service.AgentService;
import com.gw.agent.vo.AgentDetailVO;
import com.gw.common.dto.ResponseResult;
import com.gw.common.exception.BusinessExceptionCode;
import com.gw.common.user.context.UserContextUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 智能体收藏控制器
 */
@RestController
@RequestMapping("/api/v1/agent/favorites")
@RequiredArgsConstructor
@Tag(name = "智能体收藏", description = "智能体收藏相关API")
@Log4j2
public class AgentFavoriteController {

    private final AgentFavoriteService agentFavoriteService;
    private final AgentService agentService;

    /**
     * 将实体转换为VO对象
     */
    private AgentDetailVO convertToVO(AgentEntity entity) {
        AgentDetailVO vo = new AgentDetailVO();
        ModelMapperConvert.getAgentModelMapper().map(entity, vo);
        return vo;
    }

    /**
     * 添加收藏
     */
    @Operation(summary = "添加收藏", description = "收藏一个智能体")
    @PostMapping("/add")
    public ResponseResult<?> addFavorite(@RequestBody @Valid AgentFavoriteDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        // 检查智能体是否存在
        AgentEntity agent = agentService.findById(req.getAgentId());
        if (agent == null) {
            return ResponseResult.failure(BusinessExceptionCode.FAIL_CODE.getCode(), "智能体不存在");
        }
        agentFavoriteService.addFavorite(req.getAgentId(), username);
        return ResponseResult.success(null);
    }

    /**
     * 取消收藏
     */
    @Operation(summary = "取消收藏", description = "取消收藏一个智能体")
    @PostMapping("/remove")
    public ResponseResult<?> removeFavorite(@RequestBody @Valid AgentFavoriteDTO req) {
        String username = UserContextUtil.getCurrentUsername();

        boolean result = agentFavoriteService.removeFavorite(req.getAgentId(), username);
        return ResponseResult.success(null);
    }

    /**
     * 获取用户收藏的智能体列表
     */
    @Operation(summary = "获取用户收藏的智能体列表", description = "获取当前用户收藏的所有智能体")
    @GetMapping("/my")
    public ResponseResult<List<AgentDetailVO>> getMyFavorites() {
        String username = UserContextUtil.getCurrentUsername();

        List<AgentFavoriteEntity> favorites = agentFavoriteService.getUserFavorites(username);
        List<Long> agentIds = favorites.stream()
                .map(AgentFavoriteEntity::getAgentId)
                .collect(Collectors.toList());

        List<AgentEntity> agents = agentService.findByIdsAndUsername(agentIds, username);

        // 设置收藏状态
        agents.forEach(agent -> agent.setIsFavorite(1));

        List<AgentDetailVO> vos = agents.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return ResponseResult.success(vos);
    }

    /**
     * 检查智能体是否被收藏
     */
    @Operation(summary = "检查智能体是否被收藏", description = "检查指定智能体是否被当前用户收藏")
    @PostMapping("/check")
    public ResponseResult<Boolean> checkFavorite(@RequestBody @Valid AgentFavoriteDTO req) {
        String username = UserContextUtil.getCurrentUsername();

        Integer isFavorite = agentFavoriteService.isFavorite(req.getAgentId(), username);
        return ResponseResult.success(isFavorite == 1);
    }


}