package com.gw.agent.controller;

import com.gw.common.agent.vo.CozeSettingVO;
import com.gw.common.dto.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 智能体控制器
 */
@RestController
@RequestMapping("/api/v1/agent/coze")
@RequiredArgsConstructor
@Tag(name = "智能体扣子管理", description = "智能体扣子相关API")
@Log4j2
public class AgentCozeController {
    @Operation(summary = "配置", description = "配置")
    @GetMapping("setting")
    public ResponseResult<CozeSettingVO> getSetting() {
        CozeSettingVO setting = new CozeSettingVO();
        String token = System.getenv("COZE_API_TOKEN");
        String baseUrl = System.getenv("COZE_API_BASE");
        setting.setToken(token);
        setting.setApiBase(baseUrl);
        return ResponseResult.success(setting);
    }
}