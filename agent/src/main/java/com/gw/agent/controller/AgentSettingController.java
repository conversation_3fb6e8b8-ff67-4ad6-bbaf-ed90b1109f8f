package com.gw.agent.controller;

import com.gw.agent.dto.AgentSettingDTO;
import com.gw.agent.entity.AgentEntity;
import com.gw.agent.entity.MyAgentCommonSettingEntity;
import com.gw.agent.service.AgentService;
import com.gw.agent.vo.MyAgentCommonSettingVO;
import com.gw.common.agent.dto.AgentSettingQueryDTO;
import com.gw.common.dto.ItemIdDTO;
import com.gw.common.dto.ResponseResult;
import com.gw.common.exception.BusinessException;
import com.gw.common.user.context.UserContextUtil;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import static com.gw.common.exception.BusinessExceptionCode.FAIL_CODE;


@RestController
@RequestMapping("/api/v1/agent/setting")
@RequiredArgsConstructor
@Tag(name = "智能体设置", description = "智能体设置相关API")
@Log4j2
public class AgentSettingController {

    private final AgentService agentService;

    private void validateUserPermission(AgentEntity entity, String username) {
        if (!entity.getCreator().equals(username)) {
            throw new BusinessException(FAIL_CODE.getCode(), "无权限操作");
        }
    }

    @PostMapping("/save")
    @Operation(summary = "保存智能体设置", description = "保存智能体设置")
    public ResponseResult<?> save(@RequestBody AgentSettingDTO dto) {
        String username = UserContextUtil.getCurrentUsername();
        MyAgentCommonSettingEntity entity = agentService.findSettingByAgentIdAndUsername(dto.getAgentId(), username);
        if (entity == null) {
            entity = new MyAgentCommonSettingEntity();
        }
        dto.setMyNickName(dto.getMyNickName().trim());
        entity.setUsername(username);
        entity.setAgentId(dto.getAgentId());
        BeanUtils.copyProperties(dto, entity);
        agentService.saveSetting(entity);
        return ResponseResult.success(null);
    }

    @GetMapping("/get_setting")
    @Operation(summary = "获得我的智能体设置", description = "获得我的智能体设置")

    public ResponseResult<MyAgentCommonSettingVO> findById() {
        String username = UserContextUtil.getCurrentUsername();
        MyAgentCommonSettingEntity entity = agentService.findSettingByAgentIdAndUsername(0L, username);
        return ResponseResult.success(new MyAgentCommonSettingVO(entity));
    }

    @PostMapping("get_setting")
    @Operation(summary = "获得我的智能体设置根据智能体ID", description = "获得我的智能体设置")
    public ResponseResult<MyAgentCommonSettingVO> findSettingById(@RequestBody ItemIdDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        long agentId = req.getId();
        log.info("{} 查询设置，智能体ID: {}", username, agentId);
        MyAgentCommonSettingEntity entity = agentService.findSettingByAgentIdAndUsername(agentId, username);
        if (entity == null && agentId != 0L) {
            entity = agentService.findSettingByAgentIdAndUsername(0L, username);
            if (entity != null) {
                entity.setAutoPlayVoice(false);
            }
        }

        return ResponseResult.success(new MyAgentCommonSettingVO(entity));
    }

    @PostMapping("/get_username")
    @Operation(summary = "获得智能体设置根据智能体ID", description = "获得智能体设置根据智能体ID")
    @Hidden
    public ResponseResult<MyAgentCommonSettingVO> findByUsername(@RequestBody @Valid AgentSettingQueryDTO req) {
        Long agentId = req.getAgentId();
        if (agentId == null) {
            agentId = 0L;
        }
        MyAgentCommonSettingEntity entity = agentService.findSettingByAgentIdAndUsername(agentId, req.getUsername());
        if (entity == null && agentId != 0L) {
            entity = agentService.findSettingByAgentIdAndUsername(0L, req.getUsername());
            if (entity != null) {
                entity.setAutoPlayVoice(false);
            }
        }
        return ResponseResult.success(new MyAgentCommonSettingVO(entity));
    }
} 