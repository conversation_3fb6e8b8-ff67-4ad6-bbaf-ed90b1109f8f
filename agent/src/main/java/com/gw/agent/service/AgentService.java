package com.gw.agent.service;

import com.github.pagehelper.PageInfo;
import com.gw.agent.dto.AgentQueryDTO;
import com.gw.agent.dto.MyCommentAgentQueryDTO;
import com.gw.agent.dto.MyFavoriteAgentQueryDTO;
import com.gw.agent.dto.MyLikeAgentQueryDTO;
import com.gw.agent.entity.AgentEntity;
import com.gw.agent.entity.AgentStatusEntity;
import com.gw.agent.entity.MyAgentCommonSettingEntity;
import com.gw.agent.vo.AgentTypeStatisticsVO;
import com.gw.common.dto.PageOrderField;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface AgentService {
    void insert(AgentEntity entity);

    void update(AgentEntity entity);

    void updateNotPublic(AgentEntity entity);

    void delete(AgentEntity entity);

    int cntByCreator(String creator);

    AgentEntity findById(Long id);

    Boolean existByExclusive();

    AgentEntity findByExclusive();

    Long findFirstIdByExclusive();

    AgentEntity findById(Long id, String username);

    List<AgentEntity> findAllByCreator(String username);

    List<AgentEntity> findAll();

    List<AgentEntity> findAllByIds(List<Long> ids);

    List<AgentEntity> queryByIdsKeepOrder(List<Long> ids);

    void updateRecommendIdxById(Long agentId, Integer recommendIdx);

    PageInfo<AgentEntity> page(int pageNum, int pageSize, AgentQueryDTO query, PageOrderField orderFields);

    /**
     * 根据ID列表和用户名查询智能体
     *
     * @param ids 智能体ID列表
     * @return 智能体列表
     */
    List<AgentEntity> findByIdsAndUsername(List<Long> ids, String username);

    void updateBgUrl(Long agentId, String bgUrl);

    void updateAvatarUrl(Long agentId, String avatarUrl);

    void updateThumbnailNotExist(AgentEntity entity);

    /**
     * 更新智能体热度值
     *
     * @param agentId   智能体ID
     * @param increment 增量值
     */
    void updatePopularity(Long agentId, Integer increment);

    /**
     * 记录用户使用智能体
     * 当新用户使用智能体时，增加智能体热度值
     *
     * @param agentId  智能体ID
     * @param username 用户名
     * @return 是否是首次使用
     */
    boolean recordAgentUsage(Long agentId, String username);

    void saveSetting(MyAgentCommonSettingEntity entity);

    MyAgentCommonSettingEntity findSettingById(Long id);

    MyAgentCommonSettingEntity findSettingByUsername(String username);

    MyAgentCommonSettingEntity findSettingByAgentIdAndUsername(Long agentId, String username);

    /**
     * 分页查询用户点赞的智能体
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @param query    查询条件
     * @return 分页结果
     */
    PageInfo<AgentEntity> pageLikedByUsername(int pageNum, int pageSize, MyLikeAgentQueryDTO query);

    /**
     * 分页查询用户收藏的智能体
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @param query    查询条件
     * @return 分页结果
     */
    PageInfo<AgentEntity> pageFavoriteByUsername(int pageNum, int pageSize, MyFavoriteAgentQueryDTO query);

    /**
     * 分页查询用户评论过的智能体
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @param query    查询条件
     * @return 分页结果
     */
    PageInfo<AgentEntity> pageCommentByUsername(int pageNum, int pageSize, MyCommentAgentQueryDTO query);

    /**
     * 清除与特定智能体相关的所有分页缓存
     *
     * @param agentId 智能体ID
     */
    void clearAgentPageCache(Long agentId);

    /**
     * 清除与特定用户相关的所有缓存
     *
     * @param username 用户名
     */
    void clearUserRelatedCache(String username);

    /**
     * 清除所有分页缓存
     */
    void clearAllPageCache();

    /**
     * 统计指定时间范围内的新增智能体数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 新增智能体数
     */
    int countTodayNewAgents(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计智能体总数
     *
     * @return 智能体总数
     */
    int countTotalAgents();

    /**
     * 统计每种AgentType的智能体数量
     *
     * @return 每种AgentType的统计数据列表
     */
    List<AgentTypeStatisticsVO> countAgentsByType();
    List<AgentStatusEntity> findAllStatusByAgentIds(@Param("agentIds")List< Long> agentIds);

    /**
     * 根据智能体名称模糊查询智能体ID列表
     *
     * @param agentName 智能体名称
     * @return 智能体ID列表
     */
    List<Long> findAgentIdsByNameLike(String agentName);
}
