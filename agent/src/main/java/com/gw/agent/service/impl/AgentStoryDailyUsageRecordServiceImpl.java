package com.gw.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gw.agent.entity.AgentStoryDailyUsageRecordEntity;
import com.gw.agent.mapper.sql.AgentStoryDailyUsageRecordMapper;
import com.gw.agent.service.AgentStoryDailyUsageRecordService;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;

/**
 * 智能体剧情每日使用记录服务实现类
 */
@Log4j2
@Service
public class AgentStoryDailyUsageRecordServiceImpl extends ServiceImpl<AgentStoryDailyUsageRecordMapper, AgentStoryDailyUsageRecordEntity> implements AgentStoryDailyUsageRecordService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AgentStoryDailyUsageRecordEntity recordDailyUsage(Long storyId, String username) {
        LocalDate today = LocalDate.now();

        // 查询当日是否已有记录
        AgentStoryDailyUsageRecordEntity record = this.findByStoryIdAndUsernameAndDate(storyId, username, today);

        if (record == null) {
            // 当日首次使用，创建新记录
            record = new AgentStoryDailyUsageRecordEntity();
            record.setStoryId(storyId);
            record.setUsername(username);
            record.setUseDate(today);
            record.setUseCount(1);
            record.setStatus(1);
            this.save(record);
        } else {
            // 已有记录，更新使用次数
            record.setUseCount(record.getUseCount() + 1);
            this.updateById(record);
        }

        return record;
    }

    @Override
    public AgentStoryDailyUsageRecordEntity findByStoryIdAndUsernameAndDate(Long storyId, String username, LocalDate date) {
        LambdaQueryWrapper<AgentStoryDailyUsageRecordEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentStoryDailyUsageRecordEntity::getStoryId, storyId)
                .eq(AgentStoryDailyUsageRecordEntity::getUsername, username)
                .eq(AgentStoryDailyUsageRecordEntity::getUseDate, date);

        return this.getOne(queryWrapper);
    }

    @Override
    public List<AgentStoryDailyUsageRecordEntity> findByStoryIdAndDateRange(Long storyId, LocalDate startDate, LocalDate endDate) {
        LambdaQueryWrapper<AgentStoryDailyUsageRecordEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentStoryDailyUsageRecordEntity::getStoryId, storyId)
                .ge(AgentStoryDailyUsageRecordEntity::getUseDate, startDate)
                .le(AgentStoryDailyUsageRecordEntity::getUseDate, endDate)
                .orderByAsc(AgentStoryDailyUsageRecordEntity::getUseDate);

        return this.list(queryWrapper);
    }

    @Override
    public List<AgentStoryDailyUsageRecordEntity> findByUsernameAndDateRange(String username, LocalDate startDate, LocalDate endDate) {
        LambdaQueryWrapper<AgentStoryDailyUsageRecordEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentStoryDailyUsageRecordEntity::getUsername, username)
                .ge(AgentStoryDailyUsageRecordEntity::getUseDate, startDate)
                .le(AgentStoryDailyUsageRecordEntity::getUseDate, endDate)
                .orderByAsc(AgentStoryDailyUsageRecordEntity::getUseDate);

        return this.list(queryWrapper);
    }

    @Override
    public int countDailyUsersByStoryId(Long storyId, LocalDate date) {
        LambdaQueryWrapper<AgentStoryDailyUsageRecordEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentStoryDailyUsageRecordEntity::getStoryId, storyId)
                .eq(AgentStoryDailyUsageRecordEntity::getUseDate, date)
                .select(AgentStoryDailyUsageRecordEntity::getUsername);

        // 统计去重后的用户数
        return Math.toIntExact(this.baseMapper.selectCount(queryWrapper));
    }

    @Override
    public int countDailyStoriesByUsername(String username, LocalDate date) {
        LambdaQueryWrapper<AgentStoryDailyUsageRecordEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentStoryDailyUsageRecordEntity::getUsername, username)
                .eq(AgentStoryDailyUsageRecordEntity::getUseDate, date)
                .select(AgentStoryDailyUsageRecordEntity::getStoryId);

        // 统计去重后的智能体剧情数
        return Math.toIntExact(this.baseMapper.selectCount(queryWrapper));
    }
}