package com.gw.agent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gw.agent.entity.AgentTypeEntity;
import com.gw.agent.mapper.sql.AgentTypeMapper;
import com.gw.agent.service.AgentTypeService;
import com.gw.common.exception.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Log4j2
public class AgentTypeServiceImpl extends ServiceImpl<AgentTypeMapper, AgentTypeEntity> implements AgentTypeService {
    private final String CACHE_KEY = "agentType";

    @Override
    @CacheEvict(value = CACHE_KEY, allEntries = true, beforeInvocation = true)
    public void insert(AgentTypeEntity entity) {
        this.baseMapper.insert(entity);
    }

    @Override
    @CacheEvict(value = CACHE_KEY, allEntries = true, beforeInvocation = true)
    public void update(AgentTypeEntity entity) {
        this.baseMapper.updateById(entity);
    }

    @Override
    @CacheEvict(value = CACHE_KEY, allEntries = true, beforeInvocation = true)
    public void delete(AgentTypeEntity entity) {
        entity.setDeleted(1);
        this.baseMapper.updateById(entity);
        this.baseMapper.deleteById(entity);
    }

    @Override
    @Cacheable(value = CACHE_KEY, key = "#root.methodName  + ':' + #id", unless = "#result == null")
    public AgentTypeEntity findById(Long id) {
        return this.baseMapper.findById(id).orElseThrow(() -> new EntityNotFoundException("类型不存在"));
    }

    @Override
    @Cacheable(value = CACHE_KEY, key = "#root.methodName", unless = "#result == null")
    public List<AgentTypeEntity> findAll() {
        return this.baseMapper.findAll();
    }

    @Override
    public boolean isNameExist(String name) {
        return this.baseMapper.findByName(name).isPresent();
    }

    @Override
    @Cacheable(value = CACHE_KEY, key = "#root.methodName", unless = "#result == null")
    public Map<Long, AgentTypeEntity> findTypesMap() {
        List<AgentTypeEntity> entities = this.baseMapper.findAll();
        return entities.stream().collect(java.util.stream.Collectors.toMap(AgentTypeEntity::getId, entity -> entity));
    }
}
