package com.gw.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gw.agent.entity.AgentTagEntity;
import com.gw.agent.mapper.sql.AgentTagMapper;
import com.gw.agent.service.AgentTagService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 智能体标签服务实现
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class AgentTagServiceImpl extends ServiceImpl<AgentTagMapper, AgentTagEntity> implements AgentTagService {

    @Override
    @CacheEvict(value = "agentTag", allEntries = true, beforeInvocation = true)
    public void insert(AgentTagEntity entity) {
        this.baseMapper.insert(entity);
    }

    @Override
    @CacheEvict(value = "agentTag", allEntries = true, beforeInvocation = true)
    public void update(AgentTagEntity entity) {
        this.baseMapper.updateById(entity);
    }

    @Override
    @CacheEvict(value = "agentTag", allEntries = true, beforeInvocation = true)
    public void delete(AgentTagEntity entity) {
        this.baseMapper.deleteById(entity.getId());
    }

    @Override
    @Cacheable(value = "agentTag", key = "#root.methodName  + ':' + #id", unless = "#result == null")
    public AgentTagEntity findById(Long id) {
        return this.baseMapper.selectById(id);
    }

    @Override
    @Cacheable(value = "agentTag", key = "#root.methodName", unless = "#result == null")
    public List<AgentTagEntity> findAll() {
        return this.baseMapper.selectList(null);
    }

    @Override
    public boolean isNameExistByCategory(String name, Integer category) {
        LambdaQueryWrapper<AgentTagEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentTagEntity::getName, name);

        if (category != null) {
            queryWrapper.eq(AgentTagEntity::getCategory, category);
        }

        queryWrapper.eq(AgentTagEntity::getDeleted, 0);

        return this.baseMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    @Cacheable(value = "agentTag", key = "#root.methodName", unless = "#result == null")
    public Map<Long, AgentTagEntity> findTagsMap() {
        List<AgentTagEntity> tags = this.baseMapper.selectList(null);

        if (CollectionUtils.isEmpty(tags)) {
            return Collections.emptyMap();
        }

        // 使用ID作为键构建映射
        return tags.stream()
                .collect(Collectors.toMap(AgentTagEntity::getId, tag -> tag, (existing, replacement) -> existing, HashMap::new));
    }
}
