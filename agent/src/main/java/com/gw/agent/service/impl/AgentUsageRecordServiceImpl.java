package com.gw.agent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gw.agent.constant.AgentCacheConstant;
import com.gw.agent.entity.AgentUsageRecordEntity;
import com.gw.agent.mapper.sql.AgentMapper;
import com.gw.agent.mapper.sql.AgentUsageRecordMapper;
import com.gw.agent.service.AgentDailyUsageRecordService;
import com.gw.agent.service.AgentUsageRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 智能体使用记录服务实现
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class AgentUsageRecordServiceImpl extends ServiceImpl<AgentUsageRecordMapper, AgentUsageRecordEntity>
        implements AgentUsageRecordService {
    private final String CacheCntValue = "agentUsageRecCnt";
    private final String CacheIds = "agentUsageIds";

    private final AgentMapper agentMapper;
    private final AgentDailyUsageRecordService agentDailyUsageRecordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {
            @CacheEvict(value = CacheCntValue, key = "'user:' + #username", condition = "#result == true"),
            @CacheEvict(value = AgentCacheConstant.AGENT_CACHE, key = "'id:' + #agentId"),
            @CacheEvict(value = CacheIds, key = "'user:' + #username"),
            @CacheEvict(value = AgentCacheConstant.AGENT_PAGE_CACHE, allEntries = true),
            @CacheEvict(value = CacheCntValue, key = "'agent:' + #agentId", condition = "#result == true")
    })
    public boolean recordUsage(Long agentId, String username) {
        Optional<AgentUsageRecordEntity> existingRecord = this.baseMapper.findByAgentIdAndUsername(agentId, username);

        // 无论是否首次使用，都记录每日使用数据
        agentDailyUsageRecordService.recordDailyUsage(agentId, username);

        if (existingRecord.isPresent()) {
            // 如果已有记录，更新使用次数
            AgentUsageRecordEntity record = existingRecord.get();
            this.baseMapper.updateUseCount(record.getId(), 1);
            agentMapper.updatePopularity(agentId, (int) (Math.random() * 10 + 1));
            log.info("用户[{}]再次使用智能体[{}]，使用次数+1", username, agentId);
            return false;
        } else {
            // 首次使用，创建新记录
            AgentUsageRecordEntity record = new AgentUsageRecordEntity();
            record.setAgentId(agentId);
            record.setUsername(username);
            record.setUseCount(1);
            record.setStatus(1);
            this.baseMapper.insert(record);
            agentMapper.updatePopularity(agentId, (int) (Math.random() * 3));
            log.info("用户[{}]首次使用智能体[{}]，创建使用记录", username, agentId);
            return true;
        }
    }

    @Override
    public AgentUsageRecordEntity findByAgentIdAndUsername(Long agentId, String username) {
        return this.baseMapper.findByAgentIdAndUsername(agentId, username).orElse(null);
    }

    @Override
    @Cacheable(value = CacheCntValue, key = "'agent:' + #agentId")
    public int getUserCount(Long agentId) {
        return this.baseMapper.countByAgentId(agentId);
    }

    @Override
    @Cacheable(value = CacheCntValue, key = "'user:' + #username")
    public int countByUsername(String username) {
        return this.baseMapper.countByUsername(username);
    }

    @Override
    @Cacheable(value = CacheIds, key = "'user:' + #username")
    public List<Long> findAgentIdsByUsername(String username) {
        return this.baseMapper.findAgentIdsByUsername(username);
    }

    @Override
    public int countTodayActiveUsers(LocalDateTime startTime, LocalDateTime endTime) {
        return this.baseMapper.countActiveUsersByTimeRange(startTime, endTime);
    }
}