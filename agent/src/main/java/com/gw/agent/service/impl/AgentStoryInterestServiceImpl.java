package com.gw.agent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gw.agent.entity.AgentStoryInterestEntity;
import com.gw.agent.mapper.sql.AgentStoryInterestMapper;
import com.gw.agent.service.AgentStoryInterestService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 智能体剧情兴趣服务实现
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class AgentStoryInterestServiceImpl extends ServiceImpl<AgentStoryInterestMapper, AgentStoryInterestEntity> implements AgentStoryInterestService {
    private final String CacheAgentStoryUsrInterestValue = "agentStoryUsrInterest";

    @Override
    public int getInterestLevel(Long storyId, String username) {
        if (storyId == null || username == null) {
            return 0;
        }
        AgentStoryInterestEntity entity = this.baseMapper.findByStoryIdAndUsername(storyId, username);
        return entity != null ? entity.getInterestLevel() : 0;
    }

    @CacheEvict(value = CacheAgentStoryUsrInterestValue, key = "'story:' + #username", beforeInvocation = true)
    @Override
    public boolean markInterest(Long storyId, String username, Integer interestLevel, Integer source) {
        if (storyId == null || username == null || interestLevel == null) {
            return false;
        }

        try {
            AgentStoryInterestEntity entity = this.baseMapper.findByStoryIdAndUsername(storyId, username);
            LocalDateTime now = LocalDateTime.now();

            if (entity == null) {
                // 创建新的兴趣关系
                entity = new AgentStoryInterestEntity();
                entity.setStoryId(storyId);
                entity.setUsername(username);
                entity.setInterestLevel(interestLevel);
                entity.setSource(source != null ? source : 1); // 默认来源为浏览
                entity.setMarked(1);
                entity.setUpdateTime(now);
                return this.save(entity);
            } else {
                // 更新已有的兴趣关系
                entity.setInterestLevel(interestLevel);
                if (source != null) {
                    entity.setSource(source);
                }
                entity.setMarked(1);
                entity.setUpdateTime(now);
                return this.updateById(entity);
            }
        } catch (Exception e) {
            log.error("标记智能体剧情兴趣失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @CacheEvict(value = CacheAgentStoryUsrInterestValue, key = "'story:' + #username", beforeInvocation = true)
    @Override
    public boolean unmarkInterest(Long storyId, String username) {
        if (storyId == null || username == null) {
            return false;
        }

        try {
            AgentStoryInterestEntity entity = this.baseMapper.findByStoryIdAndUsername(storyId, username);
            if (entity != null) {
                // 设置为未标记状态
                entity.setMarked(0);
                entity.setUpdateTime(LocalDateTime.now());
                return this.updateById(entity);
            }
            return true; // 如果不存在，直接返回成功
        } catch (Exception e) {
            log.error("取消智能体剧情兴趣标记失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int getInterestCount(Long storyId) {
        if (storyId == null) {
            return 0;
        }
        return this.baseMapper.countByStoryId(storyId);
    }

    @Override
    public List<Long> getInterestedStoryIds(String username) {
        if (username == null) {
            return Collections.emptyList();
        }
        List<AgentStoryInterestEntity> interests = this.baseMapper.findByUsername(username);
        return interests.stream()
                .map(AgentStoryInterestEntity::getStoryId)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getInterestedUsernames(Long storyId) {
        if (storyId == null) {
            return Collections.emptyList();
        }
        List<AgentStoryInterestEntity> interests = this.baseMapper.findByStoryId(storyId);
        return interests.stream()
                .map(AgentStoryInterestEntity::getUsername)
                .collect(Collectors.toList());
    }

    @Cacheable(value = CacheAgentStoryUsrInterestValue, key = "'story:' + #username", unless = "#result == null")
    @Override
    public List<Long> findUnmarkedByUsername(String username) {
        return this.baseMapper.findUnmarkedByUsername(username);
    }
}