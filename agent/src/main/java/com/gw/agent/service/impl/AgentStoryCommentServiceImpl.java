package com.gw.agent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gw.agent.constant.AgentCacheConstant;
import com.gw.agent.entity.AgentStoryCommentEntity;
import com.gw.agent.entity.AgentStoryEntity;
import com.gw.agent.mapper.sql.AgentStoryCommentMapper;
import com.gw.agent.mapper.sql.AgentStoryMapper;
import com.gw.agent.service.AgentStoryCommentLikeService;
import com.gw.agent.service.AgentStoryCommentService;
import com.gw.agent.service.AgentStoryStatisticsService;
import com.gw.agent.vo.AgentStoryCommentVO;
import com.gw.common.exception.BusinessException;
import com.gw.common.exception.EntityNotFoundException;
import com.gw.common.notify.constant.NotifyConstant;
import com.gw.common.notify.dto.InteractiveNotifySubmitDTO;
import com.gw.common.notify.service.NotifyProxyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 智能体剧情评论服务实现
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class AgentStoryCommentServiceImpl extends ServiceImpl<AgentStoryCommentMapper, AgentStoryCommentEntity> implements AgentStoryCommentService {
    private final NotifyProxyService notifyProxyService;
    private final AgentStoryMapper agentStoryMapper;
    private final AgentStoryCommentLikeService commentLikeService;
    private final ObjectMapper objectMapper;
    private final AgentStoryStatisticsService statisticsService;
    private final String CacheValueCnt = "agentStoryCommentCnt";
    private final String CacheStoryIds = "agentStoryCommentIds";

    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {
            @CacheEvict(value = CacheValueCnt, key = "'user:' + #username"),
            @CacheEvict(value = CacheStoryIds, key = "'comment:' + #username"),
            @CacheEvict(value = AgentCacheConstant.AGENT_CACHE, key = "'story:' + #storyId"),
            @CacheEvict(value = AgentCacheConstant.AGENT_PAGE_CACHE, allEntries = true),
            @CacheEvict(value = CacheValueCnt, key = "'story:' + #storyId")
    })
    @Override
    public AgentStoryCommentEntity addComment(Long storyId, String username, String content, Long parentId,
                                              String replyToUsername, List<String> images, List<String> messages) {
        // 验证剧情是否存在
        Optional<AgentStoryEntity> story = agentStoryMapper.findById(storyId);
        if (story.isEmpty()) {
            throw new EntityNotFoundException("剧情不存在");
        }
        AgentStoryEntity storyEntity = story.get();

        // 处理parentId：如果为0则设置为null，表示顶级评论
        if (parentId != null && parentId == 0) {
            parentId = null;
        }

        // 如果是回复评论，验证父评论是否存在
        if (parentId != null && parentId > 0) {
            AgentStoryCommentEntity parentComment = this.baseMapper.selectById(parentId);
            if (parentComment == null || parentComment.getDeleted() == 1 || parentComment.getStatus() == 0) {
                throw new EntityNotFoundException("父评论不存在或已被删除");
            }

            // 确保回复的是同一个剧情的评论
            if (!parentComment.getStoryId().equals(storyId)) {
                throw new BusinessException("不能回复其他剧情的评论");
            }

            // 如果没有指定被回复的用户名，则使用父评论的用户名
            if (replyToUsername == null || replyToUsername.trim().isEmpty()) {
                replyToUsername = parentComment.getUsername();
            }
        }

        // 创建评论实体
        AgentStoryCommentEntity comment = new AgentStoryCommentEntity();
        comment.setStoryId(storyId);
        comment.setUsername(username);
        comment.setContent(content);
        comment.setParentId(parentId);
        comment.setReplyToUsername(replyToUsername);
        comment.setStatus(1); // 默认状态为正常

        // 处理图片列表（最多9张）
        if (images != null && !images.isEmpty()) {
            if (images.size() > 9) {
                throw new BusinessException("图片数量不能超过9张");
            }
            try {
                comment.setImages(objectMapper.writeValueAsString(images));
            } catch (JsonProcessingException e) {
                log.error("序列化图片列表失败", e);
                throw new BusinessException("图片数据格式错误");
            }
        }

        // 处理消息列表
        if (messages != null && !messages.isEmpty()) {
            try {
                comment.setMessages(objectMapper.writeValueAsString(messages));
            } catch (JsonProcessingException e) {
                log.error("序列化消息列表失败", e);
                throw new BusinessException("消息数据格式错误");
            }
        }

        InteractiveNotifySubmitDTO interactiveNotifySubmitDTO = new InteractiveNotifySubmitDTO(
                "{nickname}评论了我创作的剧情" + "\"" + storyEntity.getName() + "\"",
                NotifyConstant.INTERACTIVE_AGENT_COMMENTS,
                storyId,
                storyEntity.getCreator(), username);
        notifyProxyService.insertInteractiveMessage(interactiveNotifySubmitDTO);

        // 保存评论
        this.baseMapper.insert(comment);

        // 如果是回复评论，更新父评论的统计信息
        if (parentId != null) {
            // 更新父评论的子评论数量
            this.baseMapper.updateChildCount(parentId, 1);
            // 更新父评论的最新子评论ID
            this.baseMapper.updateLastChildId(parentId, comment.getId());
        }

        agentStoryMapper.updatePopularity(storyId, (int) (Math.random() * 41 + 10));
        statisticsService.updateCommentCount(storyId, 1);
        return comment;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {
            @CacheEvict(value = CacheValueCnt,  allEntries = true),
            @CacheEvict(value = AgentCacheConstant.AGENT_CACHE, allEntries = true),
            @CacheEvict(value = CacheStoryIds, allEntries = true),
            @CacheEvict(value = AgentCacheConstant.AGENT_PAGE_CACHE, allEntries = true),
            @CacheEvict(value = CacheValueCnt,  allEntries = true)
    })
    public boolean deleteComment(Long storyId, Long commentId, String username) {
        // 查询评论是否存在
        AgentStoryCommentEntity comment = this.baseMapper.selectById(commentId);
        if (comment == null || comment.getDeleted() == 1) {
            return true; // 评论不存在或已删除，视为删除成功
        }

        // 验证是否是评论作者
        if (!comment.getUsername().equals(username)) {
            throw new BusinessException("只能删除自己的评论");
        }

        // 递归删除评论及其所有子评论，并返回删除的总数量
        int deletedCount = deleteCommentRecursively(commentId, username);

        // 如果是子评论，更新父评论的统计信息
        if (comment.getParentId() != null) {
            // 减少父评论的子评论数量（只减少直接子评论的数量）
            this.baseMapper.updateChildCount(comment.getParentId(), -1);
            // 重新计算父评论的最新子评论ID
            this.baseMapper.recalculateLastChildId(comment.getParentId());
        }

        // 更新剧情的评论总数（包括所有被删除的评论）
        statisticsService.updateCommentCount(storyId, -deletedCount);

        return true;
    }
    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {
            @CacheEvict(value = CacheValueCnt,  allEntries = true),
            @CacheEvict(value = AgentCacheConstant.AGENT_CACHE, allEntries = true),
            @CacheEvict(value = CacheStoryIds, allEntries = true),
            @CacheEvict(value = AgentCacheConstant.AGENT_PAGE_CACHE, allEntries = true),
            @CacheEvict(value = CacheValueCnt,  allEntries = true)
    })
    @Override
    public boolean adminDeleteComment(Long storyId, Long commentId) {
        // 查询评论是否存在
        AgentStoryCommentEntity comment = this.baseMapper.selectById(commentId);
        if (comment == null || comment.getDeleted() == 1) {
            return true; // 评论不存在或已删除，视为删除成功
        }

        // 递归删除评论及其所有子评论，并返回删除的总数量
        int deletedCount = deleteCommentRecursively(commentId, null);

        // 如果是子评论，更新父评论的统计信息
        if (comment.getParentId() != null) {
            // 减少父评论的子评论数量（只减少直接子评论的数量）
            this.baseMapper.updateChildCount(comment.getParentId(), -1);
            // 重新计算父评论的最新子评论ID
            this.baseMapper.recalculateLastChildId(comment.getParentId());
        }

        // 更新剧情的评论总数（包括所有被删除的评论）
        statisticsService.updateCommentCount(storyId, -deletedCount);

        return true;
    }
    @Override
    public AgentStoryCommentEntity findById(Long commentId) {
        Optional<AgentStoryCommentEntity> comment = this.baseMapper.findById(commentId);
        if (comment.isEmpty()) {
            throw new EntityNotFoundException("评论不存在");
        }
        return comment.get();
    }


    @Override
    public List<AgentStoryCommentEntity> getCommentReplies(Long commentId, String username) {
        List<AgentStoryCommentEntity> replies = this.baseMapper.findRepliesByParentId(commentId);
        if (!CollectionUtils.isEmpty(replies)) {
            fillCommentExtendedInfo(replies, username);
        }
        return replies;
    }

    @Override
    @Cacheable(value = CacheValueCnt, key = "'story:' + #storyId")
    public int getCommentCount(Long storyId) {
        return this.baseMapper.countByStoryId(storyId);
    }

    @Cacheable(value = CacheValueCnt, key = "'user:' + #username")
    @Override
    public int countByUsernameAndDistinctStory(String username) {
        return this.baseMapper.countByUsernameAndDistinctStory(username);
    }



    /**
     * 填充评论的最新子评论信息
     *
     * @param comments 评论列表
     * @param username 当前用户名（用于填充点赞状态）
     */
    public void fillLatestChild(List<AgentStoryCommentEntity> comments, String username) {
        if (CollectionUtils.isEmpty(comments)) {
            return;
        }

        // 收集所有有效的lastChildId
        List<Long> lastChildIds = comments.stream()
                .map(AgentStoryCommentEntity::getLastChildId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        if (CollectionUtils.isEmpty(lastChildIds)) {
            return;
        }

        // 批量查询最新子评论
        List<AgentStoryCommentEntity> latestChildren = this.baseMapper.findByIds(lastChildIds);
        if (!CollectionUtils.isEmpty(latestChildren)) {
            // 填充扩展信息
            fillCommentExtendedInfo(latestChildren, username);

            // 按ID建立映射
            Map<Long, AgentStoryCommentEntity> latestChildMap = latestChildren.stream()
                    .collect(Collectors.toMap(AgentStoryCommentEntity::getId, Function.identity()));

            // 填充每个评论的最新子评论
            for (AgentStoryCommentEntity comment : comments) {
                if (comment.getLastChildId() != null) {
                    AgentStoryCommentEntity latestChild = latestChildMap.get(comment.getLastChildId());
                    comment.setLatestChild(latestChild);
                }
            }
        }
    }

    @Override
    public void fillCommentExtendedInfo(List<AgentStoryCommentEntity> comments, String username) {
        if (CollectionUtils.isEmpty(comments)) {
            return;
        }

        List<Long> commentIds = comments.stream()
                .map(AgentStoryCommentEntity::getId)
                .toList();

        // 批量获取用户点赞状态（点赞数直接从数据库字段获取）
        Map<Long, Integer> userLikeStatusMap = new HashMap<>();
        if (username != null && !username.trim().isEmpty()) {
            userLikeStatusMap = commentLikeService.batchIsLiked(commentIds, username);
        }

        // 填充扩展信息
        for (AgentStoryCommentEntity comment : comments) {
            Long commentId = comment.getId();

            // 设置点赞数（直接使用数据库字段）
            comment.setLikeCount(comment.getLikeCnt() != null ? comment.getLikeCnt() : 0);

            // 设置用户点赞状态
            comment.setIsLiked(userLikeStatusMap.getOrDefault(commentId, 0));

            // 设置回复数量（直接使用数据库字段）
            comment.setReplyCount(comment.getChildCnt() != null ? comment.getChildCnt() : 0);

            // 解析图片列表
            if (comment.getImages() != null && !comment.getImages().trim().isEmpty()) {
                try {
                    List<String> imageList = objectMapper.readValue(comment.getImages(), new TypeReference<List<String>>() {});
                    comment.setImageList(imageList);
                } catch (JsonProcessingException e) {
                    log.warn("解析评论图片列表失败，commentId: {}", commentId, e);
                    comment.setImageList(new ArrayList<>());
                }
            } else {
                comment.setImageList(new ArrayList<>());
            }

            // 解析消息列表
            if (comment.getMessages() != null && !comment.getMessages().trim().isEmpty()) {
                try {
                    List<String> messageList = objectMapper.readValue(comment.getMessages(), new TypeReference<List<String>>() {});
                    comment.setMessageList(messageList);
                } catch (JsonProcessingException e) {
                    log.warn("解析评论消息列表失败，commentId: {}", commentId, e);
                    comment.setMessageList(new ArrayList<>());
                }
            } else {
                comment.setMessageList(new ArrayList<>());
            }
        }
    }

    @Override
    public PageInfo<AgentStoryCommentEntity> pageStoryComments(int pageNum, int pageSize, Long storyId, Long parentId,String username) {
        PageHelper.startPage(pageNum, pageSize);

        // 处理parentId：如果为0则设置为null，表示查询顶级评论
        if (parentId != null && parentId == 0) {
            parentId = null;
        }

        List<AgentStoryCommentEntity> comments = this.baseMapper.pageStoryComments(storyId, parentId);
        if (!CollectionUtils.isEmpty(comments)) {
            fillCommentExtendedInfo(comments, username);
            // 只有在查询顶级评论时才填充回复列表和最新子评论，避免递归查询
            if (parentId == null) {

                fillLatestChild(comments, username);
            }
        }
        return new PageInfo<>(comments);
    }

    @Override
    public AgentStoryCommentEntity addCommentVO(Long storyId, String username, String content, Long parentId,
                                           String replyToUsername, List<String> images, List<String> messages) {
        AgentStoryCommentEntity entity = addComment(storyId, username, content, parentId, replyToUsername, images, messages);
        // 重新查询以获取完整的扩展信息
        AgentStoryCommentEntity fullEntity = findById(entity.getId());
//        List<AgentStoryCommentEntity> entityList = List.of(fullEntity);
//        fillCommentExtendedInfo(entityList, username);
        return fullEntity;
    }

    @Override
    public PageInfo<AgentStoryCommentEntity> pageStoryCommentsAdmin(int pageNum, int pageSize, List<Long> storyIds, Long parentId, Integer status, String username) {
        PageHelper.startPage(pageNum, pageSize);

        // 处理parentId：如果为0则设置为null，表示查询顶级评论
        if (parentId != null && parentId == 0) {
            parentId = null;
        }

        List<AgentStoryCommentEntity> comments = this.baseMapper.pageStoryCommentsAdmin(storyIds, parentId, status, username);
        if (!CollectionUtils.isEmpty(comments)) {
            fillCommentExtendedInfo(comments, null);
            // 只有在查询顶级评论时才填充最新子评论，避免递归查询
            if (parentId == null) {
                fillLatestChild(comments, null);
            }
        }
        return new PageInfo<>(comments);
    }

    /**
     * 转换AgentStoryCommentEntity为AgentStoryCommentVO
     */
    private AgentStoryCommentVO convertToVO(AgentStoryCommentEntity entity) {
        if (entity == null) {
            return null;
        }

        AgentStoryCommentVO vo = AgentStoryCommentVO.builder()
                .id(entity.getId())
                .storyId(entity.getStoryId())
                .username(entity.getUsername())
                .nickname(entity.getNickname())
                .avatarUrl(entity.getAvatarUrl())
                .content(entity.getContent())
                .parentId(entity.getParentId())
                .replyToUsername(entity.getReplyToUsername())
                .status(entity.getStatus())
                .likeCount(entity.getLikeCount())
                .isLiked(entity.getIsLiked())
                .replyCount(entity.getReplyCount())
                .imageList(entity.getImageList())
                .messageList(entity.getMessageList())
                .createTime(entity.getCreateTime())
                .updateTime(entity.getUpdateTime())
                .build();



        // 转换最新子评论
        if (entity.getLatestChild() != null) {
            vo.setLatestChild(convertToVO(entity.getLatestChild()));
        }

        return vo;
    }

    /**
     * 递归删除评论及其所有子评论
     *
     * @param commentId 要删除的评论ID
     * @param username 操作用户名（用于权限验证）
     * @return 删除的评论总数量
     */
    private int deleteCommentRecursively(Long commentId, String username) {
        return deleteCommentRecursively(commentId, username, true);
    }

    /**
     * 递归删除评论及其所有子评论
     *
     * @param commentId 要删除的评论ID
     * @param username 操作用户名（用于权限验证）
     * @param isRootDelete 是否为根级删除操作（true表示需要验证权限，false表示级联删除无需验证权限）
     * @return 删除的评论总数量
     */
    private int deleteCommentRecursively(Long commentId, String username, boolean isRootDelete) {
        // 查询当前评论
        AgentStoryCommentEntity comment = this.baseMapper.selectById(commentId);
        if (comment == null || comment.getDeleted() == 1) {
            return 0; // 评论不存在或已删除
        }

        // 验证权限：只有根级删除时才需要验证权限，子评论级联删除无需验证
        if (username != null && isRootDelete && !comment.getUsername().equals(username)) {
            log.error("只能删除自己的评论");
            return 0;
        }

        int deletedCount = 0;

        // 先递归删除所有子评论（子评论删除不需要权限验证）
        List<AgentStoryCommentEntity> childComments = this.baseMapper.findRepliesByParentId(commentId);
        for (AgentStoryCommentEntity childComment : childComments) {
            deletedCount += deleteCommentRecursively(childComment.getId(), username, false);
        }

        // 删除当前评论
        comment.setDeleted(1);
        this.baseMapper.updateById(comment);
        this.baseMapper.deleteById(comment);
        deletedCount++;

        log.info("删除评论：ID={}, 用户={}, 子评论数量={}, 是否根级删除={}",
                commentId, username, childComments.size(), isRootDelete);

        return deletedCount;
    }
}