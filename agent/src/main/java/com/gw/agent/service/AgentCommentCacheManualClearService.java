package com.gw.agent.service;

import com.gw.agent.constant.AgentCacheConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * 智能体评论手动缓存清除服务
 * 用于手动清除 AgentCommentServiceImpl 中 @Caching 注解对应的缓存项
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class AgentCommentCacheManualClearService {
    
    private final CacheManager cacheManager;
    private final RedisTemplate<String, Object> redisTemplate;
    
    // 缓存名称常量 - 对应 AgentCommentServiceImpl 中的常量
    private final String CacheValueCnt = "agentCommentCnt";
    private final String CacheAgentIds = "agentCommentIds";
    
    /**
     * 手动清除智能体评论相关的缓存项
     * 对应 AgentCommentServiceImpl 中的 @Caching 注解
     * 
     * @param agentId 智能体ID
     * @param username 用户名
     */
    public void clearAgentCommentCaches(Long agentId, String username) {
        log.info("开始手动清除智能体评论缓存 - 智能体ID: {}, 用户名: {}", agentId, username);
        
        try {
            // 1. 清除 CacheValueCnt 缓存中的用户相关项
            // 对应: @CacheEvict(value = CacheValueCnt, key = "'user:' + #username")
            clearCacheValueCntUserCache(username);
            
            // 2. 清除 CacheAgentIds 缓存中的用户评论相关项
            // 对应: @CacheEvict(value = CacheAgentIds, key = "'comment:' + #username")
            clearCacheAgentIdsCommentCache(username);
            
            // 3. 清除 AgentCacheConstant.AGENT_CACHE 缓存中的智能体相关项
            // 对应: @CacheEvict(value = AgentCacheConstant.AGENT_CACHE, key = "'id:' + #agentId")
            clearAgentCacheIdCache(agentId);
            
            // 4. 清除 AgentCacheConstant.AGENT_PAGE_CACHE 所有项
            // 对应: @CacheEvict(value = AgentCacheConstant.AGENT_PAGE_CACHE, allEntries = true)
            clearAgentPageCache();
            
            // 5. 清除 CacheValueCnt 缓存中的智能体相关项
            // 对应: @CacheEvict(value = CacheValueCnt, key = "'agent:' + #agentId")
            clearCacheValueCntAgentCache(agentId);
            
            log.info("手动清除智能体评论缓存完成 - 智能体ID: {}, 用户名: {}", agentId, username);
            
        } catch (Exception e) {
            log.error("手动清除智能体评论缓存失败 - 智能体ID: {}, 用户名: {}, 错误: {}", 
                     agentId, username, e.getMessage(), e);
            throw new RuntimeException("清除智能体评论缓存失败: " + e.getMessage());
        }
    }
    
    /**
     * 清除 CacheValueCnt 缓存中的用户相关项
     * 对应: @CacheEvict(value = CacheValueCnt, key = "'user:' + #username")
     */
    private void clearCacheValueCntUserCache(String username) {
        try {
            org.springframework.cache.Cache cache = cacheManager.getCache(CacheValueCnt);
            if (cache != null) {
                String cacheKey = "user:" + username;
                cache.evict(cacheKey);
                log.debug("清除缓存: {} - key: {}", CacheValueCnt, cacheKey);
            }
        } catch (Exception e) {
            log.warn("清除用户缓存失败 - 用户名: {}, 错误: {}", username, e.getMessage());
        }
    }
    
    /**
     * 清除 CacheAgentIds 缓存中的用户评论相关项
     * 对应: @CacheEvict(value = CacheAgentIds, key = "'comment:' + #username")
     */
    private void clearCacheAgentIdsCommentCache(String username) {
        try {
            org.springframework.cache.Cache cache = cacheManager.getCache(CacheAgentIds);
            if (cache != null) {
                String cacheKey = "comment:" + username;
                cache.evict(cacheKey);
                log.debug("清除缓存: {} - key: {}", CacheAgentIds, cacheKey);
            }
        } catch (Exception e) {
            log.warn("清除用户评论智能体ID缓存失败 - 用户名: {}, 错误: {}", username, e.getMessage());
        }
    }
    
    /**
     * 清除 AgentCacheConstant.AGENT_CACHE 缓存中的智能体相关项
     * 对应: @CacheEvict(value = AgentCacheConstant.AGENT_CACHE, key = "'id:' + #agentId")
     */
    private void clearAgentCacheIdCache(Long agentId) {
        try {
            org.springframework.cache.Cache cache = cacheManager.getCache(AgentCacheConstant.AGENT_CACHE);
            if (cache != null) {
                String cacheKey = "id:" + agentId;
                cache.evict(cacheKey);
                log.debug("清除缓存: {} - key: {}", AgentCacheConstant.AGENT_CACHE, cacheKey);
            }
        } catch (Exception e) {
            log.warn("清除智能体缓存失败 - 智能体ID: {}, 错误: {}", agentId, e.getMessage());
        }
    }
    
    /**
     * 清除 AgentCacheConstant.AGENT_PAGE_CACHE 所有项
     * 对应: @CacheEvict(value = AgentCacheConstant.AGENT_PAGE_CACHE, allEntries = true)
     */
    private void clearAgentPageCache() {
        try {
            org.springframework.cache.Cache cache = cacheManager.getCache(AgentCacheConstant.AGENT_PAGE_CACHE);
            if (cache != null) {
                cache.clear();
                log.debug("清除缓存: {} - 所有项", AgentCacheConstant.AGENT_PAGE_CACHE);
            }
        } catch (Exception e) {
            log.warn("清除智能体页面缓存失败 - 错误: {}", e.getMessage());
        }
    }
    
    /**
     * 清除 CacheValueCnt 缓存中的智能体相关项
     * 对应: @CacheEvict(value = CacheValueCnt, key = "'agent:' + #agentId")
     */
    private void clearCacheValueCntAgentCache(Long agentId) {
        try {
            org.springframework.cache.Cache cache = cacheManager.getCache(CacheValueCnt);
            if (cache != null) {
                String cacheKey = "agent:" + agentId;
                cache.evict(cacheKey);
                log.debug("清除缓存: {} - key: {}", CacheValueCnt, cacheKey);
            }
        } catch (Exception e) {
            log.warn("清除智能体计数缓存失败 - 智能体ID: {}, 错误: {}", agentId, e.getMessage());
        }
    }
    
    /**
     * 清除所有智能体评论相关缓存（危险操作，谨慎使用）
     */
    public void clearAllAgentCommentCaches() {
        log.warn("开始清除所有智能体评论相关缓存 - 这是一个危险操作");
        
        try {
            // 清除所有相关缓存
            clearCacheByName(CacheValueCnt);
            clearCacheByName(CacheAgentIds);
            clearCacheByName(AgentCacheConstant.AGENT_CACHE);
            clearCacheByName(AgentCacheConstant.AGENT_PAGE_CACHE);
            
            log.warn("清除所有智能体评论相关缓存完成");
            
        } catch (Exception e) {
            log.error("清除所有智能体评论相关缓存失败 - 错误: {}", e.getMessage(), e);
            throw new RuntimeException("清除所有智能体评论缓存失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据缓存名称清除整个缓存
     */
    private void clearCacheByName(String cacheName) {
        try {
            org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
                log.debug("清除整个缓存: {}", cacheName);
            }
        } catch (Exception e) {
            log.warn("清除缓存失败 - 缓存名: {}, 错误: {}", cacheName, e.getMessage());
        }
    }
    
    /**
     * 获取智能体评论缓存状态信息
     */
    public String getAgentCommentCacheStatus() {
        StringBuilder status = new StringBuilder();
        status.append("智能体评论缓存状态信息:\n");
        
        String[] cacheNames = {CacheValueCnt, CacheAgentIds, 
                              AgentCacheConstant.AGENT_CACHE, AgentCacheConstant.AGENT_PAGE_CACHE};
        
        for (String cacheName : cacheNames) {
            try {
                org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
                if (cache != null) {
                    status.append(String.format("- %s: 存在\n", cacheName));
                } else {
                    status.append(String.format("- %s: 不存在\n", cacheName));
                }
            } catch (Exception e) {
                status.append(String.format("- %s: 检查失败 - %s\n", cacheName, e.getMessage()));
            }
        }
        
        return status.toString();
    }
    
    /**
     * 清除指定用户的智能体评论相关缓存
     */
    public void clearUserAgentCommentCaches(String username) {
        log.info("开始清除用户智能体评论缓存 - 用户名: {}", username);
        
        try {
            clearCacheValueCntUserCache(username);
            clearCacheAgentIdsCommentCache(username);
            clearAgentPageCache(); // 页面缓存可能包含用户相关数据
            
            log.info("清除用户智能体评论缓存完成 - 用户名: {}", username);
            
        } catch (Exception e) {
            log.error("清除用户智能体评论缓存失败 - 用户名: {}, 错误: {}", username, e.getMessage(), e);
            throw new RuntimeException("清除用户智能体评论缓存失败: " + e.getMessage());
        }
    }
    
    /**
     * 清除指定智能体的评论相关缓存
     */
    public void clearAgentCommentCaches(Long agentId) {
        log.info("开始清除智能体评论缓存 - 智能体ID: {}", agentId);
        
        try {
            clearAgentCacheIdCache(agentId);
            clearCacheValueCntAgentCache(agentId);
            clearAgentPageCache(); // 页面缓存可能包含智能体相关数据
            
            log.info("清除智能体评论缓存完成 - 智能体ID: {}", agentId);
            
        } catch (Exception e) {
            log.error("清除智能体评论缓存失败 - 智能体ID: {}, 错误: {}", agentId, e.getMessage(), e);
            throw new RuntimeException("清除智能体评论缓存失败: " + e.getMessage());
        }
    }
}
