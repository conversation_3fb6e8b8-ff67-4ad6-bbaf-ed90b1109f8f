package com.gw.agent.service;

import com.github.pagehelper.PageInfo;
import com.gw.agent.constant.AgentCacheConstant;
import com.gw.agent.entity.AgentCommentEntity;
import com.gw.agent.vo.AgentCommentVO;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 智能体评论服务接口
 */
public interface AgentCommentService {

    /**
     * 添加评论
     *
     * @param agentId         智能体ID
     * @param username        用户名
     * @param content         评论内容
     * @param parentId        父评论ID，如果是顶级评论则为null
     * @param replyToUsername 被回复的用户名（仅在回复评论时有值）
     * @param images          图片列表
     * @param messages        消息列表
     * @return 评论记录
     */
    AgentCommentEntity addComment(Long agentId, String username, String content, Long parentId,
                                  String replyToUsername, List<String> images, List<String> messages);

    /**
     * 添加评论（兼容旧接口）
     *
     * @param agentId  智能体ID
     * @param username 用户名
     * @param content  评论内容
     * @param parentId 父评论ID，如果是顶级评论则为null
     * @return 评论记录
     */
    default AgentCommentEntity addComment(Long agentId, String username, String content, Long parentId) {
        return addComment(agentId, username, content, parentId, null, null, null);
    }

    /**
     * 添加评论并返回VO
     *
     * @param agentId         智能体ID
     * @param username        用户名
     * @param content         评论内容
     * @param parentId        父评论ID，如果是顶级评论则为null
     * @param replyToUsername 被回复的用户名
     * @param images          图片列表
     * @param messages        消息列表
     * @return 评论VO
     */
    AgentCommentEntity addCommentVO(Long agentId, String username, String content, Long parentId,
                               String replyToUsername, List<String> images, List<String> messages);

    /**
     * 删除评论
     *
     * @param commentId 评论ID
     * @param username  用户名（用于权限验证）
     * @return 是否成功
     */
    boolean deleteComment(Long agentId, Long commentId, String username);

    boolean adminDeleteComment(Long agentId, Long commentId);

    AgentCommentEntity findById(Long commentId);

    /**
     * 获取智能体的评论列表（只返回顶级评论）
     *
     * @param agentId  智能体ID
     * @param username 当前用户名（用于填充点赞状态）
     * @return 评论列表
     */
    List<AgentCommentEntity> getAgentComments(Long agentId, String username);

    /**
     * 获取智能体的评论列表（兼容旧接口）
     *
     * @param agentId 智能体ID
     * @return 评论列表
     */
    default List<AgentCommentEntity> getAgentComments(Long agentId) {
        return getAgentComments(agentId, null);
    }

    /**
     * 获取评论的回复列表
     *
     * @param commentId 评论ID
     * @param username  当前用户名（用于填充点赞状态）
     * @return 回复列表
     */
    List<AgentCommentEntity> getCommentReplies(Long commentId, String username);

    /**
     * 获取评论的回复列表（兼容旧接口）
     *
     * @param commentId 评论ID
     * @return 回复列表
     */
    default List<AgentCommentEntity> getCommentReplies(Long commentId) {
        return getCommentReplies(commentId, null);
    }

    /**
     * 获取评论的回复列表VO
     *
     * @param commentId 评论ID
     * @param username  当前用户名（用于填充点赞状态）
     * @return 回复VO列表
     */
    List<AgentCommentVO> getCommentRepliesVO(Long commentId, String username);

    /**
     * 获取用户的评论列表
     *
     * @param username 用户名
     * @return 评论列表
     */
    List<AgentCommentEntity> getUserComments(String username);

    /**
     * 获取用户的评论列表VO
     *
     * @param username 用户名
     * @return 评论VO列表
     */
    List<AgentCommentVO> getUserCommentsVO(String username);

    List<Long> findAgentIdsCommentedByUser(String username);

    /**
     * 获取智能体的评论数
     *
     * @param agentId 智能体ID
     * @return 评论数
     */
    int getCommentCount(Long agentId);

    int countByUsernameAndDistinctAgent(String username);

    /**
     * 填充评论的回复列表
     *
     * @param comments 评论列表
     * @param username 当前用户名（用于填充点赞状态）
     */
    void fillCommentReplies(List<AgentCommentEntity> comments, String username);

    /**
     * 填充评论的回复列表（兼容旧接口）
     *
     * @param comments 评论列表
     */
    default void fillCommentReplies(List<AgentCommentEntity> comments) {
        fillCommentReplies(comments, null);
    }

    /**
     * 填充评论的扩展信息（点赞数、回复数、用户点赞状态等）
     *
     * @param comments 评论列表
     * @param username 当前用户名
     */
    void fillCommentExtendedInfo(List<AgentCommentEntity> comments, String username);

    Map<Long, Integer> batchGetCommentCounts(List<Long> agentIds);

    /**
     * 分页获取智能体的评论列表
     *
     * @param pageNum  页码
     * @param pageSize 页大小
     * @param agentId  智能体ID
     * @param parentId 父评论ID，为null时查询顶级评论，不为null时查询指定父评论的回复
     * @param username 当前用户名（用于填充点赞状态）
     * @return 分页评论列表
     */
    PageInfo<AgentCommentEntity> pageAgentComments(int pageNum, int pageSize, Long agentId, Long parentId, String username);

    /**
     * 分页获取智能体的评论列表VO
     *
     * @param pageNum  页码
     * @param pageSize 页大小
     * @param agentId  智能体ID
     * @param parentId 父评论ID，为null时查询顶级评论，不为null时查询指定父评论的回复
     * @param username 当前用户名（用于填充点赞状态）
     * @return 分页评论VO列表
     */
    PageInfo<AgentCommentVO> pageAgentCommentsVO(int pageNum, int pageSize, Long agentId, Long parentId, String username);

    /**
     * 后台分页获取智能体的评论列表
     *
     * @param pageNum  页码
     * @param pageSize 页大小
     * @param agentIds 智能体ID列表
     * @param parentId 父评论ID，为null时查询顶级评论，不为null时查询指定父评论的回复
     * @param status   评论状态
     * @param username 用户名
     * @return 分页评论列表
     */
    PageInfo<AgentCommentEntity> pageAgentCommentsAdmin(int pageNum, int pageSize, List<Long> agentIds, Long parentId, Integer status, String username);
}