package com.gw.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gw.agent.entity.AgentCommentLikeEntity;

import java.util.List;
import java.util.Map;

/**
 * 智能体评论点赞服务接口
 */
public interface AgentCommentLikeService extends IService<AgentCommentLikeEntity> {

    /**
     * 点赞评论
     *
     * @param commentId 评论ID
     * @param username  用户名
     */
    void addLike(Long commentId, String username);

    /**
     * 取消点赞评论
     *
     * @param commentId 评论ID
     * @param username  用户名
     */
    void removeLike(Long commentId, String username);

    /**
     * 切换点赞状态
     *
     * @param commentId 评论ID
     * @param username  用户名
     * @return 点赞状态：true-已点赞，false-未点赞
     */
    boolean toggleLike(Long commentId, String username);

    /**
     * 获取评论点赞数
     *
     * @param commentId 评论ID
     * @return 点赞数
     */
    int getLikeCount(Long commentId);

    /**
     * 批量获取评论点赞数
     *
     * @param commentIds 评论ID列表
     * @return 评论ID和点赞数的映射
     */
    Map<Long, Integer> batchGetLikeCount(List<Long> commentIds);

    /**
     * 判断用户是否已点赞评论
     *
     * @param commentId 评论ID
     * @param username  用户名
     * @return 是否已点赞：1-已点赞，0-未点赞
     */
    int isLiked(Long commentId, String username);

    /**
     * 批量判断用户是否已点赞评论
     *
     * @param commentIds 评论ID列表
     * @param username   用户名
     * @return 评论ID和点赞状态的映射
     */
    Map<Long, Integer> batchIsLiked(List<Long> commentIds, String username);

    /**
     * 获取用户点赞的评论列表
     *
     * @param username 用户名
     * @return 点赞的评论列表
     */
    List<AgentCommentLikeEntity> getUserLikedComments(String username);
}
