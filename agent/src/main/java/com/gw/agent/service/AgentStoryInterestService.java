package com.gw.agent.service;

import java.util.List;

public interface AgentStoryInterestService {
    int getInterestLevel(Long storyId, String username);

    boolean markInterest(Long storyId, String username, Integer interestLevel, Integer source);

    boolean unmarkInterest(Long storyId, String username);

    int getInterestCount(Long storyId);

    List<Long> getInterestedStoryIds(String username);

    List<String> getInterestedUsernames(Long storyId);

    List<Long> findUnmarkedByUsername(String username);
}