package com.gw.agent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gw.agent.constant.AgentCacheConstant;
import com.gw.agent.entity.AgentCountEntity;
import com.gw.agent.entity.AgentEntity;
import com.gw.agent.entity.AgentFavoriteEntity;
import com.gw.agent.mapper.sql.AgentFavoriteMapper;
import com.gw.agent.mapper.sql.AgentMapper;
import com.gw.agent.service.AgentFavoriteService;
import com.gw.agent.service.AgentStatisticsService;
import com.gw.common.notify.constant.NotifyConstant;
import com.gw.common.notify.dto.InteractiveNotifySubmitDTO;
import com.gw.common.notify.service.NotifyProxyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 智能体收藏服务实现
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class AgentFavoriteServiceImpl extends ServiceImpl<AgentFavoriteMapper, AgentFavoriteEntity> implements AgentFavoriteService {
    private final NotifyProxyService notifyProxyService;
    private final AgentMapper agentMapper;
    private final String CacheCntValue = "agentFavoriteCnt";
    private final String CacheIsFavoriteValue = "agentIsFavorite";
    private final AgentStatisticsService statisticsService;

    @Override
    @Caching(evict = {
            @CacheEvict(value = CacheCntValue, key = "'user:' + #username"),
            @CacheEvict(value = CacheCntValue, key = "'agent:' + #agentId"),
            @CacheEvict(value = CacheIsFavoriteValue, key = "'favoriteAgentId:' + ':' + #username"),
            @CacheEvict(value = AgentCacheConstant.AGENT_CACHE, key = "'id:' + #agentId"),
            @CacheEvict(value = AgentCacheConstant.AGENT_PAGE_CACHE, allEntries = true),
            @CacheEvict(value = CacheIsFavoriteValue, key = "'is:' + #agentId + ':' + #username")
    })
    public void addFavorite(Long agentId, String username) {
        Optional<AgentEntity> agent = agentMapper.findById(agentId);
        if (agent.isEmpty()) {
            log.error("记录智能体{}使用失败，智能体不存在", agentId);
            return;
        }
        AgentEntity entity = agent.get();
        // 查询是否已存在收藏记录
        Optional<AgentFavoriteEntity> existingFavorite = this.baseMapper.findByAgentIdAndUsername(agentId,
                username);

        if (existingFavorite.isPresent()) {
            // 如果已存在但状态为取消收藏，则更新状态为已收藏
            AgentFavoriteEntity favorite = existingFavorite.get();
            if (favorite.getStatus() == 0) {
                favorite.setStatus(1);
                this.baseMapper.updateById(favorite);
                InteractiveNotifySubmitDTO interactiveNotifySubmitDTO = new InteractiveNotifySubmitDTO(
                        "{nickname}收藏了我创作的" + "\"" + entity.getName() + "\"",
                        NotifyConstant.INTERACTIVE_AGENT_FAVORITE,
                        agentId,
                        entity.getCreator(), username);
                notifyProxyService.insertInteractiveMessage(interactiveNotifySubmitDTO);
                statisticsService.updateFavoriteCount(agentId, 1);
            }
        } else {
            // 创建新地收藏记录
            AgentFavoriteEntity favorite = new AgentFavoriteEntity();
            favorite.setAgentId(agentId);
            favorite.setUsername(username);
            favorite.setStatus(1);
            favorite.setCreateTime(LocalDateTime.now());
            this.baseMapper.insert(favorite);
            InteractiveNotifySubmitDTO interactiveNotifySubmitDTO = new InteractiveNotifySubmitDTO(
                    "{nickname}收藏了我创作的" + "\"" + entity.getName() + "\"",
                    NotifyConstant.INTERACTIVE_AGENT_FAVORITE,
                    agentId,
                    entity.getCreator(), username);
            notifyProxyService.insertInteractiveMessage(interactiveNotifySubmitDTO);
            statisticsService.updateFavoriteCount(agentId, 1);
        }
        agentMapper.updatePopularity(agentId, (int) (Math.random() * 41 + 10));
    }

    @Override
    @Caching(evict = {
            @CacheEvict(value = CacheCntValue, key = "'user:' + #username"),
            @CacheEvict(value = CacheCntValue, key = "'agent:' + #agentId"),
            @CacheEvict(value = CacheIsFavoriteValue, key = "'favoriteAgentId:' + ':' + #username"),
            @CacheEvict(value = AgentCacheConstant.AGENT_CACHE, key = "'id:' + #agentId"),
            @CacheEvict(value = AgentCacheConstant.AGENT_PAGE_CACHE, allEntries = true),
            @CacheEvict(value = CacheIsFavoriteValue, key = "'is:' + #agentId + ':' + #username")
    })
    public boolean removeFavorite(Long agentId, String username) {
        Optional<AgentFavoriteEntity> existingFavorite = this.baseMapper.findByAgentIdAndUsername(agentId,
                username);

        if (existingFavorite.isPresent()) {
            AgentFavoriteEntity favorite = existingFavorite.get();
            // 更新状态为取消收藏
            favorite.setStatus(0);
            statisticsService.updateFavoriteCount(agentId, -1);
            return this.baseMapper.updateById(favorite) > 0;
        }

        return false;
    }

    @Override
    @Cacheable(value = CacheCntValue, key = "'agent:' + #agentId")
    public int countByAgentId(Long agentId) {
        return this.baseMapper.countByAgentId(agentId);
    }

    @Override
    @Cacheable(value = CacheCntValue, key = "'user:' + #username")
    public int countByUsername(String username) {
        return this.baseMapper.countByUsername(username);
    }

    @Override
    public List<AgentFavoriteEntity> getUserFavorites(String username) {
        return this.baseMapper.findByUsername(username);
    }

    @Cacheable(value = CacheIsFavoriteValue, key = "'favoriteAgentId:' + ':' + #username")
    @Override
    public List<Long> findUserFavoriteAgentIds(String username) {
        return this.baseMapper.findUserFavoriteAgentIds(username);
    }

    @Override
    @Cacheable(value = CacheIsFavoriteValue, key = "'is:' + #agentId + ':' + #username")
    public Integer isFavorite(Long agentId, String username) {
        Optional<AgentFavoriteEntity> favorite = this.baseMapper.findByAgentIdAndUsername(agentId, username);
        return favorite.map(AgentFavoriteEntity::getStatus).orElse(0);
    }

    @Override
    public List<AgentFavoriteEntity> getAgentFavorites(Long agentId) {
        return this.baseMapper.findByAgentId(agentId);
    }


    /**
     * 批量获取收藏数
     */
    @Override
    public Map<Long, Integer> batchGetFavoriteCounts(List<Long> agentIds) {
        if (CollectionUtils.isEmpty(agentIds)) {
            return Collections.emptyMap();
        }
        List<AgentCountEntity> entityList = this.baseMapper.batchCountByAgentIds(agentIds);
        return entityList.stream()
                .collect(Collectors.toMap(
                        AgentCountEntity::getAgentId,
                        AgentCountEntity::getCount,
                        (v1, v2) -> v1,
                        HashMap::new
                ));
    }
}