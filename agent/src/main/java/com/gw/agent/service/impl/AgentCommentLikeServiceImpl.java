package com.gw.agent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gw.agent.entity.AgentCommentEntity;
import com.gw.agent.entity.AgentCommentLikeEntity;
import com.gw.agent.mapper.sql.AgentCommentLikeMapper;
import com.gw.agent.mapper.sql.AgentCommentMapper;
import com.gw.agent.service.AgentCommentLikeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 智能体评论点赞服务实现
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class AgentCommentLikeServiceImpl extends ServiceImpl<AgentCommentLikeMapper, AgentCommentLikeEntity> implements AgentCommentLikeService {

    private final String CACHE_LIKE_COUNT = "commentLikeCount";
    private final String CACHE_IS_LIKED = "commentIsLiked";

    private final AgentCommentMapper agentCommentMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {CACHE_LIKE_COUNT, CACHE_IS_LIKED}, key = "#commentId")
    public void addLike(Long commentId, String username) {
        Optional<AgentCommentLikeEntity> existingLike = this.baseMapper.findByCommentIdAndUsername(commentId, username);
        
        if (existingLike.isPresent()) {
            AgentCommentLikeEntity likeEntity = existingLike.get();
            // 如果已经点赞，直接返回
            if (likeEntity.getStatus() == 1) {
                return;
            }
            // 如果之前取消过点赞，更新状态为已点赞
            this.baseMapper.updateStatus(likeEntity.getId(), 1);
            // 更新评论的点赞数量
            agentCommentMapper.updateLikeCount(commentId, 1);
            // 同步更新父评论的点赞数量
            //updateParentCommentLikeCount(commentId, 1);
        } else {
            // 创建新的点赞记录
            AgentCommentLikeEntity newLike = new AgentCommentLikeEntity();
            newLike.setCommentId(commentId);
            newLike.setUsername(username);
            newLike.setStatus(1);
            this.baseMapper.insert(newLike);
            // 更新评论的点赞数量
            agentCommentMapper.updateLikeCount(commentId, 1);
            // 同步更新父评论的点赞数量
            //updateParentCommentLikeCount(commentId, 1);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {CACHE_LIKE_COUNT, CACHE_IS_LIKED}, key = "#commentId")
    public void removeLike(Long commentId, String username) {
        Optional<AgentCommentLikeEntity> existingLike = this.baseMapper.findByCommentIdAndUsername(commentId, username);
        
        if (existingLike.isPresent()) {
            AgentCommentLikeEntity likeEntity = existingLike.get();
            // 只有当前状态是已点赞时才需要取消点赞
            if (likeEntity.getStatus() == 1) {
                // 更新状态为取消点赞
                this.baseMapper.updateStatus(likeEntity.getId(), 0);
                // 更新评论的点赞数量
                agentCommentMapper.updateLikeCount(commentId, -1);
                // 同步更新父评论的点赞数量
                //updateParentCommentLikeCount(commentId, -1);
            }
        }
    }

    @Override
    @Transactional
    public boolean toggleLike(Long commentId, String username) {
        Optional<AgentCommentLikeEntity> existingLike = this.baseMapper.findByCommentIdAndUsername(commentId, username);
        
        if (existingLike.isPresent()) {
            AgentCommentLikeEntity likeEntity = existingLike.get();
            int oldStatus = likeEntity.getStatus();
            int newStatus = oldStatus == 1 ? 0 : 1;
            this.baseMapper.updateStatus(likeEntity.getId(), newStatus);

            // 更新评论的点赞数量
            int increment = newStatus == 1 ? 1 : -1;
            agentCommentMapper.updateLikeCount(commentId, increment);
            // 同步更新父评论的点赞数量
            //updateParentCommentLikeCount(commentId, increment);

            return newStatus == 1;
        } else {
            // 创建新的点赞记录
            AgentCommentLikeEntity newLike = new AgentCommentLikeEntity();
            newLike.setCommentId(commentId);
            newLike.setUsername(username);
            newLike.setStatus(1);
            this.baseMapper.insert(newLike);

            // 更新评论的点赞数量
            agentCommentMapper.updateLikeCount(commentId, 1);
            // 同步更新父评论的点赞数量
            //updateParentCommentLikeCount(commentId, 1);

            return true;
        }
    }

    @Override
    @Cacheable(value = CACHE_LIKE_COUNT, key = "#commentId")
    public int getLikeCount(Long commentId) {
        return this.baseMapper.countByCommentId(commentId);
    }

    @Override
    public Map<Long, Integer> batchGetLikeCount(List<Long> commentIds) {
        if (commentIds == null || commentIds.isEmpty()) {
            return new HashMap<>();
        }
        
        List<Map<String, Object>> results = this.baseMapper.batchCountByCommentIds(commentIds);
        return results.stream()
                .collect(Collectors.toMap(
                        map -> ((Number) map.get("comment_id")).longValue(),
                        map -> ((Number) map.get("like_count")).intValue()
                ));
    }

    @Override
    @Cacheable(value = CACHE_IS_LIKED, key = "#commentId + ':' + #username")
    public int isLiked(Long commentId, String username) {
        Optional<AgentCommentLikeEntity> like = this.baseMapper.findByCommentIdAndUsername(commentId, username);
        return like.map(entity -> entity.getStatus()).orElse(0);
    }

    @Override
    public Map<Long, Integer> batchIsLiked(List<Long> commentIds, String username) {
        if (commentIds == null || commentIds.isEmpty()) {
            return new HashMap<>();
        }
        
        List<Map<String, Object>> results = this.baseMapper.batchFindUserLikeStatus(commentIds, username);
        return results.stream()
                .collect(Collectors.toMap(
                        map -> ((Number) map.get("comment_id")).longValue(),
                        map -> ((Number) map.get("status")).intValue()
                ));
    }

    @Override
    public List<AgentCommentLikeEntity> getUserLikedComments(String username) {
        return this.baseMapper.findByUsername(username);
    }

    /**
     * 更新父评论的点赞数量
     *
     * @param commentId 当前评论ID
     * @param increment 点赞数增量（1表示增加，-1表示减少）
     */
    private void updateParentCommentLikeCount(Long commentId, int increment) {
        try {
            // 查询当前评论的信息，获取父评论ID
            Optional<AgentCommentEntity> currentComment = agentCommentMapper.findById(commentId);
            if (currentComment.isPresent() && currentComment.get().getParentId() != null) {
                Long parentId = currentComment.get().getParentId();
                // 更新父评论的点赞数量
                agentCommentMapper.updateLikeCount(parentId, increment);
                log.debug("更新父评论{}的点赞数量，增量：{}", parentId, increment);
            }
        } catch (Exception e) {
            log.error("更新父评论点赞数量失败，评论ID：{}，增量：{}", commentId, increment, e);
            // 这里不抛出异常，避免影响主要的点赞逻辑
        }
    }
}
