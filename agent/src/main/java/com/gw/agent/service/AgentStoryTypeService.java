package com.gw.agent.service;

import com.gw.agent.entity.AgentStoryTypeEntity;

import java.util.List;
import java.util.Map;

/**
 * 智能体剧情类型服务接口
 */
public interface AgentStoryTypeService {

    /**
     * 插入新的剧情类型
     *
     * @param entity 剧情类型实体
     */
    void insert(AgentStoryTypeEntity entity);

    /**
     * 更新剧情类型
     *
     * @param entity 剧情类型实体
     */
    void update(AgentStoryTypeEntity entity);

    /**
     * 删除剧情类型（软删除）
     *
     * @param entity 剧情类型实体
     */
    void delete(AgentStoryTypeEntity entity);

    /**
     * 根据ID查找剧情类型
     *
     * @param id 类型ID
     * @return 剧情类型实体
     */
    AgentStoryTypeEntity findById(Long id);

    /**
     * 查找所有剧情类型
     *
     * @return 剧情类型列表
     */
    List<AgentStoryTypeEntity> findAll();

    /**
     * 检查名称是否存在
     *
     * @param name 类型名称
     * @return 是否存在
     */
    boolean isNameExist(String name);

    /**
     * 获取剧情类型映射
     *
     * @return 类型ID到实体的映射
     */
    Map<Long, AgentStoryTypeEntity> findTypesMap();

    /**
     * 获取热门剧情类型
     *
     * @param limit 限制数量
     * @return 热门类型列表
     */
    List<AgentStoryTypeEntity> findHotTypes(Integer limit);

    /**
     * 增加类型使用次数
     *
     * @param id 类型ID
     */
    void increaseUseCount(Long id);
}