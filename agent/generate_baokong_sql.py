# -*- coding: utf-8 -*-
import codecs

def generate_sql_from_baokong_words():
    # 读取暴恐敏感词文件
    with codecs.open('src/main/resources/sw_暴恐.txt', 'r', 'utf-8') as f:
        words = f.readlines()
    
    # 去除换行符并去重
    words = [word.strip() for word in words if word.strip()]
    unique_words = list(set(words))
    
    # 分类ID：4 (违法犯罪)
    category_id = 4
    
    # 敏感词级别：3-高级（暴恐类属于高级敏感词）
    level = 3
    
    # 处理动作：1-拦截
    action = 1
    
    # 状态：1-启用
    status = 1
    
    # 创建者和更新者
    creator = 'system'
    updater = 'system'
    
    # 生成SQL INSERT语句
    with codecs.open('insert_baokong_sensitive_words.sql', 'w', 'utf-8') as f:
        f.write("-- 暴恐敏感词插入SQL\n")
        for word in unique_words:
            # 转义单引号
            escaped_word = word.replace("'", "''")
            sql = f"INSERT INTO t_sensitive_word (word, category_id, level, action, status, creator, updater, remark) " \
                  f"VALUES ('{escaped_word}', {category_id}, {level}, {action}, {status}, '{creator}', '{updater}', '暴恐类敏感词');\n"
            f.write(sql)
    
    print(f"共生成 {len(unique_words)} 条不重复的敏感词SQL INSERT语句")

if __name__ == "__main__":
    generate_sql_from_baokong_words()