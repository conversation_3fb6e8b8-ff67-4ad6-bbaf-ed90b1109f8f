# 灵犀伴侣后台微服务 JVM 配置指南

## 📋 概述

本文档记录了灵犀伴侣后台7个微服务的JVM配置方案，总内存分配为10GB，设计目标为支持1000用户并发访问。

### 服务架构概览

- **agent-service**: 智能体管理服务
- **chat-service**: 与智能体的聊天服务（核心）
- **gateway**: API网关服务
- **membership-service**: 会员管理服务
- **multi-chat-service**: 智能体群聊服务（核心）
- **notify-service**: 通知服务
- **user-service**: 用户管理服务

## 🎯 内存分配策略

| 服务名称               | 分配内存     | 端口   | 优先级 | 分配理由                                   |
|--------------------|----------|------|-----|----------------------------------------|
| chat-service       | 2.5GB    | 9085 | 高   | 核心聊天服务，支持1000并发，WebSocket + Reactive架构 |
| multi-chat-service | 2.5GB    | 8084 | 高   | 核心群聊服务，支持1000并发，MongoDB密集操作            |
| gateway            | 1.5GB    | 8000 | 高   | 流量入口，所有请求都经过，Reactive架构                |
| agent-service      | 1.2GB    | 8006 | 中   | 智能体管理，大量缓存配置，业务核心                      |
| user-service       | 1GB      | 8009 | 中   | 用户认证服务，JWT处理，中等负载                      |
| membership-service | 800MB    | 8008 | 低   | 会员管理和支付，相对较轻                           |
| notify-service     | 500MB    | 8007 | 低   | 通知服务，最轻量                               |
| **总计**             | **10GB** |      |     |                                        |

## 🚀 详细JVM配置

### 1. Gateway Service (1.5GB)

```bash
JAVA_OPTS="-server \
-Xms1536m \
-Xmx1536m \
-XX:+UseG1GC \
-XX:MaxGCPauseMillis=100 \
-XX:G1HeapRegionSize=8m \
-XX:InitiatingHeapOccupancyPercent=35 \
-XX:G1MixedGCCountTarget=8 \
-XX:MetaspaceSize=128m \
-XX:MaxMetaspaceSize=256m \
-XX:MaxDirectMemorySize=512m \
-XX:+UseStringDeduplication \
-XX:+DisableExplicitGC \
-Dspring.reactor.netty.pool.maxConnections=1500 \
-Dspring.reactor.netty.pool.maxIdleTime=10000 \
-Dspring.reactor.netty.pool.maxLifeTime=60000 \
-Dreactor.netty.ioWorkerCount=8 \
-Dreactor.netty.pool.leasingStrategy=lifo \
-Dfile.encoding=UTF-8"
```

### 2. Chat Service (2.5GB)

```bash
JAVA_OPTS="-server \
-Xms2560m \
-Xmx2560m \
-XX:+UseG1GC \
-XX:MaxGCPauseMillis=150 \
-XX:G1HeapRegionSize=16m \
-XX:InitiatingHeapOccupancyPercent=35 \
-XX:G1MixedGCCountTarget=8 \
-XX:G1NewSizePercent=30 \
-XX:G1MaxNewSizePercent=40 \
-XX:MetaspaceSize=192m \
-XX:MaxMetaspaceSize=384m \
-XX:MaxDirectMemorySize=768m \
-XX:+UseStringDeduplication \
-XX:+DisableExplicitGC \
-Dspring.reactor.netty.pool.maxConnections=2000 \
-Dspring.reactor.netty.pool.maxIdleTime=15000 \
-Dspring.reactor.netty.pool.maxLifeTime=120000 \
-Dreactor.netty.ioWorkerCount=8 \
-Dreactor.netty.pool.leasingStrategy=lifo \
-Dspring.data.mongodb.field-naming-strategy=org.springframework.data.mapping.model.SnakeCaseFieldNamingStrategy \
-Dspring.redis.lettuce.pool.max-active=50 \
-Dfile.encoding=UTF-8"
```

### 3. Multi-Chat Service (2.5GB)

```bash
JAVA_OPTS="-server \
-Xms2560m \
-Xmx2560m \
-XX:+UseG1GC \
-XX:MaxGCPauseMillis=150 \
-XX:G1HeapRegionSize=16m \
-XX:InitiatingHeapOccupancyPercent=35 \
-XX:G1MixedGCCountTarget=8 \
-XX:G1NewSizePercent=30 \
-XX:G1MaxNewSizePercent=40 \
-XX:MetaspaceSize=192m \
-XX:MaxMetaspaceSize=384m \
-XX:MaxDirectMemorySize=768m \
-XX:+UseStringDeduplication \
-XX:+DisableExplicitGC \
-Dspring.data.mongodb.repositories.type=reactive \
-Dspring.data.redis.lettuce.pool.max-active=100 \
-Dspring.data.redis.lettuce.pool.max-idle=20 \
-Dspring.data.redis.timeout=5000ms \
-Dfile.encoding=UTF-8"
```

### 4. Agent Service (1.2GB)

```bash
JAVA_OPTS="-server \
-Xms1228m \
-Xmx1228m \
-XX:+UseG1GC \
-XX:MaxGCPauseMillis=200 \
-XX:G1HeapRegionSize=8m \
-XX:InitiatingHeapOccupancyPercent=40 \
-XX:G1MixedGCCountTarget=8 \
-XX:MetaspaceSize=128m \
-XX:MaxMetaspaceSize=256m \
-XX:MaxDirectMemorySize=256m \
-XX:+UseStringDeduplication \
-XX:+DisableExplicitGC \
-Dspring.datasource.hikari.maximum-pool-size=20 \
-Dspring.datasource.hikari.minimum-idle=5 \
-Dspring.redis.lettuce.pool.max-active=20 \
-Dspring.cache.caffeine.spec=maximumSize=10000,expireAfterWrite=30m \
-Dfile.encoding=UTF-8"
```

### 5. User Service (1GB)

```bash
JAVA_OPTS="-server \
-Xms1024m \
-Xmx1024m \
-XX:+UseG1GC \
-XX:MaxGCPauseMillis=200 \
-XX:G1HeapRegionSize=8m \
-XX:InitiatingHeapOccupancyPercent=40 \
-XX:MetaspaceSize=128m \
-XX:MaxMetaspaceSize=256m \
-XX:MaxDirectMemorySize=256m \
-XX:+UseStringDeduplication \
-XX:+DisableExplicitGC \
-Dspring.datasource.hikari.maximum-pool-size=15 \
-Dspring.datasource.hikari.minimum-idle=3 \
-Dspring.redis.lettuce.pool.max-active=15 \
-Dspring.security.jwt.cache-size=5000 \
-Dfile.encoding=UTF-8"
```

### 6. Membership Service (800MB)

```bash
JAVA_OPTS="-server \
-Xms819m \
-Xmx819m \
-XX:+UseG1GC \
-XX:MaxGCPauseMillis=200 \
-XX:G1HeapRegionSize=8m \
-XX:InitiatingHeapOccupancyPercent=45 \
-XX:MetaspaceSize=96m \
-XX:MaxMetaspaceSize=192m \
-XX:MaxDirectMemorySize=128m \
-XX:+UseStringDeduplication \
-XX:+DisableExplicitGC \
-Dspring.datasource.hikari.maximum-pool-size=10 \
-Dspring.datasource.hikari.minimum-idle=2 \
-Dspring.redis.lettuce.pool.max-active=10 \
-Dfile.encoding=UTF-8"
```

### 7. Notify Service (500MB)

```bash
JAVA_OPTS="-server \
-Xms512m \
-Xmx512m \
-XX:+UseG1GC \
-XX:MaxGCPauseMillis=200 \
-XX:G1HeapRegionSize=4m \
-XX:InitiatingHeapOccupancyPercent=50 \
-XX:MetaspaceSize=64m \
-XX:MaxMetaspaceSize=128m \
-XX:MaxDirectMemorySize=64m \
-XX:+UseStringDeduplication \
-XX:+DisableExplicitGC \
-Dspring.datasource.hikari.maximum-pool-size=5 \
-Dspring.datasource.hikari.minimum-idle=1 \
-Dspring.redis.lettuce.pool.max-active=8 \
-Dfile.encoding=UTF-8"
```

## 📝 启动脚本

### Bash 启动脚本 (Linux)

```bash
#!/bin/bash
# start-all-services.sh

# 默认配置
PROFILE=${1:-"prod"}
ENABLE_GC_LOG=${2:-false}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 服务配置数组
declare -A SERVICES
SERVICES[gateway]="8000:gateway/target/gateway-1.0.0.jar:1536m"
SERVICES[chat-service]="9085:chat-service/target/chat-service-1.0.0.jar:2560m"
SERVICES[multi-chat-service]="8084:multi-chat-service/target/multi-chat-service-1.0.0.jar:2560m"
SERVICES[agent-service]="8006:agent/target/agent-service-1.0.0.jar:1228m"
SERVICES[user-service]="8009:user/target/user-service-1.0.0.jar:1024m"
SERVICES[membership-service]="8008:membership-service/target/membership-service-1.0.0.jar:819m"
SERVICES[notify-service]="8007:notify/target/notify-service-1.0.0.jar:512m"

# JVM配置函数
get_java_opts() {
    local service=$1
    local memory=$2
    
    case $service in
        "gateway")
            echo "-server -Xms${memory} -Xmx${memory} -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:G1HeapRegionSize=8m -XX:InitiatingHeapOccupancyPercent=35 -XX:G1MixedGCCountTarget=8 -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m -XX:MaxDirectMemorySize=512m -XX:+UseStringDeduplication -XX:+DisableExplicitGC -Dspring.reactor.netty.pool.maxConnections=1500 -Dspring.reactor.netty.pool.maxIdleTime=10000 -Dspring.reactor.netty.pool.maxLifeTime=60000 -Dreactor.netty.ioWorkerCount=8 -Dreactor.netty.pool.leasingStrategy=lifo -Dfile.encoding=UTF-8"
            ;;
        "chat-service")
            echo "-server -Xms${memory} -Xmx${memory} -XX:+UseG1GC -XX:MaxGCPauseMillis=150 -XX:G1HeapRegionSize=16m -XX:InitiatingHeapOccupancyPercent=35 -XX:G1MixedGCCountTarget=8 -XX:G1NewSizePercent=30 -XX:G1MaxNewSizePercent=40 -XX:MetaspaceSize=192m -XX:MaxMetaspaceSize=384m -XX:MaxDirectMemorySize=768m -XX:+UseStringDeduplication -XX:+DisableExplicitGC -Dspring.reactor.netty.pool.maxConnections=2000 -Dspring.reactor.netty.pool.maxIdleTime=15000 -Dspring.reactor.netty.pool.maxLifeTime=120000 -Dreactor.netty.ioWorkerCount=8 -Dreactor.netty.pool.leasingStrategy=lifo -Dspring.data.mongodb.field-naming-strategy=org.springframework.data.mapping.model.SnakeCaseFieldNamingStrategy -Dspring.redis.lettuce.pool.max-active=50 -Dfile.encoding=UTF-8"
            ;;
        "multi-chat-service")
            echo "-server -Xms${memory} -Xmx${memory} -XX:+UseG1GC -XX:MaxGCPauseMillis=150 -XX:G1HeapRegionSize=16m -XX:InitiatingHeapOccupancyPercent=35 -XX:G1MixedGCCountTarget=8 -XX:G1NewSizePercent=30 -XX:G1MaxNewSizePercent=40 -XX:MetaspaceSize=192m -XX:MaxMetaspaceSize=384m -XX:MaxDirectMemorySize=768m -XX:+UseStringDeduplication -XX:+DisableExplicitGC -Dspring.data.mongodb.repositories.type=reactive -Dspring.data.redis.lettuce.pool.max-active=100 -Dspring.data.redis.lettuce.pool.max-idle=20 -Dspring.data.redis.timeout=5000ms -Dfile.encoding=UTF-8"
            ;;
        "agent-service")
            echo "-server -Xms${memory} -Xmx${memory} -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:G1HeapRegionSize=8m -XX:InitiatingHeapOccupancyPercent=40 -XX:G1MixedGCCountTarget=8 -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m -XX:MaxDirectMemorySize=256m -XX:+UseStringDeduplication -XX:+DisableExplicitGC -Dspring.datasource.hikari.maximum-pool-size=20 -Dspring.datasource.hikari.minimum-idle=5 -Dspring.redis.lettuce.pool.max-active=20 -Dspring.cache.caffeine.spec=maximumSize=10000,expireAfterWrite=30m -Dfile.encoding=UTF-8"
            ;;
        "user-service")
            echo "-server -Xms${memory} -Xmx${memory} -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:G1HeapRegionSize=8m -XX:InitiatingHeapOccupancyPercent=40 -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m -XX:MaxDirectMemorySize=256m -XX:+UseStringDeduplication -XX:+DisableExplicitGC -Dspring.datasource.hikari.maximum-pool-size=15 -Dspring.datasource.hikari.minimum-idle=3 -Dspring.redis.lettuce.pool.max-active=15 -Dspring.security.jwt.cache-size=5000 -Dfile.encoding=UTF-8"
            ;;
        "membership-service")
            echo "-server -Xms${memory} -Xmx${memory} -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:G1HeapRegionSize=8m -XX:InitiatingHeapOccupancyPercent=45 -XX:MetaspaceSize=96m -XX:MaxMetaspaceSize=192m -XX:MaxDirectMemorySize=128m -XX:+UseStringDeduplication -XX:+DisableExplicitGC -Dspring.datasource.hikari.maximum-pool-size=10 -Dspring.datasource.hikari.minimum-idle=2 -Dspring.redis.lettuce.pool.max-active=10 -Dfile.encoding=UTF-8"
            ;;
        "notify-service")
            echo "-server -Xms${memory} -Xmx${memory} -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:G1HeapRegionSize=4m -XX:InitiatingHeapOccupancyPercent=50 -XX:MetaspaceSize=64m -XX:MaxMetaspaceSize=128m -XX:MaxDirectMemorySize=64m -XX:+UseStringDeduplication -XX:+DisableExplicitGC -Dspring.datasource.hikari.maximum-pool-size=5 -Dspring.datasource.hikari.minimum-idle=1 -Dspring.redis.lettuce.pool.max-active=8 -Dfile.encoding=UTF-8"
            ;;
    esac
}

# 创建日志目录
mkdir -p logs

echo -e "${CYAN}=== 灵犀伴侣后台服务启动脚本 ===${NC}"
echo -e "${YELLOW}配置文件: $PROFILE${NC}"
echo -e "${YELLOW}GC日志: $([ "$ENABLE_GC_LOG" = "true" ] && echo "启用" || echo "禁用")${NC}"
echo ""

# 启动服务
for service_name in "${!SERVICES[@]}"; do
    IFS=':' read -r port jar_path memory <<< "${SERVICES[$service_name]}"
    
    echo -e "${GREEN}正在启动 $service_name (端口: $port, 内存: $memory)...${NC}"
    
    # 检查JAR文件是否存在
    if [ ! -f "$jar_path" ]; then
        echo -e "${RED}错误: JAR文件不存在 - $jar_path${NC}"
        continue
    fi
    
    # 获取JVM选项
    java_opts=$(get_java_opts "$service_name" "$memory")
    java_opts="$java_opts -Dspring.profiles.active=$PROFILE"
    
    # 如果启用GC日志
    if [ "$ENABLE_GC_LOG" = "true" ]; then
        gc_log_path="logs/${service_name}-gc-%t.log"
        heap_dump_path="logs/${service_name}-heapdump-%t.hprof"
        java_opts="$java_opts -XX:+PrintGC -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+PrintGCApplicationStoppedTime -Xloggc:$gc_log_path -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=5 -XX:GCLogFileSize=10M -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$heap_dump_path"
    fi
    
    # 启动服务
    nohup java $java_opts -jar "$jar_path" > "logs/${service_name}.out" 2> "logs/${service_name}.err" &
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ $service_name 启动成功${NC}"
    else
        echo -e "${RED}✗ $service_name 启动失败${NC}"
    fi
    
    sleep 3
done

echo ""
echo -e "${CYAN}=== 所有服务启动完成 ===${NC}"
echo -e "${YELLOW}请等待2-3分钟服务完全启动，然后运行健康检查脚本${NC}"

# 使脚本可执行
# chmod +x start-all-services.sh
# 使用方法: ./start-all-services.sh [profile] [enable_gc_log]
# 示例: ./start-all-services.sh prod true
```

### 健康检查脚本

```bash
#!/bin/bash
# health-check.sh

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 服务配置
declare -A SERVICES
SERVICES[gateway]="8000:/actuator/health"
SERVICES[chat-service]="9085:/actuator/health"
SERVICES[multi-chat-service]="8084:/actuator/health"
SERVICES[agent-service]="8006:/actuator/health"
SERVICES[user-service]="8009:/actuator/health"
SERVICES[membership-service]="8008:/actuator/health"
SERVICES[notify-service]="8007:/actuator/health"

echo -e "${CYAN}=== 服务健康检查 ===${NC}"
echo ""

# 检查每个服务的健康状态
for service_name in "${!SERVICES[@]}"; do
    IFS=':' read -r port path <<< "${SERVICES[$service_name]}"
    url="http://localhost:${port}${path}"
    
    # 使用curl检查服务状态
    if command -v curl >/dev/null 2>&1; then
        response_code=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 "$url" 2>/dev/null)
        if [ "$response_code" = "200" ]; then
            echo -e "${GREEN}✓ $service_name (端口: $port)${NC}"
        elif [ -n "$response_code" ] && [ "$response_code" != "000" ]; then
            echo -e "${YELLOW}⚠ $service_name (端口: $port) - 状态码: $response_code${NC}"
        else
            echo -e "${RED}✗ $service_name (端口: $port) - 无法连接${NC}"
        fi
    elif command -v wget >/dev/null 2>&1; then
        if wget --quiet --timeout=10 --tries=1 --spider "$url" 2>/dev/null; then
            echo -e "${GREEN}✓ $service_name (端口: $port)${NC}"
        else
            echo -e "${RED}✗ $service_name (端口: $port) - 无法连接${NC}"
        fi
    else
        # 使用nc检查端口是否开放
        if command -v nc >/dev/null 2>&1; then
            if nc -z localhost "$port" 2>/dev/null; then
                echo -e "${YELLOW}⚠ $service_name (端口: $port) - 端口开放，但无法验证健康状态${NC}"
            else
                echo -e "${RED}✗ $service_name (端口: $port) - 端口未开放${NC}"
            fi
        else
            echo -e "${YELLOW}⚠ 无法检查 $service_name - 缺少curl、wget或nc工具${NC}"
        fi
    fi
done

echo ""
echo -e "${CYAN}=== 内存使用情况 ===${NC}"

# 检查Java进程
java_pids=$(pgrep -f 'java.*\.jar')
if [ -n "$java_pids" ]; then
    total_memory=0
    echo "$java_pids" | while read -r pid; do
        if [ -n "$pid" ]; then
            # 获取进程内存使用情况（KB）
            memory_kb=$(ps -p "$pid" -o rss= 2>/dev/null | tr -d ' ')
            if [ -n "$memory_kb" ]; then
                memory_mb=$((memory_kb / 1024))
                echo -e "${WHITE}Java进程 (PID: $pid): ${memory_mb} MB${NC}"
                total_memory=$((total_memory + memory_mb))
            fi
        fi
    done
    
    # 计算总内存使用（需要重新计算）
    total_kb=$(ps -p $(echo "$java_pids" | tr '\n' ',' | sed 's/,$//') -o rss= 2>/dev/null | awk '{sum+=$1} END {print sum}')
    if [ -n "$total_kb" ]; then
        total_mb=$((total_kb / 1024))
        echo -e "${YELLOW}Java进程总内存使用: ${total_mb} MB${NC}"
    fi
else
    echo -e "${RED}未找到Java进程${NC}"
fi

# 显示系统内存使用情况
echo ""
echo -e "${CYAN}=== 系统资源概览 ===${NC}"
if command -v free >/dev/null 2>&1; then
    free -h
elif [ -f /proc/meminfo ]; then
    total_mem=$(grep MemTotal /proc/meminfo | awk '{print int($2/1024)"MB"}')
    free_mem=$(grep MemAvailable /proc/meminfo | awk '{print int($2/1024)"MB"}')
    echo "总内存: $total_mem, 可用内存: $free_mem"
fi

# 使脚本可执行
# chmod +x health-check.sh
```

### 停止服务脚本

```bash
#!/bin/bash
# stop-all-services.sh

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

echo -e "${CYAN}=== 停止所有Java服务 ===${NC}"

# 查找Java进程
java_pids=$(pgrep -f 'java.*\.jar')

if [ -n "$java_pids" ]; then
    java_count=$(echo "$java_pids" | wc -l)
    echo -e "${YELLOW}发现 $java_count 个Java进程，正在停止...${NC}"
    
    # 首先尝试优雅关闭（SIGTERM）
    echo "$java_pids" | while read -r pid; do
        if [ -n "$pid" ]; then
            echo -e "${WHITE}发送SIGTERM信号到进程 PID: $pid${NC}"
            kill "$pid" 2>/dev/null
        fi
    done
    
    echo "等待进程优雅关闭..."
    sleep 5
    
    # 检查哪些进程还在运行
    remaining_pids=$(pgrep -f 'java.*\.jar')
    
    if [ -n "$remaining_pids" ]; then
        echo -e "${YELLOW}以下进程未能优雅关闭，强制终止...${NC}"
        echo "$remaining_pids" | while read -r pid; do
            if [ -n "$pid" ]; then
                echo -e "${WHITE}强制停止进程 PID: $pid${NC}"
                kill -9 "$pid" 2>/dev/null
                if [ $? -eq 0 ]; then
                    echo -e "${GREEN}✓ 进程 $pid 已停止${NC}"
                else
                    echo -e "${RED}✗ 无法停止进程 $pid${NC}"
                fi
            fi
        done
    fi
    
    sleep 3
    
    # 最终检查
    final_check=$(pgrep -f 'java.*\.jar')
    if [ -z "$final_check" ]; then
        echo -e "${GREEN}所有Java服务已停止${NC}"
    else
        echo -e "${RED}警告: 仍有Java进程在运行${NC}"
        echo "$final_check"
    fi
else
    echo -e "${YELLOW}未找到运行中的Java进程${NC}"
fi

# 可选：清理日志文件
read -p "是否要清理日志文件? (y/N): " -r
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if [ -d "logs" ]; then
        rm -f logs/*.out logs/*.err logs/*.log
        echo -e "${GREEN}日志文件已清理${NC}"
    fi
fi

# 使脚本可执行
# chmod +x stop-all-services.sh
```

## 📊 监控和调优

### 关键参数解释

| 参数                                       | 作用           | 推荐值说明              |
|------------------------------------------|--------------|--------------------|
| `-Xms/-Xmx`                              | 初始/最大堆内存     | 设置相同值避免动态扩容        |
| `-XX:+UseG1GC`                           | 使用G1垃圾收集器    | 适合大堆内存和低延迟需求       |
| `-XX:MaxGCPauseMillis`                   | 最大GC暂停时间     | 核心服务150ms，其他200ms  |
| `-XX:G1HeapRegionSize`                   | 堆区域大小        | 内存越大设置越大           |
| `-XX:InitiatingHeapOccupancyPercent`     | 触发并发GC的堆占用阈值 | 35-50%根据服务特点调整     |
| `-XX:MetaspaceSize/-XX:MaxMetaspaceSize` | 元空间大小        | 避免类加载时内存抖动         |
| `-XX:MaxDirectMemorySize`                | 直接内存限制       | 控制NIO使用，防止OOM      |
| `-XX:+UseStringDeduplication`            | 字符串去重        | G1GC特有，节省内存        |
| `-XX:+DisableExplicitGC`                 | 禁用显式GC       | 避免代码中System.gc()调用 |

### 性能调优建议

#### 1. GC调优

```bash
# 如果GC暂停时间过长，可以调整这些参数
-XX:MaxGCPauseMillis=100          # 降低目标暂停时间
-XX:G1MixedGCCountTarget=4        # 减少混合GC次数
-XX:G1HeapWastePercent=5          # 降低堆浪费百分比
```

#### 2. 连接池调优

```bash
# 数据库连接池 (HikariCP)
-Dspring.datasource.hikari.maximum-pool-size=20
-Dspring.datasource.hikari.minimum-idle=5
-Dspring.datasource.hikari.connection-timeout=30000
-Dspring.datasource.hikari.idle-timeout=600000
-Dspring.datasource.hikari.max-lifetime=1800000

# Redis连接池 (Lettuce)
-Dspring.redis.lettuce.pool.max-active=20
-Dspring.redis.lettuce.pool.max-idle=8
-Dspring.redis.lettuce.pool.min-idle=2
-Dspring.redis.timeout=5000
```

#### 3. Reactor Netty调优

```bash
# 适用于Gateway和Chat服务
-Dspring.reactor.netty.pool.maxConnections=2000
-Dspring.reactor.netty.pool.maxIdleTime=15000
-Dspring.reactor.netty.pool.maxLifeTime=120000
-Dreactor.netty.ioWorkerCount=8
-Dreactor.netty.pool.leasingStrategy=lifo
```

### 监控指标

#### 1. JVM监控

- **堆内存使用率**: 保持在80%以下
- **GC频率和耗时**: Major GC < 1次/分钟，暂停时间 < 200ms
- **线程数**: 监控线程池使用情况
- **直接内存**: 监控NIO内存使用

#### 2. 应用监控

- **响应时间**: P95 < 500ms，P99 < 1000ms
- **QPS**: 根据业务需求监控
- **错误率**: < 0.1%
- **连接池**: 活跃连接数不超过80%

#### 3. 系统监控

- **CPU使用率**: < 80%
- **内存使用率**: < 85%
- **磁盘I/O**: 监控日志和数据文件写入
- **网络**: 监控带宽使用情况

## 🔧 故障排查

### 常见问题和解决方案

#### 1. OutOfMemoryError

```bash
# 堆内存不足
java.lang.OutOfMemoryError: Java heap space
# 解决方案: 增加-Xmx值或优化内存使用

# 元空间不足
java.lang.OutOfMemoryError: Metaspace
# 解决方案: 增加-XX:MaxMetaspaceSize值

# 直接内存不足
java.lang.OutOfMemoryError: Direct buffer memory
# 解决方案: 增加-XX:MaxDirectMemorySize值
```

#### 2. GC问题

```bash
# GC频繁
# 解决方案: 调整InitiatingHeapOccupancyPercent
-XX:InitiatingHeapOccupancyPercent=40

# GC暂停时间长
# 解决方案: 降低MaxGCPauseMillis或调整堆区域大小
-XX:MaxGCPauseMillis=100
-XX:G1HeapRegionSize=8m
```

#### 3. 连接池问题

```bash
# 数据库连接耗尽
# 解决方案: 增加连接池大小或检查连接泄漏
-Dspring.datasource.hikari.maximum-pool-size=30

# Redis连接超时
# 解决方案: 调整超时时间和连接池配置
-Dspring.redis.timeout=10000
-Dspring.redis.lettuce.pool.max-active=30
```

### 日志分析

#### 1. GC日志分析

```bash
# 使用GCViewer或GCPlot.com分析GC日志
# 关注指标:
# - 吞吐量 (Throughput)
# - 平均暂停时间 (Avg Pause Time)  
# - 最大暂停时间 (Max Pause Time)
# - 分配速率 (Allocation Rate)
```

#### 2. 应用日志分析

```bash
# 查找错误关键词
grep -iE "error|exception|failed" logs/*.log logs/*.out logs/*.err

# 监控慢查询
grep -iE "slow query|timeout" logs/*.log logs/*.out

# 检查内存使用
grep -iE "memory|gc|heap|outofmemory" logs/*.log logs/*.out

# 实时监控日志
tail -f logs/chat-service.out | grep -iE "error|exception"

# 统计错误频率
grep -c "ERROR" logs/*.log | sort -rn

# 查看最近的错误
grep "ERROR" logs/*.log | tail -20
```

## 📋 部署检查清单

### 部署前检查

- [ ] 确认Java版本 (推荐Java 11或17): `java -version`
- [ ] 确认可用内存 ≥ 12GB: `free -h`
- [ ] 检查端口是否可用: `netstat -tuln | grep -E ':(8000|8006|8007|8008|8009|8084|9085)'`
- [ ] 确认文件描述符限制: `ulimit -n` (应该 ≥ 65536)
- [ ] 确认数据库连接 (PostgreSQL, MongoDB)
- [ ] 确认Redis连接: `redis-cli ping`
- [ ] 确认Nacos注册中心可访问
- [ ] 检查磁盘空间: `df -h`
- [ ] 确认系统时区: `timedatectl status`

### 部署后检查

- [ ] 所有服务启动成功: `./health-check.sh`
- [ ] Java进程正在运行: `pgrep -f 'java.*\.jar'`
- [ ] 端口监听正常: `netstat -tuln | grep -E ':(8000|8006|8007|8008|8009|8084|9085)'`
- [ ] 健康检查通过: `curl -s http://localhost:8000/actuator/health`
- [ ] 服务注册到Nacos: 检查Nacos控制台
- [ ] 网关路由正常: 测试通过网关访问各服务
- [ ] 数据库连接正常: 检查应用日志无连接错误
- [ ] 缓存功能正常: 检查Redis连接和缓存命中
- [ ] 日志输出正常: `ls -la logs/` 确认日志文件生成
- [ ] 内存使用合理: 检查总内存使用量不超过10GB

### 性能测试

- [ ] 单服务压力测试: 使用JMeter或Apache Bench
- [ ] 整体系统压力测试: 通过网关测试所有服务
- [ ] 1000并发用户测试: 聊天和群聊服务并发测试
- [ ] 长期稳定性测试: 运行24小时以上监控内存泄漏
- [ ] 网络延迟测试: `ping` 和 `telnet` 测试服务间连接
- [ ] 数据库连接池测试: 监控连接数和响应时间
- [ ] JVM性能监控: 使用 `jstat`, `jmap`, `jstack` 等工具

## 📚 参考资料

### Java和JVM优化

- [Java G1GC调优指南](https://docs.oracle.com/javase/9/gctuning/garbage-first-garbage-collector.htm)
- [OpenJDK官方文档](https://openjdk.java.net/projects/jdk/)
- [JVM性能调优实战](https://www.oracle.com/technical-resources/articles/java/g1gc.html)

### Spring框架相关

- [Spring Boot Production Ready](https://docs.spring.io/spring-boot/docs/current/reference/html/production-ready-features.html)
- [Reactor Netty参考文档](https://projectreactor.io/docs/netty/release/reference/index.html)
- [Spring Cloud Gateway文档](https://docs.spring.io/spring-cloud-gateway/docs/current/reference/html/)

### 数据库和缓存

- [HikariCP配置优化](https://github.com/brettwooldridge/HikariCP#configuration-knobs-baby)
- [Redis性能优化指南](https://redis.io/topics/memory-optimization)
- [MongoDB性能调优](https://docs.mongodb.com/manual/administration/performance-tuning/)

### Linux系统优化

- [Linux性能优化实战](https://www.kernel.org/doc/Documentation/sysctl/)
- [TCP/IP调优参数](https://www.kernel.org/doc/Documentation/networking/ip-sysctl.txt)
- [文件描述符限制设置](https://www.cyberciti.biz/faq/linux-increase-the-maximum-number-of-open-files/)

### 监控和工具

- [JVM监控工具使用指南](https://docs.oracle.com/javase/8/docs/technotes/guides/troubleshoot/tooldescr025.html)
- [Linux系统监控命令](https://www.tecmint.com/command-line-tools-to-monitor-linux-performance/)
- [压力测试工具比较](https://blog.loadimpact.com/open-source-load-testing-tool-review)

## 📋 快速开始

### 1. 下载脚本

```bash
# 创建脚本目录
mkdir -p ~/lingxi-scripts
cd ~/lingxi-scripts

# 下载脚本文件（或直接复制上述内容）
# 启动脚本
cat > start-all-services.sh << 'EOF'
# 将上述Bash启动脚本内容粘贴到这里
EOF

# 健康检查脚本
cat > health-check.sh << 'EOF'
# 将上述健康检查脚本内容粘贴到这里
EOF

# 停止服务脚本
cat > stop-all-services.sh << 'EOF'
# 将上述停止服务脚本内容粘贴到这里
EOF
```

### 2. 设置执行权限

```bash
chmod +x *.sh
```

### 3. 启动服务

```bash
# 使用默认配置（prod环境，不启用GC日志）
./start-all-services.sh

# 使用自定义配置
./start-all-services.sh dev true  # dev环境，启用GC日志
```

### 4. 检查服务状态

```bash
./health-check.sh
```

### 5. 停止服务

```bash
./stop-all-services.sh
```

## 🔧 Linux环境配置建议

### 系统资源要求

- **操作系统**: Linux (Ubuntu 18.04+, CentOS 7+, RHEL 7+)
- **内存**: ≥ 12GB (10GB给Java服务，2GB给系统)
- **CPU**: ≥ 4核心（推荐8核心）
- **磁盘**: ≥ 50GB 可用空间
- **Java**: OpenJDK 11 或 17

### 系统优化

```bash
# 1. 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 2. 优化内核参数
cat >> /etc/sysctl.conf << EOF
# 网络优化
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_keepalive_time = 600

# 内存优化
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
EOF

sysctl -p

# 3. 设置时区
timedatectl set-timezone Asia/Shanghai
```

### 监控工具安装

```bash
# CentOS/RHEL
yum install -y htop iotop curl wget nc

# Ubuntu/Debian
apt-get update
apt-get install -y htop iotop curl wget netcat-openbsd

# 安装JVM监控工具
# 可选择安装 jstat, jmap, jstack 等 JDK 自带工具
```

---

**文档版本**: 1.1  
**最后更新**: 2024年12月  
**维护者**: 系统架构师  
**适用环境**: Linux (Ubuntu/CentOS), Java 11+, 内存 ≥ 10GB 