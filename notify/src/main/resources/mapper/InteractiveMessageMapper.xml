<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gw.notify.mapper.InteractiveMessageMapper">
    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.gw.notify.entity.InteractiveMessageEntity">
        <id column="id" property="id"/>
        <result column="content" property="content"/>
        <result column="level" property="level"/>
        <result column="associated_with_agent" property="associatedWithAgent"/>
        <result column="agent_id" property="agentId"/>
        <result column="session_id" property="sessionId"/>
        <result column="type" property="type"/>
        <result column="status" property="status"/>
        <result column="valid_start_date" property="validStartDate"/>
        <result column="valid_end_date" property="validEndDate"/>
        <result column="target_users" property="targetUsers"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="updater" property="updater"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <!-- 通过用户名分页查询有效的系统消息 -->
    <select id="pageByUsername" resultMap="BaseResultMap">
        SELECT a.*, b.status
        FROM t_active_message a
        INNER JOIN t_active_message_user b ON a.id = b.message_id AND b.username = #{username} AND b.deleted = 0
        WHERE a.deleted = 0

        <if test="agentId != null  and agentId > 0">
            AND a.agent_id = #{agentId}
        </if>


        ORDER BY a.create_time DESC
    </select>

    <!-- 分页查询所有系统消息（管理员用） -->
    <select id="page" resultMap="BaseResultMap">
        SELECT a.*
        FROM t_active_message a
        WHERE a.deleted = 0
        <!-- Add more filters based on InteractiveMessageQueryDTO if needed -->
        <if test="associatedWithAgent != null">
            AND a.associated_with_agent = #{associatedWithAgent}
        </if>
        <if test="agentId != null  and agentId > 0">
            AND a.agent_id = #{agentId}
        </if>
        ORDER BY a.create_time DESC
    </select>
</mapper> 