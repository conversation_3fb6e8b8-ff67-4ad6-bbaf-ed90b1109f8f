package com.gw.notify.service;

import com.github.pagehelper.PageInfo;
import com.gw.notify.entity.SystemMessageUserEntity;

import java.util.List;

public interface SystemMessageUserService {
    void markAsRead(Long messageId, String username);

    void markAllAsReadByUsername(String username);

    boolean existsByMessageIdAndUsername(Long messageId, String username);

    void deleteByMessageId(Long messageId);

    void insertBatch(List<SystemMessageUserEntity> entityList);

    PageInfo<SystemMessageUserEntity> pageByUsername(int pageNum, int pageSize, String username);

}
