package com.gw.notify.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gw.common.exception.BusinessException;
import com.gw.common.user.constant.UserCommonCacheConstant;
import com.gw.common.user.service.UserProxyService;
import com.gw.common.user.vo.UserBaseContentVo;
import com.gw.notify.config.CacheProperties;
import com.gw.notify.dto.InteractiveMessageQueryDTO;
import com.gw.notify.entity.InteractiveMessageEntity;
import com.gw.notify.mapper.InteractiveMessageMapper;
import com.gw.notify.service.InteractiveMessageService;
import com.gw.notify.service.InteractiveMessageUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

import static com.gw.common.exception.BusinessExceptionCode.NOT_FOUND_CODE;

/**
 * 互动消息服务实现
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class InteractiveMessageServiceImpl extends ServiceImpl<InteractiveMessageMapper, InteractiveMessageEntity> implements InteractiveMessageService {
    private final InteractiveMessageUserService messageUserService;
    private final UserProxyService userProxyService;
    private final CacheProperties cacheProperties;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createMessage(InteractiveMessageEntity entity) {
        // 1. 保存消息实体
        this.save(entity);

        // 2. 保存用户关联
        if (entity.getUsers() != null && !entity.getUsers().isEmpty()) {
            // 设置消息ID
            entity.getUsers().forEach(user -> user.setMessageId(entity.getId()));
            messageUserService.insertBatch(entity.getUsers());
        }
    }

    public void fillContentParams(InteractiveMessageEntity entity, Map<String, UserBaseContentVo> userMap) {

        if (entity.getContent().contains("{nickname}")) {
            UserBaseContentVo userBaseContentVo = userMap.get(entity.getCreator());
            if (userBaseContentVo != null && userBaseContentVo.getNickname() != null) {
                entity.setContent(entity.getContent().replace("{nickname}", userBaseContentVo.getNickname()));
            } else {
                entity.setContent(entity.getContent().replace("{nickname}", "有人"));
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMessage(InteractiveMessageEntity entity) {
        // 1. 更新消息实体
        this.updateById(entity);

        // 2. 删除原有的用户关联
        messageUserService.deleteByMessageId(entity.getId());

        // 3. 重新创建用户关联
        if (entity.getUsers() != null && !entity.getUsers().isEmpty()) {
            entity.getUsers().forEach(user -> user.setMessageId(entity.getId()));
            messageUserService.insertBatch(entity.getUsers());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMessage(InteractiveMessageEntity entity) {
        // 1. 逻辑删除消息实体
        entity.setDeleted(1);
        this.updateById(entity);
        this.baseMapper.deleteById(entity);
        // 2. 删除用户关联
        messageUserService.deleteByMessageId(entity.getId());
    }

    @Override
    public InteractiveMessageEntity findById(Long id) {
        InteractiveMessageEntity entity = this.baseMapper.findValidById(id);
        if (entity == null) {
            throw new BusinessException(NOT_FOUND_CODE.getCode(), "消息不存在");
        }
        Map<String, UserBaseContentVo> userMap = userProxyService.findAllUserMap(cacheProperties.getCacheName(UserCommonCacheConstant.USER_BASE_MAP_CACHE_KEY));
        fillContentParams(entity, userMap);
        return entity;
    }

    @Override
    public PageInfo<InteractiveMessageEntity> pageValidMessagesByUsername(int pageNum, int pageSize, InteractiveMessageQueryDTO query) {
        PageHelper.startPage(pageNum, pageSize);
        List<InteractiveMessageEntity> messages = this.baseMapper.pageByUsername(query);
        List<String> usernames = messages.stream().map(InteractiveMessageEntity::getCreator).toList();
        Map<String, UserBaseContentVo> userMap = userProxyService.findAllUserMapByUsernames(cacheProperties.getCacheName(UserCommonCacheConstant.USER_BASE_MAP_CACHE_KEY), usernames);
        for (InteractiveMessageEntity message : messages) {
            fillContentParams(message, userMap);
        }
        return new PageInfo<>(messages);
    }

    @Override
    public PageInfo<InteractiveMessageEntity> pageMessages(int pageNum, int pageSize, InteractiveMessageQueryDTO query) {
        PageHelper.startPage(pageNum, pageSize);
        List<InteractiveMessageEntity> messages = this.baseMapper.page(query);
        List<String> usernames = messages.stream().map(InteractiveMessageEntity::getCreator).toList();
        Map<String, UserBaseContentVo> userMap = userProxyService.findAllUserMapByUsernames(cacheProperties.getCacheName(UserCommonCacheConstant.USER_BASE_MAP_CACHE_KEY), usernames);
        for (InteractiveMessageEntity message : messages) {
            fillContentParams(message, userMap);
        }
        return new PageInfo<>(messages);
    }
} 