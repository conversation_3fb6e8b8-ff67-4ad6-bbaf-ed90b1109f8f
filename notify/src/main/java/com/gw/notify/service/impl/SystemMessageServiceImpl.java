package com.gw.notify.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gw.common.exception.EntityNotFoundException;
import com.gw.notify.dto.SystemMessageQueryDTO;
import com.gw.notify.entity.SystemMessageEntity;
import com.gw.notify.entity.SystemMessageUserEntity;
import com.gw.notify.mapper.SystemMessageMapper;
import com.gw.notify.service.SystemMessageService;
import com.gw.notify.service.SystemMessageUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 系统消息服务实现类
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class SystemMessageServiceImpl extends ServiceImpl<SystemMessageMapper, SystemMessageEntity> implements SystemMessageService {
    private final SystemMessageUserService messageUserService;


    @Override
    @Transactional
    public void createMessage(SystemMessageEntity entity) {
        this.baseMapper.insert(entity);
        List<SystemMessageUserEntity> users = entity.getUsers();
        if (users != null) {
            users.forEach(user -> {
                user.setMessageId(entity.getId());
                user.setStatus(1);
            });
        }
        messageUserService.insertBatch(users);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMessage(SystemMessageEntity entity) {
        this.baseMapper.updateById(entity);
        messageUserService.deleteByMessageId(entity.getId());
        messageUserService.insertBatch(entity.getUsers());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMessage(SystemMessageEntity entity) {
        entity.setDeleted(1);
        this.baseMapper.updateById(entity);
        this.baseMapper.deleteById(entity);
        messageUserService.deleteByMessageId(entity.getId());
    }

    @Override
    public SystemMessageEntity findById(Long id) {
        return this.baseMapper.findById(id).orElseThrow(() -> new EntityNotFoundException("没有找到对应ID的系统消息"));
    }

    @Override
    public PageInfo<SystemMessageEntity> pageValidMessagesByUsername(int pageNum, int pageSize, SystemMessageQueryDTO query) {
        PageHelper.startPage(pageNum, pageSize);
        List<SystemMessageEntity> messages = this.baseMapper.pageByUsername(query);
        return new PageInfo<>(messages);
    }

    @Override
    public PageInfo<SystemMessageEntity> pageMessages(int pageNum, int pageSize, SystemMessageQueryDTO query) {
        PageHelper.startPage(pageNum, pageSize);
        List<SystemMessageEntity> messages = this.baseMapper.page(query);
        return new PageInfo<>(messages);
    }

    @Override
    public PageInfo<SystemMessageEntity> pagePublic(int pageNum, int pageSize, SystemMessageQueryDTO query) {
        PageHelper.startPage(pageNum, pageSize);
        List<SystemMessageEntity> messages = this.baseMapper.pagePublic(query);
        return new PageInfo<>(messages);
    }
} 