-- 会员套餐表
DROP TABLE IF EXISTS "public"."t_membership_package";
DROP SEQUENCE IF EXISTS t_membership_package_id_seq;

-- 创建序列
CREATE SEQUENCE t_membership_package_id_seq START 1 CACHE 1;
CREATE TABLE IF NOT EXISTS t_membership_package
(
    id             BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_membership_package_id_seq'::regclass),
    create_time    TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    update_time    TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted        INTEGER            DEFAULT 0,
    creator        VA<PERSON>HA<PERSON>(255),
    updater        VARCHAR(255),
    name           VARCHAR(100)   NOT NULL,
    description    TEXT,
    price          DECIMAL(10, 2) NOT NULL,
    discount_price DECIMAL(10, 2),
    valid_days     INTEGER        NOT NULL,
    type           INTEGER        NOT NULL,      -- 1-包月, 2-包季, 3-包年, 4-终身
    status         INTEGER            DEFAULT 1, -- 0-下架, 1-上架
    sort_order     INTEGER            DEFAULT 0,
    is_recommended INTEGER            DEFAULT 0, -- 0-否, 1-是
    icon_url       VARCHAR(255),
    allow_stack    INTEGER            DEFAULT 0  -- 是否允许叠加购买: 0-否, 1-是
);
COMMENT ON TABLE t_membership_package IS '会员套餐表';
COMMENT ON COLUMN t_membership_package.name IS '套餐名称';
COMMENT ON COLUMN t_membership_package.description IS '套餐描述';
COMMENT ON COLUMN t_membership_package.price IS '套餐价格(元)';
COMMENT ON COLUMN t_membership_package.discount_price IS '折扣价格(元)';
COMMENT ON COLUMN t_membership_package.valid_days IS '有效期(天)，0表示永久';
COMMENT ON COLUMN t_membership_package.type IS '套餐类型: 1-包月, 2-包季, 3-包年, 4-终身';
COMMENT ON COLUMN t_membership_package.status IS '套餐状态: 0-下架, 1-上架';
COMMENT ON COLUMN t_membership_package.sort_order IS '排序优先级';
COMMENT ON COLUMN t_membership_package.is_recommended IS '是否推荐: 0-否, 1-是';
COMMENT ON COLUMN t_membership_package.icon_url IS '套餐图标URL';
COMMENT ON COLUMN t_membership_package.allow_stack IS '是否允许叠加购买: 0-否, 1-是';

-- 邀请码表
DROP TABLE IF EXISTS "public"."t_invitation_code";
DROP SEQUENCE IF EXISTS t_invitation_code_id_seq;

-- 创建序列
CREATE SEQUENCE t_invitation_code_id_seq START 1 CACHE 1;
CREATE TABLE IF NOT EXISTS t_invitation_code
(
    id            BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_invitation_code_id_seq'::regclass),
    create_time   TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    update_time   TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted       INTEGER            DEFAULT 0,
    creator       VARCHAR(255),
    updater       VARCHAR(255),
    username      VARCHAR(255) NOT NULL,
    code          VARCHAR(50)  NOT NULL,
    status        INTEGER            DEFAULT 1,  -- 0-已失效, 1-有效
    used_times    INTEGER            DEFAULT 0,
    max_use_times INTEGER            DEFAULT -1, -- -1表示不限制
    expire_time   TIMESTAMP
);
COMMENT ON TABLE t_invitation_code IS '邀请码表';
COMMENT ON COLUMN t_invitation_code.username IS '用户ID';
COMMENT ON COLUMN t_invitation_code.code IS '邀请码';
COMMENT ON COLUMN t_invitation_code.status IS '状态: 0-已失效, 1-有效';
COMMENT ON COLUMN t_invitation_code.used_times IS '已使用次数';
COMMENT ON COLUMN t_invitation_code.max_use_times IS '最大使用次数, -1表示不限制';
COMMENT ON COLUMN t_invitation_code.expire_time IS '失效时间';

-- 会员权益表
DROP TABLE IF EXISTS "public"."t_membership_benefit";
DROP SEQUENCE IF EXISTS t_membership_benefit_id_seq;

-- 创建序列
CREATE SEQUENCE t_membership_benefit_id_seq START 1 CACHE 1;
CREATE TABLE IF NOT EXISTS t_membership_benefit
(
    id          BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_membership_benefit_id_seq'::regclass),
    create_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted     INTEGER            DEFAULT 0,
    creator     VARCHAR(255),
    updater     VARCHAR(255),
    name        VARCHAR(100) NOT NULL,
    code        VARCHAR(50)  NOT NULL,
    type        INTEGER      NOT NULL,        -- 1-功能权益, 2-资源权益, 3-服务权益
    icon_url    VARCHAR(255),
    sort_order  INTEGER            DEFAULT 0,
    status      INTEGER            DEFAULT 1, -- 1-启用, 2-禁用
    vip_level   INTEGER            DEFAULT 0, -- 是否仅限VIP使用: 0-普通会员, 1-黄金会员
    reset_cycle INTEGER            DEFAULT 1, -- 重置周期: 1-日, 2-月, 3-周, 4-年
    quota_value INTEGER            DEFAULT 0, -- 配额值，默认 0，-1 表示无限配额
    is_show     INTEGER            DEFAULT 1  -- 是否显示: 1-显示, 0-不显示
);
COMMENT ON TABLE t_membership_benefit IS '会员权益表';
COMMENT ON COLUMN t_membership_benefit.name IS '权益名称';
COMMENT ON COLUMN t_membership_benefit.code IS '权益代码';

COMMENT ON COLUMN t_membership_benefit.type IS '权益类型: 1-功能权益, 2-资源权益, 3-服务权益';
COMMENT ON COLUMN t_membership_benefit.icon_url IS '权益图标URL';
COMMENT ON COLUMN t_membership_benefit.sort_order IS '排序优先级';
COMMENT ON COLUMN t_membership_benefit.status IS '权益状态: 1-启用, 2-禁用';
COMMENT ON COLUMN t_membership_benefit.vip_level IS '是否仅限VIP使用: 0-普通会员, 1-黄金会员';
COMMENT ON COLUMN t_membership_benefit.reset_cycle IS '重置周期: 1-日, 2-月, 3-周, 4-年';
COMMENT ON COLUMN t_membership_benefit.quota_value IS '配额值，默认 0，-1 表示无限配额';
COMMENT ON COLUMN t_membership_benefit.is_show IS '是否显示: 1-显示, 0-不显示';

-- 初始化普通会员权益数据
INSERT INTO t_membership_benefit (name, code, type, icon_url, sort_order, status, vip_level, reset_cycle, quota_value)
VALUES
-- 每日通话次数
('每日通话次数', 'dailyLimitedChat', 1, '', 10, 1, 0, 1, 50),

-- 每日语音通话次数
('每日语音通话次数', 'dailyLimitedPhone', 1, '', 20, 1, 0, 1, 5),

-- 每日AI生图
('每日AI生图次数', 'dailyLimitedImage', 1, '', 30, 1, 0, 1, 2),

-- 云端备份大小
('云端备份空间', 'backupSize', 2, '', 40, 1, 0, 1, 100),
('创建智能体数量', 'creatAgent', 2, '', 40, 1, 0, 1, 3),

-- 对话回溯条数
('对话回溯条数', 'historyMessageCount', 2, '', 50, 1, 0, 1, 50),

-- 专属折扣数
('专属折扣', 'discountRate', 3, '', 60, 1, 0, 1, -1),

-- 记忆重置
('记忆重置', 'memoryReset', 1, '', 70, 1, 0, 1, 0),

-- 会员标识
('会员标识', 'membershipBadge', 3, '', 80, 1, 0, 1, 0);
-- 初始化VIP会员权益数据
INSERT INTO t_membership_benefit (name, code, type, icon_url, sort_order, status, vip_level, reset_cycle, quota_value)
VALUES
-- 每日通话次数
('每日通话次数', 'dailyLimitedChat', 1, '', 10, 1, 1, 1, -1),

-- 每日语音通话次数
('每日语音通话次数', 'dailyLimitedPhone', 1, '', 20, 1, 1, 1, -1),

-- 每日AI生图
('每日AI生图次数', 'dailyLimitedImage', 1, '', 30, 1, 1, 1, 40),

-- 云端备份大小
('云端备份空间', 'backupSize', 2, '', 40, 1, 1, 1, 100),
('创建智能体数量', 'creatAgent', 2, '', 40, 1, 0, 1, 10),

-- 对话回溯条数
('对话回溯条数', 'historyMessageCount', 2, '', 50, 1, 1, 1, -1),

-- 专属折扣数
('专属折扣', 'discountRate', 3, '', 60, 1, 1, 1, -1),

-- 记忆重置
('记忆重置', 'memoryReset', 1, '', 70, 1, 1, 1, -1),

-- 会员标识
('会员标识', 'membershipBadge', 3, '', 80, 1, 1, 1, -1);

-- 套餐权益关系表
DROP TABLE IF EXISTS "public"."t_package_benefit_relation";
DROP SEQUENCE IF EXISTS t_package_benefit_relation_id_seq;

-- 创建序列
CREATE SEQUENCE t_package_benefit_relation_id_seq START 1 CACHE 1;
CREATE TABLE IF NOT EXISTS t_package_benefit_relation
(
    id            BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_package_benefit_relation_id_seq'::regclass),
    create_time   TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    update_time   TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted       INTEGER            DEFAULT 0,
    creator       VARCHAR(255),
    updater       VARCHAR(255),
    package_id    BIGINT NOT NULL,
    benefit_id    BIGINT NOT NULL,
    benefit_value VARCHAR(255),
    status        INTEGER            DEFAULT 1 -- 0-禁用, 1-启用
);
COMMENT ON TABLE t_package_benefit_relation IS '套餐权益关系表';
COMMENT ON COLUMN t_package_benefit_relation.package_id IS '套餐ID';
COMMENT ON COLUMN t_package_benefit_relation.benefit_id IS '权益ID';
COMMENT ON COLUMN t_package_benefit_relation.benefit_value IS '权益值';
COMMENT ON COLUMN t_package_benefit_relation.status IS '关联状态: 0-禁用, 1-启用';

-- 会员权益使用记录表
DROP TABLE IF EXISTS "public"."t_member_benefit_usage";
DROP SEQUENCE IF EXISTS t_member_benefit_usage_id_seq;

-- 创建序列
CREATE SEQUENCE t_member_benefit_usage_id_seq START 1 CACHE 1;
CREATE TABLE IF NOT EXISTS t_member_benefit_usage
(
    id           BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_member_benefit_usage_id_seq'::regclass),
    username     VARCHAR(255) NOT NULL,
    benefit_id   BIGINT       NOT NULL,
    benefit_code VARCHAR(50),
    use_count    INTEGER            DEFAULT 1,
    daily_limit  INTEGER,
    use_time     DATE               DEFAULT CURRENT_DATE
);
COMMENT ON TABLE t_member_benefit_usage IS '会员权益使用记录表';
COMMENT ON COLUMN t_member_benefit_usage.username IS '用户ID';
COMMENT ON COLUMN t_member_benefit_usage.benefit_id IS '权益ID';
COMMENT ON COLUMN t_member_benefit_usage.benefit_code IS '权益代码';
COMMENT ON COLUMN t_member_benefit_usage.use_count IS '使用次数';
COMMENT ON COLUMN t_member_benefit_usage.daily_limit IS '每日限制次数';
COMMENT ON COLUMN t_member_benefit_usage.use_time IS '使用日期';

-- 会员折扣表
DROP TABLE IF EXISTS "public"."t_membership_discount";
DROP SEQUENCE IF EXISTS t_membership_discount_id_seq;

-- 创建序列
CREATE SEQUENCE t_membership_discount_id_seq START 1 CACHE 1;
CREATE TABLE IF NOT EXISTS t_membership_discount
(
    id                   BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_membership_discount_id_seq'::regclass),
    create_time          TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    update_time          TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted              INTEGER            DEFAULT 0,
    creator              VARCHAR(255),
    updater              VARCHAR(255),
    name                 VARCHAR(100)   NOT NULL,
    discount_type        INTEGER        NOT NULL,      -- 1-固定金额, 2-百分比
    discount_value       DECIMAL(10, 2) NOT NULL,      -- 折扣值（固定金额或百分比）
    min_order_amount     DECIMAL(10, 2)     DEFAULT 0, -- 最低订单金额
    start_time           TIMESTAMP,                    -- 折扣开始时间
    end_time             TIMESTAMP,                    -- 折扣结束时间
    status               INTEGER            DEFAULT 1, -- 0-禁用, 1-启用
    apply_to_package_ids TEXT                          -- 适用的套餐ID列表, 逗号分隔, 为空表示适用所有套餐
);
COMMENT ON TABLE t_membership_discount IS '会员折扣表';
COMMENT ON COLUMN t_membership_discount.name IS '折扣名称';
COMMENT ON COLUMN t_membership_discount.discount_type IS '折扣类型: 1-固定金额, 2-百分比';
COMMENT ON COLUMN t_membership_discount.discount_value IS '折扣值（固定金额或百分比）';
COMMENT ON COLUMN t_membership_discount.min_order_amount IS '最低订单金额';
COMMENT ON COLUMN t_membership_discount.start_time IS '折扣开始时间';
COMMENT ON COLUMN t_membership_discount.end_time IS '折扣结束时间';
COMMENT ON COLUMN t_membership_discount.status IS '折扣状态: 0-禁用, 1-启用';
COMMENT ON COLUMN t_membership_discount.apply_to_package_ids IS '适用的套餐ID列表, 逗号分隔, 为空表示适用所有套餐';

-- 会员订单表
DROP TABLE IF EXISTS "public"."t_membership_order";
DROP SEQUENCE IF EXISTS t_membership_order_id_seq;

-- 创建序列
CREATE SEQUENCE t_membership_order_id_seq START 1 CACHE 1;
CREATE TABLE IF NOT EXISTS t_membership_order
(
    id              BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_membership_order_id_seq'::regclass),
    create_time     TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    update_time     TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted         INTEGER            DEFAULT 0,
    creator         VARCHAR(255),
    updater         VARCHAR(255),
    order_no        VARCHAR(50)    NOT NULL,
    username        VARCHAR(255)   NOT NULL,
    package_id      BIGINT         NOT NULL,
    package_name    VARCHAR(100)   NOT NULL,
    amount          DECIMAL(10, 2) NOT NULL,
    pay_amount      DECIMAL(10, 2) NOT NULL,
    discount_amount DECIMAL(10, 2)     DEFAULT 0,
    pay_time        TIMESTAMP,
    pay_method      INTEGER            DEFAULT 1, -- 1-微信, 2-支付宝, 3-Apple支付, 4-银行卡
    trade_no        VARCHAR(100),
    status          INTEGER            DEFAULT 1, -- 1-未支付, 2-已支付, 3-已取消, 4-已退款
    valid_days      INTEGER        NOT NULL,
    order_type      INTEGER            DEFAULT 1, -- 1-购买, 2-续费, 3-赠送, 4-后台补录, 5-邀请赠送
    source_username VARCHAR(255),                 -- 邀请人用户名
    invitation_code VARCHAR(50),                  -- 使用的邀请码
    operator        VARCHAR(255),                 -- 操作人ID（后台赠送/补录时使用）
    remark          TEXT
);
COMMENT ON TABLE t_membership_order IS '会员订单表';
COMMENT ON COLUMN t_membership_order.order_no IS '订单号';
COMMENT ON COLUMN t_membership_order.username IS '用户ID';
COMMENT ON COLUMN t_membership_order.package_id IS '套餐ID';
COMMENT ON COLUMN t_membership_order.package_name IS '套餐名称';
COMMENT ON COLUMN t_membership_order.amount IS '订单金额(元)';
COMMENT ON COLUMN t_membership_order.pay_amount IS '支付金额(元)';
COMMENT ON COLUMN t_membership_order.discount_amount IS '优惠金额(元)';
COMMENT ON COLUMN t_membership_order.pay_time IS '支付时间';
COMMENT ON COLUMN t_membership_order.pay_method IS '支付方式: 1-微信, 2-支付宝, 3-Apple支付, 4-银行卡';
COMMENT ON COLUMN t_membership_order.trade_no IS '支付流水号';
COMMENT ON COLUMN t_membership_order.status IS '订单状态: 1-未支付, 2-已支付, 3-已取消, 4-已退款';
COMMENT ON COLUMN t_membership_order.valid_days IS '订单有效期(天)';
COMMENT ON COLUMN t_membership_order.order_type IS '订单类型: 1-购买, 2-续费, 3-赠送, 4-后台补录, 5-邀请赠送';
COMMENT ON COLUMN t_membership_order.source_username IS '邀请人用户名（邀请赠送时使用）';
COMMENT ON COLUMN t_membership_order.invitation_code IS '使用的邀请码';
COMMENT ON COLUMN t_membership_order.operator IS '操作人ID（后台赠送/补录时使用）';
COMMENT ON COLUMN t_membership_order.remark IS '备注';

-- 用户消费记录表
DROP TABLE IF EXISTS "public"."t_user_expense_record";
DROP SEQUENCE IF EXISTS t_user_expense_record_id_seq;

-- 创建序列
CREATE SEQUENCE t_user_expense_record_id_seq START 1 CACHE 1;
CREATE TABLE IF NOT EXISTS t_user_expense_record
(
    id             BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_user_expense_record_id_seq'::regclass),
    create_time    TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted        INTEGER            DEFAULT 0,
    username       VARCHAR(255)   NOT NULL,
    order_id       BIGINT,
    expense_type   INTEGER        NOT NULL,     -- 1-会员购买, 2-智能体购买, 3-其他消费
    amount         DECIMAL(10, 2) NOT NULL,     -- 支出金额
    description    TEXT,                        -- 支出描述
    payment_method INTEGER,                     -- 1-支付宝, 2-微信, 3-Apple支付, 4-银行卡
    transaction_id VARCHAR(100),                -- 交易流水号
    status         INTEGER            DEFAULT 1 -- 1-成功, 2-退款, 3-失败
);
COMMENT ON TABLE t_user_expense_record IS '用户支出明细表';
COMMENT ON COLUMN t_user_expense_record.username IS '用户ID';
COMMENT ON COLUMN t_user_expense_record.order_id IS '订单ID';
COMMENT ON COLUMN t_user_expense_record.expense_type IS '支出类型: 1-会员购买, 2-智能体购买, 3-其他消费';
COMMENT ON COLUMN t_user_expense_record.amount IS '支出金额';
COMMENT ON COLUMN t_user_expense_record.description IS '支出描述';
COMMENT ON COLUMN t_user_expense_record.payment_method IS '支付方式: 1-支付宝, 2-微信, 3-Apple支付, 4-银行卡';
COMMENT ON COLUMN t_user_expense_record.transaction_id IS '交易流水号';
COMMENT ON COLUMN t_user_expense_record.status IS '状态: 1-成功, 2-退款, 3-失败';

-- 用户会员表
DROP TABLE IF EXISTS "public"."t_user_membership";
DROP SEQUENCE IF EXISTS t_user_membership_id_seq;

-- 创建序列
CREATE SEQUENCE t_user_membership_id_seq START 1 CACHE 1;
CREATE TABLE IF NOT EXISTS t_user_membership
(
    id               BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_user_membership_id_seq'::regclass),
    create_time      TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    update_time      TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted          INTEGER            DEFAULT 0,
    creator          VARCHAR(255),
    updater          VARCHAR(255),
    username         VARCHAR(255) NOT NULL,
    package_id       BIGINT,
    status           INTEGER            DEFAULT 1, -- 0-非会员, 1-会员
    start_time       TIMESTAMP    NOT NULL,
    expire_time      TIMESTAMP,                    -- 会员到期时间
    last_order_id    BIGINT,                       -- 最后一次订单ID
    auto_renew       INTEGER            DEFAULT 0, -- 0-否, 1-是
    total_pay_amount DECIMAL(10, 2)     DEFAULT 0, -- 总计支付金额(元)
    vip_level        INTEGER            DEFAULT 0, -- 会员等级
    member_points    INTEGER            DEFAULT 0  -- 会员积分
);
COMMENT ON TABLE t_user_membership IS '用户会员状态表';
COMMENT ON COLUMN t_user_membership.username IS '用户ID';
COMMENT ON COLUMN t_user_membership.package_id IS '当前套餐ID';
COMMENT ON COLUMN t_user_membership.status IS '会员状态: 0-非会员, 1-会员';
COMMENT ON COLUMN t_user_membership.start_time IS '会员开始时间';
COMMENT ON COLUMN t_user_membership.expire_time IS '会员到期时间';
COMMENT ON COLUMN t_user_membership.last_order_id IS '最后一次订单ID';
COMMENT ON COLUMN t_user_membership.auto_renew IS '是否自动续费: 0-否, 1-是';
COMMENT ON COLUMN t_user_membership.total_pay_amount IS '总计支付金额(元)';
COMMENT ON COLUMN t_user_membership.vip_level IS '会员等级';
COMMENT ON COLUMN t_user_membership.member_points IS '会员积分';

-- 用户会员历史记录表
DROP TABLE IF EXISTS "public"."t_user_membership_history";
DROP SEQUENCE IF EXISTS t_user_membership_history_id_seq;

-- 创建序列
CREATE SEQUENCE t_user_membership_history_id_seq START 1 CACHE 1;
CREATE TABLE IF NOT EXISTS t_user_membership_history
(
    id                 BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_user_membership_history_id_seq'::regclass),
    create_time        TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    update_time        TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted            INTEGER            DEFAULT 0,
    creator            VARCHAR(255),
    updater            VARCHAR(255),
    username           VARCHAR(255) NOT NULL,
    operation_type     INTEGER      NOT NULL, -- 1-新开通, 2-续费, 3-赠送, 4-管理员操作, 5-邀请获得, 6-过期, 7-取消, 8-套餐变更
    order_id           BIGINT,                -- 相关订单ID
    package_id         BIGINT,                -- 套餐ID
    package_name       VARCHAR(100),          -- 套餐名称
    before_status      INTEGER,               -- 变更前会员状态: 0-非会员, 1-会员
    after_status       INTEGER,               -- 变更后会员状态: 0-非会员, 1-会员
    before_expire_time TIMESTAMP,             -- 变更前到期时间
    after_expire_time  TIMESTAMP,             -- 变更后到期时间
    operator           VARCHAR(255),          -- 操作人
    invite_username    VARCHAR(255),          -- 邀请人用户名
    remark             TEXT                   -- 备注
);
COMMENT ON TABLE t_user_membership_history IS '用户会员历史记录表';
COMMENT ON COLUMN t_user_membership_history.username IS '用户ID';
COMMENT ON COLUMN t_user_membership_history.operation_type IS '操作类型: 1-新开通, 2-续费, 3-赠送, 4-管理员操作, 5-邀请获得, 6-过期, 7-取消, 8-套餐变更';
COMMENT ON COLUMN t_user_membership_history.order_id IS '相关订单ID';
COMMENT ON COLUMN t_user_membership_history.package_id IS '套餐ID';
COMMENT ON COLUMN t_user_membership_history.package_name IS '套餐名称';
COMMENT ON COLUMN t_user_membership_history.before_status IS '变更前会员状态: 0-非会员, 1-会员';
COMMENT ON COLUMN t_user_membership_history.after_status IS '变更后会员状态: 0-非会员, 1-会员';
COMMENT ON COLUMN t_user_membership_history.before_expire_time IS '变更前到期时间';
COMMENT ON COLUMN t_user_membership_history.after_expire_time IS '变更后到期时间';
COMMENT ON COLUMN t_user_membership_history.operator IS '操作人';
COMMENT ON COLUMN t_user_membership_history.invite_username IS '邀请人用户名';
COMMENT ON COLUMN t_user_membership_history.remark IS '备注';

-- 邀请奖励配置表
DROP TABLE IF EXISTS "public"."t_reward_config";
DROP SEQUENCE IF EXISTS t_reward_config_id_seq;

-- 创建序列
CREATE SEQUENCE t_reward_config_id_seq START 1 CACHE 1;
CREATE TABLE IF NOT EXISTS t_reward_config
(
    id           BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_reward_config_id_seq'::regclass),
    create_time  TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    update_time  TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted      INTEGER            DEFAULT 0,
    creator      VARCHAR(255),
    updater      VARCHAR(255),
    name         VARCHAR(255),
    reward_type  INTEGER NOT NULL,            -- 1-天数, 2-月, 3-季度, 4-年
    reward_value INTEGER NOT NULL,            -- 邀请奖励值（对应reward_type的数量）
    code         VARCHAR(50),                 -- 奖励码
    status       INTEGER            DEFAULT 1 -- 0-禁用, 1-启用
);
COMMENT ON TABLE t_reward_config IS '邀请奖励配置表';
COMMENT ON COLUMN t_reward_config.reward_type IS '奖励类型: 1-天数, 2-月, 3-季度, 4-年';
COMMENT ON COLUMN t_reward_config.reward_value IS '奖励值（对应reward_type的数量）';
COMMENT ON COLUMN t_reward_config.code IS '奖励码';
COMMENT ON COLUMN t_reward_config.status IS '状态: 0-禁用, 1-启用';

-- 邀请历史表
DROP TABLE IF EXISTS "public"."t_invitation_history";
DROP SEQUENCE IF EXISTS t_invitation_history_id_seq;

-- 创建序列
CREATE SEQUENCE t_invitation_history_id_seq START 1 CACHE 1;
CREATE TABLE IF NOT EXISTS t_invitation_history
(
    id              BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_invitation_history_id_seq'::regclass),
    inviter         VARCHAR(255) NOT NULL,
    invitee         VARCHAR(255) NOT NULL,
    invitation_code VARCHAR(50)  NOT NULL,
    create_time     TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    update_time     TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted         INTEGER            DEFAULT 0
);
COMMENT ON TABLE t_invitation_history IS '邀请历史记录表';
COMMENT ON COLUMN t_invitation_history.inviter IS '邀请人';
COMMENT ON COLUMN t_invitation_history.invitee IS '被邀请人';
COMMENT ON COLUMN t_invitation_history.invitation_code IS '邀请码';
COMMENT ON COLUMN t_invitation_history.create_time IS '创建时间';

-- 创建索引
CREATE INDEX idx_invitation_history_inviter ON t_invitation_history (inviter) WHERE deleted = 0;
CREATE INDEX idx_invitation_history_invitee ON t_invitation_history (invitee) WHERE deleted = 0;
CREATE INDEX idx_invitation_history_code ON t_invitation_history (invitation_code) WHERE deleted = 0;

-- 邀请码唯一性检查表
CREATE TABLE IF NOT EXISTS t_invitation_code_check
(
    code        VARCHAR(50) PRIMARY KEY,
    create_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE t_invitation_code_check IS '邀请码唯一性检查表';
COMMENT ON COLUMN t_invitation_code_check.code IS '邀请码';
COMMENT ON COLUMN t_invitation_code_check.create_time IS '创建时间';

-- 创建清理函数，可以通过应用程序定期调用
CREATE OR REPLACE FUNCTION clean_invitation_code_check()
    RETURNS void AS
$$
BEGIN
    DELETE FROM t_invitation_code_check WHERE create_time < NOW() - INTERVAL '1 day';
END;
$$ LANGUAGE plpgsql;
-- 会员积分历史记录表
CREATE TABLE IF NOT EXISTS t_member_points_history
(
    id            SERIAL PRIMARY KEY,
    username      VARCHAR(255) NOT NULL,
    change_type   INTEGER      NOT NULL, -- 1-增加, 2-消费
    points_before INTEGER      NOT NULL,
    points_after  INTEGER      NOT NULL,
    points_change INTEGER      NOT NULL,
    reason        VARCHAR(255),
    operator      VARCHAR(255),
    order_id      BIGINT,
    operate_time  TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted       INTEGER                  DEFAULT 0,
    create_time   TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    update_time   TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE t_member_points_history IS '会员积分历史记录表';
COMMENT ON COLUMN t_member_points_history.username IS '用户名';
COMMENT ON COLUMN t_member_points_history.change_type IS '变更类型: 1-增加, 2-消费';
COMMENT ON COLUMN t_member_points_history.points_before IS '变更前积分';
COMMENT ON COLUMN t_member_points_history.points_after IS '变更后积分';
COMMENT ON COLUMN t_member_points_history.points_change IS '变更积分数量';
COMMENT ON COLUMN t_member_points_history.reason IS '变更原因';
COMMENT ON COLUMN t_member_points_history.operator IS '操作人';
COMMENT ON COLUMN t_member_points_history.order_id IS '关联订单ID（如果有）';
COMMENT ON COLUMN t_member_points_history.operate_time IS '操作时间';

-- 创建索引
CREATE INDEX idx_member_points_history_username ON t_member_points_history (username);
CREATE INDEX idx_member_points_history_operate_time ON t_member_points_history (operate_time);
