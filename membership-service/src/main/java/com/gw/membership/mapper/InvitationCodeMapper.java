package com.gw.membership.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.membership.dto.InvitationCodeQueryDTO;
import com.gw.membership.entity.InvitationCodeEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 邀请码Mapper
 */
@Mapper
public interface InvitationCodeMapper extends BaseMapper<InvitationCodeEntity> {
    List<InvitationCodeEntity> page(InvitationCodeQueryDTO query);

    InvitationCodeEntity findById(Long id);

    InvitationCodeEntity findByCode(String code);

    /**
     * 检查邀请码是否唯一，通过尝试插入临时记录
     * 首先检查实际邀请码表中是否存在该邀请码，如果不存在才插入临时表
     * 如果插入成功返回1，如果邀请码已存在返回0
     *
     * @param code 邀请码
     * @return 插入结果
     */
    @Insert("WITH check_real_code AS (" +
            "  SELECT 1 WHERE NOT EXISTS (" +
            "    SELECT 1 FROM t_invitation_code WHERE code = #{code} AND deleted = 0" +
            "  )" +
            ")" +
            "INSERT INTO t_invitation_code_check (code) " +
            "SELECT #{code} FROM check_real_code " +
            "ON CONFLICT (code) DO NOTHING")
    int insertCodeCheck(@Param("code") String code);
}