package com.gw.membership.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.membership.entity.PackageBenefitRelationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 套餐权益关联Mapper接口
 */
@Mapper
public interface PackageBenefitRelationMapper extends BaseMapper<PackageBenefitRelationEntity> {

    /**
     * 根据套餐ID查询关联
     *
     * @param packageId 套餐ID
     * @return 套餐权益关联列表
     */
    @Select("SELECT * FROM t_package_benefit_relation WHERE package_id = #{packageId} AND status = 1 AND deleted = 0")
    List<PackageBenefitRelationEntity> selectByPackageId(@Param("packageId") Long packageId);

    /**
     * 根据权益ID查询关联
     *
     * @param benefitId 权益ID
     * @return 套餐权益关联列表
     */
    @Select("SELECT * FROM t_package_benefit_relation WHERE benefit_id = #{benefitId} AND status = 1 AND deleted = 0")
    List<PackageBenefitRelationEntity> selectByBenefitId(@Param("benefitId") Long benefitId);

    /**
     * 根据套餐ID和权益ID查询关联
     *
     * @param packageId 套餐ID
     * @param benefitId 权益ID
     * @return 套餐权益关联
     */
    @Select("SELECT * FROM t_package_benefit_relation WHERE package_id = #{packageId} AND benefit_id = #{benefitId} AND deleted = 0 LIMIT 1")
    PackageBenefitRelationEntity selectByPackageIdAndBenefitId(@Param("packageId") Long packageId,
                                                               @Param("benefitId") Long benefitId);

    @Update("UPDATE t_package_benefit_relation SET deleted = 1 WHERE package_id = #{packageId}")
    void deleteByPackageId(@Param("packageId") Long packageId);
}
