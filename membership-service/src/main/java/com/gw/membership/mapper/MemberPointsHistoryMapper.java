package com.gw.membership.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gw.membership.entity.MemberPointsHistoryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会员积分历史记录Mapper接口
 */
@Mapper
public interface MemberPointsHistoryMapper extends BaseMapper<MemberPointsHistoryEntity> {

    /**
     * 查询用户积分历史记录
     *
     * @param username 用户名
     * @return 积分历史记录列表
     */
    @Select("SELECT * FROM t_member_points_history WHERE username = #{username} AND deleted = 0 ORDER BY create_time DESC")
    List<MemberPointsHistoryEntity> selectByUsername(@Param("username") String username);

    /**
     * 分页查询用户积分历史记录
     *
     * @param page     分页参数
     * @param username 用户名
     * @return 分页结果
     */
    @Select("SELECT * FROM t_member_points_history WHERE username = #{username} AND deleted = 0 ORDER BY create_time DESC")
    IPage<MemberPointsHistoryEntity> pageByUsername(Page<?> page, @Param("username") String username);

    /**
     * 查询指定时间范围内的积分历史记录
     *
     * @param username  用户名
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 积分历史记录列表
     */
    @Select("SELECT * FROM t_member_points_history WHERE username = #{username} AND operate_time BETWEEN #{startTime} AND #{endTime} AND deleted = 0 ORDER BY operate_time DESC")
    List<MemberPointsHistoryEntity> selectByTimeRange(@Param("username") String username,
                                                      @Param("startTime") LocalDateTime startTime,
                                                      @Param("endTime") LocalDateTime endTime);
}
