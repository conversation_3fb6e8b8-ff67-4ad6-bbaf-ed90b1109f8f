package com.gw.membership.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gw.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 会员积分历史记录实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_member_points_history")
public class MemberPointsHistoryEntity extends BaseEntity {

    /**
     * 用户ID
     */
    private String username;

    /**
     * 变更类型: 1-增加, 2-消费
     */
    private Integer changeType;

    /**
     * 变更前积分
     */
    private Integer pointsBefore;

    /**
     * 变更后积分
     */
    private Integer pointsAfter;

    /**
     * 变更积分数量
     */
    private Integer pointsChange;

    /**
     * 变更原因
     */
    private String reason;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 关联订单ID（如果有）
     */
    private Long orderId;

    /**
     * 操作时间
     */
    private LocalDateTime operateTime;
}
