package com.gw.membership.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gw.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 会员套餐实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_membership_package")
public class MembershipPackageEntity extends BaseEntity {

    /**
     * 套餐名称
     */
    private String name;

    /**
     * 套餐描述
     */
    private String description;

    /**
     * 套餐价格(元)
     */
    private BigDecimal price;

    /**
     * 折扣价格(元)
     */
    private BigDecimal discountPrice;

    /**
     * 有效期(天)，0表示永久
     */
    private Integer validDays;

    /**
     * 套餐类型: 1-包月, 2-包季, 3-包年, 255-终身
     */
    private Integer type;

    /**
     * 套餐状态: 0-下架, 1-上架
     */
    private Integer status;

    /**
     * 排序优先级
     */
    private Integer sortOrder;

    /**
     * 是否推荐: 0-否, 1-是
     */
    private Integer isRecommended;

    /**
     * 套餐图标URL
     */
    private String iconUrl;

    /**
     * 是否允许叠加购买: 0-否, 1-是
     */
    private Integer allowStack;
    @TableField("vip_level")
    private Integer vipLevel = 1;
    /**
     * 套餐包含的权益列表，通过套餐权益关联表获取
     */
    @TableField(exist = false)
    private List<MembershipBenefitEntity> benefitDetails;
}