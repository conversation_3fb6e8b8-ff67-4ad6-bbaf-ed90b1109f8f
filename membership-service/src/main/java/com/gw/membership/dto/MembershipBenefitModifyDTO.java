package com.gw.membership.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;


@Data
public class MembershipBenefitModifyDTO {
    @Positive(message = "权益ID不能为空")
    @Schema(description = "权益ID")
    private Long id;
    /**
     * 重置周期: 1-日, 2-月, 3-周, 4-年 默认是1
     */
    @Schema(description = "重置周期: 1-日, 2-月, 3-周, 4-年 默认是1")
    @Positive(message = "重置周期不能为空")
    @Min(value = 1, message = "重置周期不能小于1")
    @Max(value = 4, message = "重置周期不能大于4")
    private Integer resetCycle;

    /**
     * 配额值，默认 -1 表示无限配额
     */
    @Schema(description = "配额值，默认 -1 表示无限配额")
    @NotNull(message = "配额值不能为空")
    private Integer quotaValue = 0;
}