package com.gw.membership.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户支出明细DTO
 */
@Data
public class UserExpenseRecordDTO {

    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 支出类型: 1-会员购买, 2-智能体购买, 3-其他消费
     */
    private Integer expenseType;

    /**
     * 支出金额
     */
    private BigDecimal amount;

    /**
     * 支出描述
     */
    private String description;

    /**
     * 支付方式: 1-支付宝, 2-微信, 3-Apple支付, 4-银行卡
     */
    private Integer paymentMethod;

    /**
     * 交易流水号
     */
    private String transactionId;

    /**
     * 状态: 0-失败, 1-成功, 2-退款
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
} 