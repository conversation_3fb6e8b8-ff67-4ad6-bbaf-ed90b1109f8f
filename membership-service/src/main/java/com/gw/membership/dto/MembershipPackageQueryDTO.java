package com.gw.membership.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 会员套餐DTO
 */
@Data
@Schema(description = "会员套餐查询DTO")
public class MembershipPackageQueryDTO {


    /**
     * 套餐名称
     */
    @NotBlank(message = "套餐名称不能为空")
    @Schema(description = "套餐名称")
    private String name;

    /**
     * 套餐价格(元)
     */
    @NotNull(message = "套餐价格不能为空")
    private BigDecimal price;

    /**
     * 折扣价格(元)
     */
    @Schema(description = "折扣价格(元)")
    private BigDecimal discountPrice;

    /**
     * 套餐状态: 2-下架, 1-上架
     */
    @Schema(description = "套餐状态: 2-下架, 1-上架")
    private Integer status = 1;

    /**
     * 关联的权益ID列表
     */
    private List<Long> benefitIds;
}