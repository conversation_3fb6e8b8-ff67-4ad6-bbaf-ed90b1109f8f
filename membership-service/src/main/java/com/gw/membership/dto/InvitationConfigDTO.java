package com.gw.membership.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 邀请码配置DTO
 */
@Data
public class InvitationConfigDTO {

    private Long id;

    /**
     * 奖励类型: 1-天数, 2-月, 3-季度, 4-年
     */
    private Integer rewardType;

    /**
     * 奖励值（对应reward_type的数量）
     */
    private Integer rewardValue;

    /**
     * 状态: 0-禁用, 1-启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
} 