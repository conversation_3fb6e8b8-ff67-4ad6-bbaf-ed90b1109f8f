package com.gw.membership.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 邀请码DTO
 */
@Data
public class InvitationCodeDTO {

    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 邀请码
     */
    private String code;

    /**
     * 状态: 0-已失效, 1-有效
     */
    private Integer status;

    /**
     * 已使用次数
     */
    private Integer usedTimes;

    /**
     * 最大使用次数, -1表示不限制
     */
    private Integer maxUseTimes;

    /**
     * 失效时间
     */
    private LocalDateTime expireTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
} 