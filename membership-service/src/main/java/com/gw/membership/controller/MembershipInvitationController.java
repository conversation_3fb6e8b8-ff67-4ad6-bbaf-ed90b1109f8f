package com.gw.membership.controller;

import com.github.pagehelper.PageInfo;
import com.gw.common.dto.PageBaseRequest;
import com.gw.common.dto.ResponseResult;
import com.gw.common.membership.dto.InvitationCodeQuery;
import com.gw.common.membership.dto.InvitationCodeUseDTO;
import com.gw.common.membership.vo.InvitationCodeVO;
import com.gw.common.user.constant.UserCommonCacheConstant;
import com.gw.common.user.context.UserContextUtil;
import com.gw.common.user.service.UserProxyService;
import com.gw.common.user.vo.UserBaseContentVo;
import com.gw.common.vo.PageBaseContentVo;
import com.gw.common.vo.PaginationVo;
import com.gw.membership.config.CacheProperties;
import com.gw.membership.dto.InvitationCodeValidateDTO;
import com.gw.membership.dto.MyInvitationRecordQuery;
import com.gw.membership.entity.InvitationCodeEntity;
import com.gw.membership.entity.InvitationHistoryEntity;
import com.gw.membership.mapper.ModelMapperConvert;
import com.gw.membership.service.InvitationService;
import com.gw.membership.vo.InvitationCodeValidateVO;
import com.gw.membership.vo.InvitationRecordVO;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.modelmapper.ModelMapper;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 会员邀请服务控制器
 */
@RestController
@RequestMapping("/api/v1/membership/invitation")
@RequiredArgsConstructor
@Tag(name = "邀请相关的服务接口", description = "邀请相关的服务接口服务接口相关API")
@Log4j2
public class MembershipInvitationController {
    private final InvitationService invitationService;
    private final UserProxyService userProxyService;
    private final CacheProperties cacheProperties;
    private final ModelMapper modelMapper = ModelMapperConvert.getBaseModelMapper();
    ;

    /**
     * 创建邀请码
     */
    @Operation(summary = "创建邀请码", description = "创建邀请码")
    @GetMapping("/code")
    @Hidden
    public ResponseResult<InvitationCodeVO> createInvitationCode() {
        String username = UserContextUtil.getCurrentUsername();
        InvitationCodeEntity entity = invitationService.getUserInvitationCode(username);
        return ResponseResult.success(modelMapper.map(entity, InvitationCodeVO.class));
    }

    @PostMapping("/code/code_by_username")
    @Hidden
    public ResponseResult<InvitationCodeVO> getCodeByUsername(@RequestBody InvitationCodeQuery params) {
        InvitationCodeEntity entity = invitationService.getUserInvitationCode(params.getUsername());
        if (entity == null) {
            entity = invitationService.createInvitationCode(params.getUsername());
        }
        log.info("{} 获得 邀请码 {}", params.getUsername(), entity.getCode());
        return ResponseResult.success(modelMapper.map(entity, InvitationCodeVO.class));
    }

    /**
     * 获取用户邀请码
     */
    @Operation(summary = "获取用户邀请码", description = "创建邀请码")
    @GetMapping("/get_code")
    public ResponseResult<InvitationCodeVO> getUserInvitationCode() {
        String username = UserContextUtil.getCurrentUsername();
        InvitationCodeEntity entity = invitationService.getUserInvitationCode(username);
        return ResponseResult.success(modelMapper.map(entity, InvitationCodeVO.class));
    }

    /**
     * 验证邀请码
     */
    @Operation(summary = "验证邀请码", description = "验证邀请码")
    @GetMapping("/code/validate")
    @Hidden
    public ResponseResult<InvitationCodeValidateVO> validateInvitationCode(@RequestBody @Valid InvitationCodeValidateDTO params) {
        boolean valid = invitationService.validateInvitationCode(params.getCode());
        return ResponseResult.success(new InvitationCodeValidateVO(params.getCode(), valid));
    }

    /**
     * 使用邀请码
     */
    @Operation(summary = "使用邀请码", description = "使用邀请码")
    @PostMapping("/code/use")
    @Hidden
    public ResponseResult<?> useInvitationCode(@RequestBody @Valid InvitationCodeUseDTO params) {
        String invitee = params.getUsername();
        log.info("邀请码 {}，被邀请人 {}", params.getCode(), invitee);
        invitationService.useInvitationCode(params.getCode(), invitee);
        return ResponseResult.success(null);
    }

    @Operation(summary = "分页获取邀请记录", description = "分页获取邀请记录记录")
    @PostMapping("/admin/page")
    public ResponseResult<PageBaseContentVo<InvitationRecordVO>> adminGetInviteHistoryPage(
            @RequestBody @Valid PageBaseRequest<MyInvitationRecordQuery> params) {
        if (params.getFilter() == null) {
            params.setFilter(new MyInvitationRecordQuery());
        }
        String username = params.getFilter().getUsername();
        params.getFilter().setInviter(username);
        return getInvitationRecordPage(params);
    }

    @Operation(summary = "分页获取我的邀请记录", description = "分页获取我的邀请记录记录")
    @PostMapping("/my/page")
    public ResponseResult<PageBaseContentVo<InvitationRecordVO>> getMyInviteHistoryPage(
            @RequestBody @Valid PageBaseRequest<MyInvitationRecordQuery> params) {
        if (params.getFilter() == null) {
            params.setFilter(new MyInvitationRecordQuery());
        }
        params.getFilter().setInviter(UserContextUtil.getCurrentUsername());
        return getInvitationRecordPage(params);
    }

    private ResponseResult<PageBaseContentVo<InvitationRecordVO>> getInvitationRecordPage(PageBaseRequest<MyInvitationRecordQuery> params) {
        PageInfo<InvitationHistoryEntity> historyPage = invitationService.getInvitationRecordPage(
                params.getCurrent(), params.getPageSize(), params.getFilter());

        PaginationVo pagination = new PaginationVo(
                historyPage.getTotal(),
                historyPage.getPageNum(),
                historyPage.getPageSize());

        if (historyPage.getList() == null || historyPage.getList().isEmpty()) {
            return ResponseResult.success(new PageBaseContentVo<>(new ArrayList<>(), pagination));
        }
        Set<String> usernames = historyPage.getList().stream()
                .flatMap(history -> Stream.of(history.getInviter(), history.getInvitee()))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        
        Map<String, UserBaseContentVo> userMap = userProxyService.findAllUserMapByUsernames(
                cacheProperties.getCacheName(UserCommonCacheConstant.USER_BASE_MAP_CACHE_KEY),
                new ArrayList<>(usernames));

        List<InvitationRecordVO> rows = historyPage.getList().stream()
                .map(history -> {
                    InvitationRecordVO vo = new InvitationRecordVO();
                    UserBaseContentVo user = userMap.get(history.getInvitee());
                    if (user == null) {
                        vo.setNickname(history.getInvitee());
                    } else {
                        vo.setAvatar(user.getAvatar());
                        vo.setNickname(user.getNickname());
                        vo.setIdentify(user.getIdentify() == null ? "" : user.getIdentify());
                    }
                    vo.setCreateTime(history.getCreateTime());
                    return vo;
                })
                .toList();

        return ResponseResult.success(new PageBaseContentVo<>(rows, pagination));
    }
}