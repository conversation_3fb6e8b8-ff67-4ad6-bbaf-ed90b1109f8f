package com.gw.membership.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gw.common.exception.BusinessException;
import com.gw.common.membership.constant.MemberCacheConstant;
import com.gw.common.notify.constant.NotifyConstant;
import com.gw.common.notify.dto.SystemNotifySubmitDTO;
import com.gw.common.notify.service.NotifyProxyService;
import com.gw.membership.config.CacheProperties;
import com.gw.membership.constant.MemberConstants;
import com.gw.membership.constant.MemberLocalCacheConstant;
import com.gw.membership.constant.OperationTypeConstants;
import com.gw.membership.constant.RewardConstants;
import com.gw.membership.entity.MembershipPackageEntity;
import com.gw.membership.entity.RewardConfigEntity;
import com.gw.membership.entity.UserMembershipEntity;
import com.gw.membership.enums.AutoRenewStatus;
import com.gw.membership.enums.MembershipStatus;
import com.gw.membership.enums.MembershipType;
import com.gw.membership.mapper.MembershipPackageMapper;
import com.gw.membership.mapper.ModelMapperConvert;
import com.gw.membership.mapper.UserMembershipMapper;
import com.gw.membership.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.modelmapper.ModelMapper;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 用户会员服务实现类
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class UserMembershipServiceImpl extends ServiceImpl<UserMembershipMapper, UserMembershipEntity>
        implements UserMembershipService {

    // 常量定义
    private static final int NORMAL_MEMBER_LEVEL = 0;
    private static final int VIP_LEVEL = 1;

    private static final int HIGH_LEVEL_PACKAGE_TYPE = 1;
    private static final long DEFAULT_PACKAGE_ID = 0L;
    private static final int PERMANENT_MEMBERSHIP_YEARS = 100;

    // 积分操作类型常量
    private static final int POINTS_OPERATION_ADD = 1;
    private static final int POINTS_OPERATION_CONSUME = 2;

    // 分页常量
    private static final int DEFAULT_PAGE_SIZE = 20;
    private static final int MAX_RESULT_SIZE = 1000;
    // 定义批处理大小常量
    private static final int BATCH_SIZE = 100;
    private final MembershipPackageMapper packageMapper;
    private final MembershipPackageService packageService;
    private final UserMembershipHistoryService membershipHistoryService;
    private final RewardConfigService rewardConfigService;
    private final RedisTemplate<String, Object> redisTemplate;
    private final CacheProperties cacheProperties;
    private final MemberPointsHistoryService memberPointsHistoryService;
    private final CacheManager cacheManager;
    private final NotifyProxyService notifyProxyService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Cacheable(value = MemberLocalCacheConstant.USER_MEMBERSHIP_VALUE, key = "'member:' + #username", unless = "#result == null")
    public UserMembershipEntity findOrInitUserMembership(String username) {
        // 检查用户会员信息是否已存在
        UserMembershipEntity existingMembership = this.baseMapper.selectByUsername(username);
        if (existingMembership != null) {
            return existingMembership; // 已存在，直接返回
        }

        // 创建初始会员信息
        log.info("初始化用户{}会员信息", username);
        LocalDateTime now = LocalDateTime.now();
        UserMembershipEntity userMembership = new UserMembershipEntity();
        userMembership.setUsername(username);
        userMembership.setStatus(MembershipStatus.NON_MEMBER.getCode());
        userMembership.setVipLevel(NORMAL_MEMBER_LEVEL);
        userMembership.setMemberPoints(0);
        userMembership.setAutoRenew(AutoRenewStatus.OFF.getCode());
        userMembership.setTotalPayAmount(BigDecimal.ZERO);
        userMembership.setPackageId(DEFAULT_PACKAGE_ID);
        userMembership.setCreateTime(now);
        userMembership.setUpdateTime(now);
        userMembership.setStartTime(now);

        try {
            this.baseMapper.insert(userMembership);
            log.debug("初始化用户{}会员信息成功", username);
            return userMembership;
        } catch (Exception e) {
            log.error("初始化用户{}会员信息失败: {}", username, e.getMessage(), e);
            throw new BusinessException("初始化用户会员信息失败");
        }
    }

    public void update(UserMembershipEntity entity) {
        this.baseMapper.updateById(entity);
        // 修复缓存键名称，确保正确清除Redis缓存
        redisTemplate.delete(cacheProperties.getCacheName(MemberCacheConstant.MEMBERSHIP_BASE_CACHE_KEY + ":" + entity.getUsername()));
        // 修复本地缓存清除，使用正确的参数名
        var cache = cacheManager.getCache(MemberLocalCacheConstant.USER_MEMBERSHIP_VALUE);
        if (cache != null) {
            cache.evict("member:" + entity.getUsername());
        }
    }

    @Override
    public boolean existMembershipByUsername(String username) {
        return this.baseMapper.findByUsername(username).isPresent();
    }

    @Override
    public Map<String, UserMembershipEntity> findByUsernames(List<String> username) {
        return Map.of();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CachePut(value = MemberLocalCacheConstant.USER_MEMBERSHIP_VALUE, key = "'member:' + #userMembership.username", unless = "#result == null")
    public UserMembershipEntity checkAndUpdateMembershipStatus(UserMembershipEntity userMembership) {
        LocalDateTime now = LocalDateTime.now();

        // 检查会员是否过期并更新状态
        checkAndUpdateExpiration(userMembership, now);

        // 计算会员过期相关信息
        updateExpirationDetails(userMembership, now);

        // 加载套餐信息
        loadPackageDetails(userMembership);

        return userMembership;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean activateMembershipFromPackage(String username, Long packageId, Long orderId, Integer days) {
        try {
            // 验证套餐是否存在
            MembershipPackageEntity packageEntity = validatePackage(packageId);

            // 查询或初始化用户会员信息
            UserMembershipEntity userMembership = findOrInitUserMembership(username);
            UserMembershipEntity beforeMembership = copyUserMembership(userMembership);

            // 计算过期时间 - 使用套餐类型
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime expireTime = calculateMembershipExpireTime(null, now, packageEntity.getType(), days, false);

            // 更新会员信息
            updateMembershipInfo(userMembership, packageId, packageEntity, now, expireTime, orderId);

            // 记录会员历史 - 新开通会员
            membershipHistoryService.recordMembershipHistory(
                    beforeMembership,
                    userMembership,
                    OperationTypeConstants.TYPE_NEW, // 新开通
                    orderId,
                    username,
                    null,
                    "首次开通会员"
            );

            log.info("用户{}已激活会员，套餐ID:{}，套餐类型:{}，有效期至:{}",
                    username, packageId, packageEntity.getType(), expireTime);
            notifyProxyService.insertSystemMessage(new SystemNotifySubmitDTO(packageEntity.getName() + "会员开通成功",
                    NotifyConstant.LEVEL_NORMAL, username));
            return true;
        } catch (Exception e) {
            log.error("激活会员失败: {}", e.getMessage(), e);
            throw new BusinessException("激活会员失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public boolean renewMembershipFromPackage(String username, Long packageId, Long orderId, Integer days) {
        try {
            // 验证套餐是否存在
            MembershipPackageEntity packageEntity = validatePackage(packageId);

            // 查询用户会员信息
            UserMembershipEntity userMembership = this.baseMapper.selectByUsername(username);
            if (userMembership == null) {
                // 如果不存在，改为激活会员
                log.info("用户{}不存在会员记录，改为激活会员", username);
                return activateMembershipFromPackage(username, packageId, orderId, days);
            }

            // 保存变更前的状态
            UserMembershipEntity beforeMembership = copyUserMembership(userMembership);

            // 计算新的过期时间 - 使用套餐类型
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime expireTime = calculateMembershipExpireTime(userMembership, now, packageEntity.getType(), days, true);
            log.debug("用户{}会员续费，新的过期时间为{}", username, expireTime);

            // 更新会员信息
            updateMembershipInfo(userMembership, packageId, packageEntity, now, expireTime, orderId);

            // 记录会员历史 - 续费会员
            membershipHistoryService.recordMembershipHistory(
                    beforeMembership,
                    userMembership,
                    OperationTypeConstants.TYPE_RENEWAL, // 续费
                    orderId,
                    username,
                    null,
                    "续费会员服务"
            );

            log.info("用户{}已续费会员，套餐ID:{}，套餐类型:{}，有效期至:{}",
                    username, packageId, packageEntity.getType(), expireTime);
            notifyProxyService.insertSystemMessage(new SystemNotifySubmitDTO(packageEntity.getName() + "会员续费成功",
                    NotifyConstant.LEVEL_NORMAL, username));
            return true;
        } catch (Exception e) {
            log.error("续费会员失败: {}", e.getMessage(), e);
            throw new BusinessException("续费会员失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public boolean cancelMembership(String username) {
        try {
            // 查询用户会员信息
            UserMembershipEntity userMembership = this.baseMapper.selectByUsername(username);
            if (userMembership == null || userMembership.getStatus() == MembershipStatus.NON_MEMBER.getCode()) {
                return true; // 非会员状态或不存在，直接返回成功
            }

            // 保存变更前的状态
            UserMembershipEntity beforeMembership = copyUserMembership(userMembership);

            // 更新为非会员状态
            userMembership.setStatus(MembershipStatus.NON_MEMBER.getCode());
            userMembership.setUpdateTime(LocalDateTime.now());
            userMembership.setAutoRenew(AutoRenewStatus.OFF.getCode()); // 取消自动续费

            this.update(userMembership);

            // 记录会员历史 - 取消会员
            membershipHistoryService.recordMembershipHistory(
                    beforeMembership,
                    userMembership,
                    OperationTypeConstants.TYPE_CANCEL, // 取消
                    null,
                    username,
                    null,
                    "用户主动取消会员"
            );

            log.info("用户{}已取消会员", username);
            return true;
        } catch (Exception e) {
            log.error("取消会员失败: {}", e.getMessage(), e);
            throw new BusinessException("取消会员失败: " + e.getMessage());
        }
    }

    @Override
    public Boolean checkUserMembership(String username) {
        return this.baseMapper.selectByUsername(username) != null;
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public boolean updateAutoRenew(String username, Boolean autoRenew) {
        try {
            // 查询用户会员信息
            UserMembershipEntity userMembership = findOrInitUserMembership(username);

            // 更新自动续费状态
            userMembership.setAutoRenew(autoRenew ? AutoRenewStatus.ON.getCode() : AutoRenewStatus.OFF.getCode());
            userMembership.setUpdateTime(LocalDateTime.now());

            this.update(userMembership);
            log.info("用户{}已{}自动续费", username, autoRenew ? "开启" : "关闭");
            return true;
        } catch (Exception e) {
            log.error("更新自动续费状态失败: {}", e.getMessage(), e);
            throw new BusinessException("更新自动续费状态失败: " + e.getMessage());
        }
    }

    @Override
    public boolean checkMembershipExpired(String username) {
        // 查询用户会员信息
        UserMembershipEntity userMembership = this.baseMapper.selectByUsername(username);

        // 检查是否为非会员或不存在或无过期时间
        if (userMembership == null ||
                userMembership.getStatus() == MembershipStatus.NON_MEMBER.getCode() ||
                userMembership.getExpireTime() == null) {
            return true;
        }

        return userMembership.getExpireTime().isBefore(LocalDateTime.now());
    }

    @Transactional
    @Override
    public boolean giftMembership(String username, Integer days, String reason, String operator) {
        if (days <= 0) {
            throw new BusinessException("赠送天数必须大于0");
        }

        try {
            // 查询或初始化用户会员信息
            UserMembershipEntity userMembership = findOrInitUserMembership(username);
            UserMembershipEntity beforeMembership = copyUserMembership(userMembership);

            // 计算新的过期时间
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime expireTime = calculateMembershipExpireTime(userMembership, now, MemberConstants.TYPE_DAILY, days, true);

            // 更新会员信息
            userMembership.setStatus(MembershipStatus.ACTIVE_MEMBER.getCode());
            userMembership.setExpireTime(expireTime);
            userMembership.setUpdateTime(now);
            if (userMembership.getVipLevel() == null || userMembership.getVipLevel() < 1) {
                userMembership.setVipLevel(VIP_LEVEL);
            }
            this.update(userMembership);

            // 记录会员历史
            membershipHistoryService.recordMembershipHistory(
                    beforeMembership,
                    userMembership,
                    OperationTypeConstants.TYPE_GIFT, // 赠送
                    null,
                    operator,
                    username,
                    reason
            );

            log.info("管理员{}已为用户{}赠送会员{}天，原因:{}，有效期至:{}",
                    operator, username, days, reason, expireTime);
            notifyProxyService.insertSystemMessage(new SystemNotifySubmitDTO("系统赠送" + days + "天会员时长",
                    NotifyConstant.LEVEL_NORMAL, username));
            return true;
        } catch (Exception e) {
            log.error("赠送会员失败: {}", e.getMessage(), e);
            throw new BusinessException("赠送会员失败: " + e.getMessage());
        }
    }

    @Scheduled(cron = "0 0 1 * * ?") // 每天凌晨1点执行
    public void processExpiredMemberships() {
        log.info("开始处理过期会员...");

        try {
            // 获取当前时间
            LocalDateTime now = LocalDateTime.now();

            // 查询已过期的会员
            List<UserMembershipEntity> expiredMembers = this.baseMapper.selectExpired(now);
            int totalExpired = expiredMembers.size();

            log.info("发现{}个过期会员需要处理", totalExpired);

            if (totalExpired == 0) {
                log.info("没有过期会员需要处理");
                return;
            }

            // 分批处理
            int processedCount = 0;

            // 将列表分成多个批次处理
            for (int i = 0; i < totalExpired; i += BATCH_SIZE) {
                int endIndex = Math.min(i + BATCH_SIZE, totalExpired);
                List<UserMembershipEntity> expiredBatch = expiredMembers.subList(i, endIndex);

                // 处理当前批次
                processBatchExpiredMemberships(expiredBatch, now);

                // 更新计数器
                processedCount += expiredBatch.size();

                log.info("已处理 {}/{} 个过期会员", processedCount, totalExpired);
            }

            log.info("过期会员处理完成，共处理{}个账户", processedCount);
        } catch (Exception e) {
            log.error("批量处理过期会员失败: {}", e.getMessage(), e);
            // 不抛出异常，避免整个任务失败
        }
    }

    /**
     * 处理一批过期会员
     *
     * @param expiredMembers 过期会员列表
     * @param now            当前时间
     */
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public void processBatchExpiredMemberships(List<UserMembershipEntity> expiredMembers, LocalDateTime now) {
        for (UserMembershipEntity member : expiredMembers) {
            try {
                // 保存变更前的状态
                UserMembershipEntity beforeMembership = copyUserMembership(member);

                // 更新会员状态为非会员
                member.setStatus(MembershipStatus.NON_MEMBER.getCode());
                member.setVipLevel(NORMAL_MEMBER_LEVEL);
                member.setUpdateTime(now);
                this.update(member);

                // 记录会员历史 - 过期
                membershipHistoryService.recordMembershipHistory(
                        beforeMembership,
                        member,
                        OperationTypeConstants.TYPE_EXPIRED, // 过期
                        null,
                        "system",
                        null,
                        "会员已过期"
                );

                log.info("用户{}的会员已过期，已更新为非会员状态", member.getUsername());
                notifyProxyService.insertSystemMessage(new SystemNotifySubmitDTO("会员已过期",
                        NotifyConstant.LEVEL_NORMAL, member.getUsername()));
            } catch (Exception e) {
                log.error("处理用户{}的过期会员信息失败: {}", member.getUsername(), e.getMessage(), e);
                // 捕获异常但继续处理下一个，避免一个失败导致整批失败
            }
        }
    }

    @Override
    public List<UserMembershipEntity> getSoonToExpireMembers(Integer days) {
        // 计算日期范围
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime expireEnd = now.plusDays(days);

        // 查询即将过期的会员
        List<UserMembershipEntity> members = this.baseMapper.selectSoonToExpire(now, expireEnd);

        // 限制返回数量，避免内存溢出
        if (members.size() > MAX_RESULT_SIZE) {
            log.warn("即将过期会员数量超过{}，已截断结果集", MAX_RESULT_SIZE);
            return members.subList(0, MAX_RESULT_SIZE);
        }

        return members;
    }

    /**
     * 分页获取即将过期的会员
     *
     * @param days     天数
     * @param pageSize 每页大小
     * @param pageNum  页码（从1开始）
     * @return 即将过期的会员列表
     */
    public List<UserMembershipEntity> getSoonToExpireMembersPaged(Integer days, Integer pageSize, Integer pageNum) {
        if (pageSize == null || pageSize <= 0) {
            pageSize = DEFAULT_PAGE_SIZE; // 默认每页条数
        }
        if (pageNum == null || pageNum <= 0) {
            pageNum = 1; // 默认第1页
        }

        // 计算日期范围
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime expireEnd = now.plusDays(days);

        // 获取所有即将过期的会员
        List<UserMembershipEntity> allMembers = this.baseMapper.selectSoonToExpire(now, expireEnd);

        // 手动分页
        int startIndex = (pageNum - 1) * pageSize;
        if (startIndex >= allMembers.size()) {
            return new ArrayList<>(); // 超出范围，返回空列表
        }

        int endIndex = Math.min(startIndex + pageSize, allMembers.size());
        return allMembers.subList(startIndex, endIndex);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = MemberLocalCacheConstant.USER_MEMBERSHIP_VALUE, key = "'member:' + #username")
    public boolean extendMembershipFromSystem(String username, Integer extendDays) {
        if (extendDays <= 0) {
            throw new BusinessException("延长天数必须大于0");
        }

        try {
            // 查询用户会员信息
            UserMembershipEntity userMembership = findOrInitUserMembership(username);

            // 计算新的过期时间
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime expireTime = calculateMembershipExpireTime(userMembership, now, MemberConstants.TYPE_DAILY, extendDays, true);

            // 更新会员信息
            userMembership.setStatus(MembershipStatus.ACTIVE_MEMBER.getCode());
            userMembership.setExpireTime(expireTime);
            userMembership.setUpdateTime(now);
            if (userMembership.getVipLevel() == null || userMembership.getVipLevel() < 1) {
                userMembership.setVipLevel(VIP_LEVEL);
            }
            this.update(userMembership);

            log.info("管理员已为用户{}延长会员{}天，有效期至:{}", username, extendDays, expireTime);
            return true;
        } catch (Exception e) {
            log.error("延长会员有效期失败: {}", e.getMessage(), e);
            throw new BusinessException("延长会员有效期失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public boolean addMemberPoints(String username, Integer points, String reason) {
        validatePositivePoints(points);

        try {
            UserMembershipEntity userMembership = findOrInitUserMembership(username);

            // 更新积分
            Integer currentPoints = userMembership.getMemberPoints();
            Integer newPoints = currentPoints + points;
            userMembership.setMemberPoints(newPoints);
            userMembership.setUpdateTime(LocalDateTime.now());

            this.update(userMembership);

            // 记录积分变更历史
            memberPointsHistoryService.recordPointsChange(
                    username,
                    POINTS_OPERATION_ADD, // 增加积分
                    currentPoints,
                    newPoints,
                    points,
                    reason,
                    "system",
                    null
            );

            log.info("用户{}积分增加{}，原因:{}，当前总积分:{}",
                    username, points, reason, userMembership.getMemberPoints());
            return true;
        } catch (Exception e) {
            log.error("增加会员积分失败: {}", e.getMessage(), e);
            throw new BusinessException("增加会员积分失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public boolean consumeMemberPoints(String username, Integer points, String reason) {
        validatePositivePoints(points);

        try {
            // 查询用户会员信息
            UserMembershipEntity userMembership = this.baseMapper.selectByUsername(username);
            if (userMembership == null) {
                throw new BusinessException("用户会员信息不存在");
            }

            // 检查积分是否足够
            Integer currentPoints = userMembership.getMemberPoints();
            if (currentPoints < points) {
                throw new BusinessException("积分不足");
            }

            // 更新积分
            Integer newPoints = currentPoints - points;
            userMembership.setMemberPoints(newPoints);
            userMembership.setUpdateTime(LocalDateTime.now());

            this.update(userMembership);

            // 记录积分变更历史
            memberPointsHistoryService.recordPointsChange(
                    username,
                    POINTS_OPERATION_CONSUME, // 消费积分
                    currentPoints,
                    newPoints,
                    points,
                    reason,
                    "system",
                    null
            );

            log.info("用户{}积分消费{}，原因:{}，当前剩余积分:{}",
                    username, points, reason, userMembership.getMemberPoints());
            return true;
        } catch (BusinessException be) {
            log.warn("消费会员积分失败: {}", be.getMessage());
            throw be;
        } catch (Exception e) {
            log.error("消费会员积分失败: {}", e.getMessage(), e);
            throw new BusinessException("消费会员积分失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void newUserRegister(String username) {
        // 获取邀请奖励配置
        RewardConfigEntity config = rewardConfigService.findByCode(RewardConstants.REGISTER_CODE);
        if (config == null || config.getStatus() != 1) {
            log.warn("新用户奖励配置不存在或未启用");
            return;
        }

        Optional<UserMembershipEntity> userMembershipOptional = this.baseMapper.findByUsername(username);
        if (userMembershipOptional.isPresent()) {
            log.info("用户{}已存在，跳过新用户奖励", username);
            return;
        }

        try {
            // 计算奖励天数
            int validDays = calculateValidDays(config.getRewardType(), config.getRewardValue());
            if (validDays <= 0) {
                log.warn("邀请奖励天数计算为0，跳过奖励");
                return;
            }

            // 查询或初始化邀请人的会员信息
            UserMembershipEntity userMembership = findOrInitUserMembership(username);

            // 记录变更前的状态
            UserMembershipEntity beforeMembership = copyUserMembership(userMembership);

            // 直接调用会员服务扩展会员有效期
            boolean result = extendMembershipFromSystem(username, validDays);

            if (result) {
                log.info("成功为新用户 {} 奖励会员权益，天数: {}", username, validDays);

                // 查询更新后的会员信息以记录历史
                UserMembershipEntity afterMembership = this.baseMapper.selectByUsername(username);

                // 额外记录邀请获得会员的历史记录
                membershipHistoryService.recordMembershipHistory(
                        beforeMembership,
                        afterMembership,
                        OperationTypeConstants.TYPE_NEW_USER, // 操作类型: 5-邀请获得
                        null, // 无关联订单
                        "system",
                        username,
                        "通过新用户注册获得 " + validDays + " 天会员奖励"
                );
                notifyProxyService.insertSystemMessage(new SystemNotifySubmitDTO("新用户奖励", "新用户注册赠送" + validDays + "天会员时长",
                        NotifyConstant.LEVEL_NORMAL, username));
            } else {
                log.error("为新用户 {} 激活邀请奖励会员权益失败", username);
            }
        } catch (Exception e) {
            log.error("处理新用户奖励时发生错误", e);
            throw e;
        }
    }

    @Override
    public double calculateTodayIncome(LocalDateTime startTime, LocalDateTime endTime) {
        return this.baseMapper.calculateIncomeByTimeRange(startTime, endTime);
    }

    @Override
    public int countTotalVipUsers() {
        return this.baseMapper.countTotalVipUsers();
    }

    @Override
    public double calculateTotalIncome() {
        return this.baseMapper.calculateTotalIncome();
    }

    // ========== 抽取的私有辅助方法 ==========

    /**
     * 验证套餐是否存在且有效
     */
    private MembershipPackageEntity validatePackage(Long packageId) {
        MembershipPackageEntity packageEntity = packageMapper.selectById(packageId);
        if (packageEntity == null || packageEntity.getStatus() == 0) {
            throw new BusinessException("套餐不存在或已下架");
        }
        return packageEntity;
    }

    /**
     * 检查积分必须为正数
     */
    private void validatePositivePoints(Integer points) {
        if (points <= 0) {
            throw new BusinessException("积分必须大于0");
        }
    }

    /**
     * 检查会员是否过期并更新状态
     */
    @Override
    public void checkAndUpdateExpiration(UserMembershipEntity userMembership, LocalDateTime now) {
        log.info("检查会员{}是否过期", JSON.toJSONString(userMembership));
        if (userMembership.getStatus() == MembershipStatus.ACTIVE_MEMBER.getCode() &&
                userMembership.getExpireTime() != null &&
                userMembership.getExpireTime().isBefore(now)) {
            // 会员已过期，更新状态
            userMembership.setStatus(MembershipStatus.NON_MEMBER.getCode());
            userMembership.setUpdateTime(now);
            userMembership.setVipLevel(NORMAL_MEMBER_LEVEL);
            this.update(userMembership);
            log.info("用户{}会员已过期，更新为非会员状态", userMembership.getUsername());
        } else if (userMembership.getStatus() == MembershipStatus.NON_MEMBER.getCode() && userMembership.getVipLevel() != NORMAL_MEMBER_LEVEL) {

            userMembership.setUpdateTime(now);
            userMembership.setVipLevel(NORMAL_MEMBER_LEVEL);
            this.update(userMembership);
        }
    }

    /**
     * 更新会员过期相关信息
     */
    private void updateExpirationDetails(UserMembershipEntity userMembership, LocalDateTime now) {
        if (userMembership.getStatus() == MembershipStatus.ACTIVE_MEMBER.getCode() && userMembership.getExpireTime() != null) {
            boolean isExpired = now.isAfter(userMembership.getExpireTime());
            userMembership.setIsExpired(isExpired);

            if (!isExpired) {
                // 计算剩余天数
                long days = ChronoUnit.DAYS.between(now, userMembership.getExpireTime()) + 1;
                userMembership.setRemainingDays((int) days);
            } else {
                userMembership.setRemainingDays(0);
            }
        } else {
            userMembership.setIsExpired(true);
            userMembership.setRemainingDays(0);
        }
    }

    /**
     * 加载套餐详细信息
     */
    private void loadPackageDetails(UserMembershipEntity userMembership) {
        if (userMembership.getPackageId() != null && userMembership.getPackageId() > 0) {
            MembershipPackageEntity packageEntity = packageService.findById(userMembership.getPackageId());
            if (packageEntity != null) {
                userMembership.setPackageName(packageEntity.getName());
                userMembership.setPackageDetail(packageEntity);
            }
        }
    }

    /**
     * 更新会员VIP等级信息
     */
    private void updateVipLevel(UserMembershipEntity userMembership, MembershipPackageEntity packageEntity) {
        userMembership.setVipLevel(packageEntity.getVipLevel());
    }

    /**
     * 统一计算会员过期时间
     *
     * @param userMembership 用户会员信息，如果为null则表示新会员
     * @param baseTime       基准时间（当前时间或原过期时间）
     * @param packageType    套餐类型: 1-包月, 2-包季, 3-包年, 4-终身, 5-按日
     * @param days           天数（仅当packageType=5时使用）
     * @param isRenewal      是否为续期操作
     * @return 计算后的过期时间
     */
    private LocalDateTime calculateMembershipExpireTime(UserMembershipEntity userMembership,
                                                        LocalDateTime baseTime,
                                                        Integer packageType,
                                                        Integer days,
                                                        boolean isRenewal) {
        // 参数校验
        if (baseTime == null) {
            baseTime = LocalDateTime.now();
        }

        if (packageType == null) {
            packageType = MemberConstants.TYPE_DAILY; // 默认按日计算
        }

        if (days == null) {
            days = 1; // 默认1天
        }

        // 如果是终身会员套餐
        if (packageType == MembershipType.LIFETIME.getCode()) {
            return baseTime.plusYears(PERMANENT_MEMBERSHIP_YEARS);
        }

        // 如果是续期且当前是有效会员
        if (isRenewal && userMembership != null && isActiveMemberNotExpired(userMembership, LocalDateTime.now())) {
            LocalDateTime expireTime = userMembership.getExpireTime();
            if (expireTime == null) {
                expireTime = baseTime; // 防止空指针
            }

            // 根据套餐类型计算新的过期时间
            return switch (packageType) {
                case MemberConstants.TYPE_MONTHLY -> expireTime.plusMonths(1); // 包月
                case MemberConstants.TYPE_QUARTERLY -> expireTime.plusMonths(3); // 包季
                case MemberConstants.TYPE_YEARLY -> expireTime.plusYears(1); // 包年
                case MemberConstants.TYPE_DAILY -> expireTime.plusDays(days); // 按日
                case MemberConstants.TYPE_PERMANENT -> baseTime.plusYears(PERMANENT_MEMBERSHIP_YEARS); // 终身
                default -> expireTime.plusDays(days); // 默认按天
            };
        } else {
            // 新会员或已过期会员，从当前时间开始计算
            if (userMembership != null) {
                userMembership.setStartTime(baseTime);
            }

            // 根据套餐类型计算过期时间
            return switch (packageType) {
                case MemberConstants.TYPE_MONTHLY -> baseTime.plusMonths(1); // 包月
                case MemberConstants.TYPE_QUARTERLY -> baseTime.plusMonths(3); // 包季
                case MemberConstants.TYPE_YEARLY -> baseTime.plusYears(1); // 包年
                case MemberConstants.TYPE_DAILY -> baseTime.plusDays(days); // 按日
                case MemberConstants.TYPE_PERMANENT -> baseTime.plusYears(PERMANENT_MEMBERSHIP_YEARS); // 终身
                default -> baseTime.plusDays(days); // 默认按天
            };
        }
    }

    /**
     * 判断是否为活跃且未过期的会员
     */
    private boolean isActiveMemberNotExpired(UserMembershipEntity userMembership, LocalDateTime now) {
        return userMembership.getStatus() == MembershipStatus.ACTIVE_MEMBER.getCode() &&
                userMembership.getExpireTime() != null &&
                userMembership.getExpireTime().isAfter(now);
    }

    /**
     * 更新会员信息
     */
    private void updateMembershipInfo(UserMembershipEntity userMembership, Long packageId,
                                      MembershipPackageEntity packageEntity, LocalDateTime now,
                                      LocalDateTime expireTime, Long orderId) {
        userMembership.setPackageId(packageId);
        userMembership.setStatus(MembershipStatus.ACTIVE_MEMBER.getCode());
        userMembership.setExpireTime(expireTime);
        userMembership.setLastOrderId(orderId);
        userMembership.setUpdateTime(now);

        // 如果是高级套餐，提升会员等级
        updateVipLevel(userMembership, packageEntity);

        this.update(userMembership);
    }

    /**
     * 创建会员信息的副本，用于记录历史
     */
    @Override
    public UserMembershipEntity copyUserMembership(UserMembershipEntity source) {
        if (source == null) {
            return null;
        }
        ModelMapper modelMapper = ModelMapperConvert.getBaseModelMapper();
        return modelMapper.map(source, UserMembershipEntity.class);
    }


    /**
     * 根据配置计算有效天数
     */
    @Override
    public int calculateValidDays(Integer rewardType, Integer rewardValue) {
        if (rewardType == null || rewardValue == null) {
            return 0;
        }

        return switch (rewardType) {
            case 1 -> // 天数
                    rewardValue;
            case 2 -> // 月
                    rewardValue * 30;
            case 3 -> // 季度
                    rewardValue * 90;
            case 4 -> // 年
                    rewardValue * 365;
            default -> 0;
        };
    }
}