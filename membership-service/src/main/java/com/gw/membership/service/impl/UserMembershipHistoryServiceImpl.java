package com.gw.membership.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gw.membership.constant.OperationTypeConstants;
import com.gw.membership.entity.MembershipPackageEntity;
import com.gw.membership.entity.UserMembershipEntity;
import com.gw.membership.entity.UserMembershipHistoryEntity;
import com.gw.membership.mapper.UserMembershipHistoryMapper;
import com.gw.membership.service.MembershipPackageService;
import com.gw.membership.service.UserMembershipHistoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户会员历史记录服务实现
 */
@Service
@RequiredArgsConstructor
public class UserMembershipHistoryServiceImpl extends ServiceImpl<UserMembershipHistoryMapper, UserMembershipHistoryEntity> implements UserMembershipHistoryService {

    private final MembershipPackageService packageService;

    @Override
    public boolean recordMembershipHistory(
            UserMembershipEntity beforeMembership,
            UserMembershipEntity afterMembership,
            Integer operationType,
            Long orderId,
            String operator,
            String inviteUsername,
            String remark
    ) {
        UserMembershipHistoryEntity history = new UserMembershipHistoryEntity();
        history.setUsername(afterMembership.getUsername());
        history.setOperationType(operationType);
        history.setOrderId(orderId);

        // 设置套餐信息
        if (afterMembership.getPackageId() != null && afterMembership.getPackageId() > 0) {
            history.setPackageId(afterMembership.getPackageId());
            MembershipPackageEntity packageEntity = packageService.findById(afterMembership.getPackageId());
            if (packageEntity != null) {
                history.setPackageName(packageEntity.getName());
            }
        }

        // 设置状态变更前后信息
        if (beforeMembership != null) {
            history.setBeforeStatus(beforeMembership.getStatus());
            history.setBeforeExpireTime(beforeMembership.getExpireTime());
        } else {
            history.setBeforeStatus(0); // 默认为非会员
            history.setBeforeExpireTime(null);
        }

        history.setAfterStatus(afterMembership.getStatus());
        history.setAfterExpireTime(afterMembership.getExpireTime());


        history.setOperator(operator);
        history.setInviteUsername(inviteUsername);
        history.setRemark(remark);

        // 设置创建和更新时间
        LocalDateTime now = LocalDateTime.now();
        history.setCreateTime(now);
        history.setUpdateTime(now);

        return save(history);
    }

    @Override
    public boolean recordActivation(
            UserMembershipEntity membership,
            Long orderId,
            Integer operationType,
            Integer source,
            String operator,
            String inviteUsername,
            String remark
    ) {
        // 创建一个虚拟的前状态，表示用户之前是非会员
        UserMembershipEntity beforeMembership = new UserMembershipEntity();
        beforeMembership.setUsername(membership.getUsername());
        beforeMembership.setStatus(0); // 非会员
        beforeMembership.setExpireTime(null);

        return recordMembershipHistory(
                beforeMembership,
                membership,
                operationType,
                orderId,
                operator,
                inviteUsername,
                remark
        );
    }

    @Override
    public boolean recordExpiration(UserMembershipEntity membership) {
        // 创建一个会员过期后的状态
        UserMembershipEntity afterMembership = new UserMembershipEntity();
        afterMembership.setUsername(membership.getUsername());
        afterMembership.setPackageId(membership.getPackageId());
        afterMembership.setStatus(0); // 非会员
        afterMembership.setExpireTime(membership.getExpireTime());

        return recordMembershipHistory(
                membership,
                afterMembership,
                OperationTypeConstants.TYPE_EXPIRED, // 过期
                null,
                "system",
                null,
                "会员已过期"
        );
    }

    @Override
    public List<UserMembershipHistoryEntity> getUserMembershipHistory(String username) {
        LambdaQueryWrapper<UserMembershipHistoryEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserMembershipHistoryEntity::getUsername, username)
                .orderByDesc(UserMembershipHistoryEntity::getCreateTime);
        return list(queryWrapper);
    }

    @Override
    public PageInfo<UserMembershipHistoryEntity> getUserMembershipHistoryPage(String username, int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);

        LambdaQueryWrapper<UserMembershipHistoryEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserMembershipHistoryEntity::getUsername, username)
                .orderByDesc(UserMembershipHistoryEntity::getCreateTime);

        List<UserMembershipHistoryEntity> list = list(queryWrapper);
        return new PageInfo<>(list);
    }
} 