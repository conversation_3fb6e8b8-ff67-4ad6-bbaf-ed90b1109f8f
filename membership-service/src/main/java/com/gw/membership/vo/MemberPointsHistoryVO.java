package com.gw.membership.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 会员积分历史记录VO
 */
@Data
@Schema(description = "会员积分历史记录")
public class MemberPointsHistoryVO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "变更类型: 1-增加, 2-消费")
    private Integer changeType;

    @Schema(description = "变更前积分")
    private Integer pointsBefore;

    @Schema(description = "变更后积分")
    private Integer pointsAfter;

    @Schema(description = "变更积分数量")
    private Integer pointsChange;

    @Schema(description = "变更原因")
    private String reason;

    @Schema(description = "操作时间")
    private LocalDateTime operateTime;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}
