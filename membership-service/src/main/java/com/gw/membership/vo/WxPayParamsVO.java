package com.gw.membership.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * 微信支付参数响应VO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "微信支付参数响应")
public class WxPayParamsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 小程序ID
     */
    @Schema(description = "小程序ID")
    private String appId;

    /**
     * 时间戳
     */
    @Schema(description = "时间戳")
    private String timeStamp;

    /**
     * 随机字符串
     */
    @Schema(description = "随机字符串")
    private String nonceStr;

    /**
     * 订单详情扩展字符串
     */
    @Schema(description = "订单详情扩展字符串")
    private String packageValue;

    /**
     * 签名方式
     */
    @Schema(description = "签名方式")
    private String signType;

    /**
     * 签名
     */
    @Schema(description = "签名")
    private String paySign;

    /**
     * 二维码链接
     */
    private String qrCodeUrl;

    /**
     * 通过Map构造支付参数
     */
    public WxPayParamsVO(Map<String, Object> params) {
        if (params != null) {
            this.appId = (String) params.get("appId");
            this.timeStamp = (String) params.get("timeStamp");
            this.nonceStr = (String) params.get("nonceStr");
            this.packageValue = (String) params.get("package");
            this.signType = (String) params.get("signType");
            this.paySign = (String) params.get("paySign");
            this.qrCodeUrl = (String) params.get("qrCodeUrl");
        }
    }
} 