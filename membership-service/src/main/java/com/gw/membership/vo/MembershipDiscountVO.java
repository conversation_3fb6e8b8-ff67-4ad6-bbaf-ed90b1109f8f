package com.gw.membership.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员折扣DTO
 */
@Data
public class MembershipDiscountVO {

    private Long id;

    /**
     * 折扣名称
     */
    private String name;

    /**
     * 折扣类型: 1-固定金额, 2-百分比
     */
    private Integer discountType;

    /**
     * 折扣值（固定金额或百分比）
     */
    private BigDecimal discountValue;

    /**
     * 最低订单金额
     */
    private BigDecimal minOrderAmount;

    /**
     * 折扣开始时间
     */
    private LocalDateTime startTime;

    /**
     * 折扣结束时间
     */
    private LocalDateTime endTime;

    /**
     * 折扣状态: 0-禁用, 1-启用
     */
    private Integer status;

    /**
     * 适用的套餐ID列表, 逗号分隔, 为空表示适用所有套餐
     */
    private String applyToPackageIds;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
} 