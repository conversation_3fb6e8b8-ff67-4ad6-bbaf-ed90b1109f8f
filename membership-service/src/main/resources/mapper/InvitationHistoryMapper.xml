<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gw.membership.mapper.InvitationHistoryMapper">
    <!-- 基础映射结果集 -->
    <resultMap id="BaseResultMap" type="com.gw.membership.entity.InvitationHistoryEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="inviter" jdbcType="VARCHAR" property="inviter"/>
        <result column="invitee" jdbcType="VARCHAR" property="invitee"/>
        <result column="invitation_code" jdbcType="VARCHAR" property="invitationCode"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <!-- 分页查询邀请记录列表 -->
    <select id="page" resultMap="BaseResultMap">
        SELECT * FROM t_invitation_history
        <where>
            deleted = 0
            <if test="inviter != null and inviter != ''">
                AND inviter = #{inviter}
            </if>
            <if test="invitee != null and invitee != ''">
                AND invitee = #{invitee}
            </if>
            <if test="startTime != null">
                AND create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
</mapper> 